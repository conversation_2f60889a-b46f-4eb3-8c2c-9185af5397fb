# System Patterns *Optional*

This file documents recurring patterns and standards used in the project.
It is optional, but recommended to be updated as the project evolves.
2025-08-04 01:18:42 - Log of updates made.

*

## Coding Patterns

*   

## Architectural Patterns

* **分层架构与 JSON 数据总线**:
  * **模式**: 系统被清晰地分为三层：Python 表示/逻辑层、C++ 核心模拟层，以及一个作为数据总线的 JSON 适配器接口。
    *   **Python 层**: 负责用户界面、团队/赛事管理和状态持久化。
    *   **JSON 接口**: Python 将所有需要模拟的数据（如锦标赛结构、参赛者属性）序列化为 JSON 字符串。
    *   **C++ 层**: 接收 JSON 字符串，使用核心算法进行密集计算，然后将结果序列化回 JSON 字符串并返回。
  * **优点**:
    *   **关注点分离**: Python 和 C++ 各司其职，Python 负责高级逻辑，C++ 负责高性能计算。
    *   **强类型解耦**: 避免了 `ctypes` 在处理复杂嵌套结构时的脆弱性。数据契约由 JSON 结构定义，而不是 C 结构体的内存布局。
    *   **可扩展性**: 添加新功能时，只需扩展 JSON 契约和处理逻辑，而无需更改接口函数签名。
  * **架构图**:
    ```mermaid
    graph TD
        subgraph Python Application Layer
            A[UI Controller] --> B{Team/Tournament Manager};
            B --> C{Data Persistence (JSON/DB)};
        end

        subgraph C++ Core Simulation Layer
            G[Race Logic] --> H{Attribute Modifiers};
            H --> I[Core Simulator - CalculateRaceResult];
        end

        subgraph Adapter Interface
            D(JSON Serializer) -- Tournament Data --> E[RunTournament() DLL Call];
            F(JSON Deserializer) -- Race Results --- E;
        end

        B -- Build Tournament Data --> D;
        E -- Calls --> G;
        F -- Update UI/State --> A;

        style A fill:#cde,stroke:#333,stroke-width:2px;
        style B fill:#cde,stroke:#333,stroke-width:2px;
        style C fill:#cde,stroke:#333,stroke-width:2px;
        style G fill:#f9f,stroke:#333,stroke-width:2px;
        style H fill:#f9f,stroke:#333,stroke-width:2px;
        style I fill:#f9f,stroke:#333,stroke-width:2px;
        style E fill:#fcf,stroke:#f0f,stroke-width:4px;
    ```

## Testing Patterns

* **命令行测试工具 (`basic_test.exe`)**:
  * **模式**: 使用一个独立的C++可执行文件来调用核心DLL的功能，用于进行快速的、可重复的集成测试和场景验证。
  * **优点**:
    * **快速验证**: 无需完整的客户端环境即可测试核心逻辑。
    * **批量测试**: 支持运行大量模拟以收集统计数据，便于性能分析和平衡性调整（例如`runCompetition`功能）。
    * **回归测试**: 可轻松集成到自动化构建或测试脚本中。
  * **适用场景**:
    * 当核心逻辑被封装在库（如DLL）中时。
    * 需要对算法进行调优和平衡性验证。
    * 需要一个轻量级的方式来演示和复现特定的功能场景。

* **Windows 控制台 Unicode (UTF-8) 支持**:
 * **模式**: 在应用程序启动时，通过调用 Windows API `SetConsoleOutputCP` 和 `SetConsoleCP` 将控制台的输入输出代码页均设置为 `65001` (UTF-8)，并同步更新 C++ 的 `std::locale`。
 * **优点**:
   * **一致性**: 确保程序内部处理、输入和输出的编码一致，从根本上解决乱码问题。
   * **可移植性**: 代码使用 `#ifdef _WIN32` 进行了平台隔离，不会影响非 Windows平台的编译。
   * **标准化**: 使用 UTF-8 是现代软件开发的标准实践。
 * **适用场景**:
   * 任何需要在 Windows 控制台中显示非 ASCII 字符（如中文、日文、特殊符号）的 C++ 应用程序。
   * 需要处理来自用户的多语言输入的控制台程序。

* **游戏循环与显示器刷新率同步**:
 * **模式**: 使用 `EnumDisplaySettingsW` API 获取主显示器的刷新率，计算出每帧的目标时长。在游戏主循环的末尾，通过高精度时钟计算当帧已用时间，并使用 `std::this_thread::sleep_for` 休眠剩余时间，以将 FPS 同步到显示器的刷新率。
 * **优点**:
   * **流畅视觉**: 避免画面撕裂，提供更平滑的动画效果。
   * **性能优化**: 在渲染速度超过刷新率时，通过休眠避免不必要的 CPU 资源消耗，降低功耗和发热。
   * **稳定性**: 提供一个稳定的、可预测的更新频率，有助于物理和动画计算的稳定性。
 * **适用场景**:
   * 实时渲染的游戏或模拟器，尤其是那些有大量动画的程序。
   * 任何需要以固定频率更新和渲染的图形应用程序。