# ASCII Running Simulation - Technical Specification

## Problem Statement

- **Business Issue**: Need a real-time ASCII character-based running race simulation that integrates with existing race_simulator.dll to provide visual race progress and final results
- **Current State**: No visual interface exists for the race simulator - only DLL function calls and raw data output
- **Expected Outcome**: A Windows console application that displays an 800x600 ASCII interface showing runners moving across a track in real-time at 30 FPS, with race results displayed upon completion

## Solution Overview

- **Approach**: Create a lightweight C++ console application that uses Windows console APIs for terminal control, loads race_simulator.dll via ctypes, and renders real-time ASCII animation of runners on a track
- **Core Changes**: Implement Windows console manipulation for 800x600 display, integrate DLL race engine for simulation logic, create ASCII animation engine for character-based movement, and build complete race workflow from start to finish
- **Success Criteria**: Application displays 30 FPS ASCII animation of runners moving left-to-right across '-' character track, shows real-time positions, displays final race results, and exits cleanly after completion

## Technical Implementation

### Database Changes
- **No database required** - state maintained in memory via DLL interface

### Code Changes

#### Files to Modify
- **New Files Only** - complete standalone implementation

#### New Files Structure
```
src/
├── ascii_simulator.cpp      # Main application entry point
├── race_engine.h           # DLL interface wrapper
├── animation_engine.h      # ASCII rendering and animation
├── console_utils.h         # Windows console control utilities
└── race_state.h           # Race data structures and state
```

#### Function Signatures

**race_engine.h**:
```cpp
class RaceEngine {
public:
    bool InitializeDLL(const std::string& dll_path);
    bool StartRace(int num_runners, const std::vector<std::string>& runner_names);
    bool UpdateRace(float delta_time);
    bool IsRaceComplete();
    std::vector<RunnerState> GetRunnerPositions();
    RaceResults GetRaceResults();
    void Cleanup();
};
```

**animation_engine.h**:
```cpp
class AnimationEngine {
public:
    void Initialize(int screen_width, int screen_height);
    void ClearScreen();
    void DrawTrack(int track_length, int track_y_position);
    void DrawRunners(const std::vector<RunnerState>& runners, int track_y_position);
    void DrawResults(const RaceResults& results);
    void SetCursorPosition(int x, int y);
    void RenderFrame();
    void Shutdown();
};
```

**console_utils.h**:
```cpp
namespace ConsoleUtils {
    void SetupConsole(int width, int height);
    void SetConsoleSize(int width, int height);
    void HideCursor();
    void ShowCursor();
    void ClearScreen();
    void SetCursorPosition(int x, int y);
    void DisableQuickEdit();
    COORD GetConsoleSize();
    bool IsConsoleValidSize(int width, int height);
}
```

**race_state.h**:
```cpp
struct RunnerState {
    char identifier;        // 'A', 'B', 'C', etc.
    float position;         // 0.0 to 100.0 (percentage of track)
    float speed;           // current speed
    std::string name;      // runner name
};

struct RaceResults {
    std::vector<std::pair<char, std::string>> finish_order;  // identifier + name
    std::vector<float> finish_times;                         // corresponding times
    bool is_complete;
};
```

### API Changes
- **No REST API** - Direct DLL function calls via ctypes

### DLL Integration Strategy

**DLL Function Mapping** (via ctypes):
```cpp
// Required DLL functions
extern "C" {
    typedef void* (*CreateRaceSimulatorFunc)();
    typedef bool (*InitializeRaceFunc)(void* simulator, int num_runners, const char** names);
    typedef bool (*UpdateRaceFunc)(void* simulator, float delta_time);
    typedef bool (*IsRaceCompleteFunc)(void* simulator);
    typedef void (*GetRaceResultsFunc)(void* simulator, float* times, int* positions);
    typedef void (*DestroyRaceSimulatorFunc)(void* simulator);
}
```

### Configuration Changes
- **No configuration files** - hardcoded parameters for simplicity
- **Environment Variables**: None required
- **Feature Flags**: None - single mode operation

## Implementation Sequence

### Phase 1: Console Setup and Utilities
**Files**: `console_utils.h`, `console_utils.cpp`
- Implement Windows console initialization for 800x600 display
- Create cursor control and screen clearing functions
- Add console validation and error handling
- Test standalone console manipulation

### Phase 2: DLL Integration Layer
**Files**: `race_engine.h`, `race_engine.cpp`
- Implement DLL loading and function mapping
- Create wrapper for race simulation functions
- Handle DLL error cases and graceful degradation
- Test DLL integration without animation

### Phase 3: ASCII Animation Engine
**Files**: `animation_engine.h`, `animation_engine.cpp`
- Implement track drawing with '-' characters
- Create runner character rendering (A, B, C...)
- Add 30 FPS timing control using Windows high-resolution timer
- Implement position interpolation for smooth movement

### Phase 4: Main Application Integration
**Files**: `ascii_simulator.cpp`
- Combine all components into complete workflow
- Implement race initialization sequence
- Add real-time update loop with 30 FPS target
- Create final results display with formatting

### Phase 5: Build System and Testing
**Files**: `CMakeLists.txt`, `Makefile`
- Create Windows-specific build configuration
- Add DLL dependency handling
- Implement basic error checking and validation

## Validation Plan

### Unit Tests
- **ConsoleUtils Tests**:
  - Console initialization with valid dimensions
  - Cursor positioning accuracy
  - Screen clearing functionality
  - Error handling for invalid console sizes

- **RaceEngine Tests**:
  - DLL loading success/failure cases
  - Race initialization with various runner counts
  - Position updates over time
  - Race completion detection

- **AnimationEngine Tests**:
  - Track rendering at correct position
  - Runner character placement accuracy
  - Frame timing consistency (30 FPS)
  - Results display formatting

### Integration Tests
- **End-to-End Race Simulation**:
  - Complete race from initialization to results
  - Multiple runner scenarios (3-8 runners)
  - Performance under 30 FPS load
  - Memory leak detection

- **DLL Integration**:
  - Verify ctypes function mapping accuracy
  - Test race data flow from DLL to display
  - Validate position interpolation
  - Confirm race completion detection

### Business Logic Verification
- **Visual Accuracy**:
  - Runners appear as letters A, B, C... in order
  - Track displays as continuous line of '-' characters
  - Movement is smooth and left-to-right only
  - Final positions match DLL results exactly

- **Performance Metrics**:
  - Consistent 30 FPS during race animation
  - No screen flickering or tearing
  - Immediate results display after race completion
  - Clean application exit

## Additional Technical Details

### Memory Management
- All DLL handles properly closed
- No memory leaks in animation buffers
- RAII patterns for resource management

### Error Handling
- Graceful DLL loading failure
- Console size validation on startup
- Clear error messages to stderr
- Non-zero exit codes for failures

### Performance Considerations
- Minimal console I/O per frame
- Double buffering not required for ASCII
- Optimized character rendering
- Windows high-resolution timer for accurate FPS

### Build Dependencies
- **Windows SDK** for console APIs
- **Standard C++ Library** only
- **No external dependencies** beyond race_simulator.dll
- **Static linking** preferred for portability