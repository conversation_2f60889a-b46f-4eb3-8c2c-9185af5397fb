#include <iostream>
#include "include/console_utils.h"

int main() {
    std::cout << "Testing ConsoleUtils refresh rate detection" << std::endl;
    std::cout << "===========================================" << std::endl;
    
    ConsoleUtils console;
    console.setupUtf8Console();
    
    int refreshRate = console.getDisplayRefreshRate();
    double frameTime = 1.0 / refreshRate;
    
    std::cout << "Detected refresh rate: " << refreshRate << " Hz" << std::endl;
    std::cout << "Frame time: " << (frameTime * 1000.0) << " ms" << std::endl;
    std::cout << "Target FPS: " << refreshRate << std::endl;
    
    std::cout << "\nPress any key to exit..." << std::endl;
    console.getKey();
    
    return 0;
}
