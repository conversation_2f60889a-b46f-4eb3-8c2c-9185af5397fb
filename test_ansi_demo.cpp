#include <iostream>
#include <chrono>
#include <thread>
#include "include/console_utils.h"

int main() {
    ConsoleUtils console;
    console.setupUtf8Console();
    
    std::cout << "ANSI Escape Sequence Demo - Flicker-Free Animation" << std::endl;
    std::cout << "=================================================" << std::endl;
    std::cout << "Watch the smooth animation without any flickering!" << std::endl;
    std::cout << "Press Ctrl+C to exit" << std::endl;
    std::cout << std::endl;
    
    // Hide cursor for better visual experience
    console.hideCursorANSI();
    
    int position = 0;
    int direction = 1;
    const int trackWidth = 60;
    
    for (int frame = 0; frame < 300; frame++) {
        // Move to the animation line
        console.setCursorPositionANSI(0, 6);
        
        // Draw the track
        std::cout << "Track: ";
        for (int i = 0; i < trackWidth; i++) {
            if (i == position) {
                std::cout << "O";  // Moving object
            } else if (i == 0 || i == trackWidth - 1) {
                std::cout << "|";  // Track boundaries
            } else {
                std::cout << "-";  // Track
            }
        }
        
        // Clear rest of line to avoid artifacts
        console.clearFromCursorANSI();
        
        // Update position
        position += direction;
        if (position >= trackWidth - 1 || position <= 0) {
            direction = -direction;
        }
        
        // Show frame info
        console.setCursorPositionANSI(0, 8);
        std::cout << "Frame: " << frame << " | Position: " << position << " | 60 FPS";
        console.clearFromCursorANSI();
        
        // Wait for next frame (60 FPS)
        std::this_thread::sleep_for(std::chrono::milliseconds(16));
    }
    
    // Show cursor again
    console.showCursorANSI();
    
    console.setCursorPositionANSI(0, 10);
    std::cout << "Demo completed! Notice how smooth it was without any flickering." << std::endl;
    std::cout << "Press any key to exit..." << std::endl;
    console.getKey();
    
    return 0;
}
