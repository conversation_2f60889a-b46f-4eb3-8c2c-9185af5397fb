# Product Context

This file provides a high-level overview of the project and the expected product that will be created. Initially it is based upon projectBrief.md (if provided) and all other available project-related information in the working directory. This file is intended to be updated as the project evolves, and should be used to inform all other modes of the project's goals and context.
2025-08-04 01:18:02 - Log of updates made will be appended as footnotes to the end of this file.

*

## Project Goal

*   

## Key Features

*   **团队管理**: 支持创建、查看、更新和删除 (CRUD) 选手和团队。
*   **赛事系统**: 允许玩家参与多阶段的锦标赛，并根据比赛结果影响选手状态。
*   **DLL 核心模拟**: 所有比赛逻辑都由后端的 C++ DLL (`race_simulator.dll`) 驱动。

## 核心数据模型

### 团队与选手

*   **Team**: 代表玩家的队伍。
    *   `id`: 唯一标识符。
    *   `name`: 团队名称。
    *   `players`: `Player` 对象的列表。

*   **Player**: 代表一个可参赛的选手。
    *   `id`: 唯一标识符。
    *   `name`: 选手名称。
    *   `role_id`: 关联的 `Role` ID。
    *   `attributes`: 选手的核心比赛属性，其结构与 C++ `RunnerAttributes` 匹配。
        *   `mood`, `stamina`, `speed`, `power`, `endurance`, `intelligence`, `body`, `adaptability`, `hasMidRaceAccel`, `hasStartWisdom`, `hasLateRacePower`
    *   `stamina_current`: 选手的当前体力。比赛会消耗体力，需要时间恢复。

*   **Role**: 定义一类选手的原型和成长潜力。
    *   `id`: 唯一标识符。
    *   `name`: 角色名称 (例如, "速度型", "耐力型")。
    *   `base_attributes`: 创建该类型选手时的基础属性范围。
    
    ### 赛事系统
    
    *   **Tournament**: 代表一个完整的锦标赛。
        *   `id`: 唯一标识符。
        *   `name`: 锦标赛名称。
        *   `stages`: `Stage` 对象的有序列表。
        *   `entry_requirements`: 参赛要求。
    
    *   **Stage**: 锦标赛的一个赛段。
        *   `id`: 唯一标识符。
        *   `name`: 赛段名称 (例如, "资格赛", "决赛")。
        *   `matches`: `Match` 对象的列表。
        *   `progression_rules`: 定义晋级到下一赛段的规则。
    
    *   **Match**: 一场具体的比赛。
        *   `id`: 唯一标识符。
        *   `participants`: 参赛 `Player` ID 的列表。
        *   `environment`: 定义比赛环境，其结构与 C++ `Environment` 匹配。
        *   `result`: 比赛结果，包括排名和时间。
        *   `stamina_cost`: 比赛消耗的体力。

## Overall Architecture

*   