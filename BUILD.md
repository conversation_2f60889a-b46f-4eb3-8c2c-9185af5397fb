# ASCII Running Simulation - Build Instructions

## Overview
This is a complete C++ ASCII character-based running race simulation system with real-time animation at 30 FPS.

## Features
- Real-time ASCII character animation at 30 FPS
- 800x600 console optimization for Windows
- DLL integration ready for race simulation
- 6 runners competing in 100m races
- Interactive menu system
- Cross-platform compatible (Windows primary)

## Build Requirements

### Windows (Primary)
- **MinGW-w64 GCC** (recommended)
- **Visual Studio 2019+** (MSVC)
- **CMake 3.15+** (optional)

### MinGW-w64 Installation
1. Download from: https://sourceforge.net/projects/mingw-w64/
2. Add to PATH: `C:\mingw-w64\bin`
3. Verify: `g++ --version`

## Build Methods

### Method 1: Build Script (Windows)
```cmd
build.bat
```

### Method 2: Makefile (MinGW)
```cmd
make all
make run
```

### Method 3: CMake (Cross-platform)
```cmd
mkdir build
cd build
cmake ..
cmake --build . --config Release
```

### Method 4: Direct Compilation
```cmd
g++ -std=c++17 -Wall -Wextra -O2 src/*.cpp -o bin/ascii_runner.exe -static -lkernel32 -luser32 -lgdi32 -lwinmm
```

## Usage

### Controls
- **SPACE**: Start race / Continue
- **R**: Reset to main menu
- **ESC**: Exit

### Running the Simulation
```cmd
bin\ascii_runner.exe
```

## DLL Integration

The system looks for `race_simulation.dll` in the current directory. If not found, it uses built-in simulation.

### Expected DLL Interface
```cpp
bool InitializeRace(double distance, int runnerCount, 
                   const char** runnerNames, const double* speeds, 
                   const double* endurances);
bool UpdateRace(double deltaTime, double* positions, double* times, 
               bool* finished, int* places);
bool GetRaceInfo(double* elapsedTime, bool* raceFinished, 
                char* winnerName, int nameBufferSize);
void ResetRace();
```

## Directory Structure
```
sim/
├── src/                    # Source code
│   ├── console_utils.h/cpp  # Windows console control
│   ├── race_engine.h/cpp    # DLL integration
│   ├── animation_engine.h/cpp # ASCII animation
│   └── main.cpp            # Entry point
├── bin/                    # Executables
├── obj/                    # Object files
├── build.bat              # Windows build script
├── Makefile               # MinGW make
└── CMakeLists.txt         # CMake configuration
```

## Troubleshooting

### Common Issues

1. **"g++ not found"**
   - Install MinGW-w64 and add to PATH

2. **Console size issues**
   - Run in Windows Terminal or CMD
   - Ensure 800x600 resolution support

3. **DLL loading fails**
   - Place DLL in same directory as executable
   - Check DLL architecture (32/64-bit)

4. **Build errors**
   - Use: `g++ --version` to verify compiler
   - Try: `build.bat clean` then rebuild

### Performance Tips
- Use Windows Terminal for better font rendering
- Ensure console font is set to "Consolas" or "Courier New"
- Maximize console window for best experience

## Development

### Adding Features
1. Modify `RaceEngine` for new physics
2. Update `AnimationEngine` for new visuals
3. Extend `AsciiSimulator` for new interactions

### Testing
- Built-in simulation provides fallback
- Test DLL integration separately
- Verify 30 FPS performance on target hardware