#include "ascii_simulator.h"
#include "race_engine.h"
#include "animation_engine.h"
#include "console_utils.h"
#include <iostream>
#include <vector>
#include <algorithm>

AsciiSimulator::AsciiSimulator() : isRunning(false), raceInProgress(false) {
}

AsciiSimulator::~AsciiSimulator() {
    shutdown();
}

bool AsciiSimulator::initialize() {
    console = std::make_unique<ConsoleUtils>();
    raceEngine = std::make_unique<RaceEngine>();
    animationEngine = std::make_unique<AnimationEngine>();
    
    if (!console->initialize()) {
        std::cerr << "Failed to initialize console utilities" << std::endl;
        return false;
    }
    
    if (!raceEngine->initialize()) {
        std::cerr << "Failed to initialize race engine" << std::endl;
        return false;
    }
    
    if (!animationEngine->initialize(80, 25)) {
        std::cerr << "Failed to initialize animation engine" << std::endl;
        return false;
    }
    
    // Setup console
    console->setConsoleSize(80, 25);
    console->setConsoleTitle("ASCII Running Race Simulator");
    console->setCursorVisibility(false);
    console->clearScreen();
    
    // Load DLL
    if (!raceEngine->loadRaceDLL("RaceSimulation.dll")) {
        std::cerr << "Failed to load RaceSimulation.dll" << std::endl;
        return false;
    }
    
    return true;
}

void AsciiSimulator::run() {
    isRunning = true;
    
    // Setup default runners
    std::vector<std::string> runners = {
        "Flash", "Sonic", "Bolt", "Speedy"
    };
    
    if (!raceEngine->setupRace(runners)) {
        std::cerr << "Failed to setup race: " << raceEngine->getLastError() << std::endl;
        return;
    }
    
    animationEngine->setRaceDistance(100.0f);
    animationEngine->setTrackLength(60);
    animationEngine->setTargetFPS(30);
    
    while (isRunning) {
        processInput();
        update();
        render();
        
        animationEngine->waitForNextFrame();
    }
}

void AsciiSimulator::shutdown() {
    if (raceEngine) {
        raceEngine->shutdown();
    }
    
    if (console) {
        console->setCursorVisibility(true);
        console->shutdown();
    }
}

void AsciiSimulator::processInput() {
    if (console->keyPressed()) {
        int key = console->getKey();
        
        switch (key) {
            case ' ': // Space - Start/Pause race
                if (!raceInProgress) {
                    startRace();
                }
                break;
            case 'r':
            case 'R': // Reset race
                resetRace();
                break;
            case 'q':
            case 'Q': // Quit
                isRunning = false;
                break;
            case 27: // Escape
                isRunning = false;
                break;
        }
    }
}

void AsciiSimulator::update() {
    if (raceInProgress) {
        raceEngine->updateRace();
        
        if (raceEngine->isRaceFinished()) {
            raceInProgress = false;
            displayResults();
        }
    }
}

void AsciiSimulator::render() {
    console->clearScreen();
    
    if (!raceInProgress && !raceEngine->isRaceFinished()) {
        displayMenu();
    } else {
        displayRace();
    }
}

void AsciiSimulator::startRace() {
    if (raceEngine->startRace()) {
        raceInProgress = true;
    } else {
        std::cerr << "Failed to start race: " << raceEngine->getLastError() << std::endl;
    }
}

void AsciiSimulator::resetRace() {
    raceInProgress = false;
    
    // Reset the race
    std::vector<std::string> runners = {
        "Flash", "Sonic", "Bolt", "Speedy"
    };
    
    if (!raceEngine->setupRace(runners)) {
        std::cerr << "Failed to reset race: " << raceEngine->getLastError() << std::endl;
    }
}

void AsciiSimulator::displayMenu() {
    console->setCursorPosition(10, 5);
    std::cout << "ASCII RUNNING RACE SIMULATOR";
    
    console->setCursorPosition(10, 7);
    std::cout << "=============================";
    
    console->setCursorPosition(5, 10);
    std::cout << "Runners:";
    
    auto runners = raceEngine->getRunnerInfo();
    for (size_t i = 0; i < runners.size(); ++i) {
        console->setCursorPosition(5, 12 + static_cast<int>(i));
        std::cout << "  " << (i + 1) << ". " << runners[i].name;
    }
    
    console->setCursorPosition(5, 18);
    std::cout << "Controls:";
    console->setCursorPosition(5, 19);
    std::cout << "  SPACE - Start Race";
    console->setCursorPosition(5, 20);
    std::cout << "  R     - Reset Race";
    console->setCursorPosition(5, 21);
    std::cout << "  Q     - Quit";
}

void AsciiSimulator::displayRace() {
    auto runners = raceEngine->getRunnerInfo();
    
    // Draw track
    console->setCursorPosition(0, 0);
    
    const int trackStartY = 3;
    const int trackHeight = 3;
    const int trackLength = 60;
    const int trackStartX = 5;
    
    // Clear previous frame
    for (int i = 0; i < 25; ++i) {
        console->setCursorPosition(0, i);
        std::cout << std::string(80, ' ');
    }
    
    // Draw title
    console->setCursorPosition(25, 0);
    std::cout << "ASCII RUNNING RACE SIMULATOR";
    
    // Draw track for each runner
    for (size_t lane = 0; lane < runners.size() && lane < 4; ++lane) {
        int laneY = trackStartY + static_cast<int>(lane) * trackHeight;
        
        // Draw lane
        console->setCursorPosition(0, laneY);
        std::cout << "Lane " << (lane + 1) << ": ";
        
        // Draw track
        console->setCursorPosition(trackStartX, laneY);
        std::cout << "|" << std::string(trackLength - 2, '-') << "|FINISH";
        
        // Draw runner
        float progress = runners[lane].distance / 100.0f;
        int runnerPos = trackStartX + static_cast<int>(progress * (trackLength - 10));
        
        console->setCursorPosition(runnerPos, laneY);
        
        if (runners[lane].finished) {
            std::cout << "\o/";
        } else {
            static const char* sprites[] = {" o-", " O-", " o=", " O="};
            static int frame = 0;
            std::cout << sprites[frame++ % 4];
        }
        
        // Draw runner name
        console->setCursorPosition(runnerPos - static_cast<int>(runners[lane].name.length()) - 1, laneY);
        std::cout << runners[lane].name.substr(0, 8);
    }
    
    // Draw status
    console->setCursorPosition(0, 15);
    std::cout << "Race Status:";
    
    for (size_t i = 0; i < runners.size(); ++i) {
        console->setCursorPosition(2, 16 + static_cast<int>(i));
        float progress = (runners[i].distance / 100.0f) * 100.0f;
        std::cout << runners[i].name.substr(0, 8) << ": " << std::fixed << std::setprecision(1) << progress << "%";
        
        if (runners[i].finished) {
            std::cout << " (Finished: " << runners[i].finishTime << "s)";
        }
    }
    
    // Draw controls
    console->setCursorPosition(0, 22);
    std::cout << "Controls: SPACE - Start/Pause, R - Reset, Q - Quit";
}

void AsciiSimulator::displayResults() {
    auto results = raceEngine->getRaceResults();
    
    console->clearScreen();
    
    console->setCursorPosition(30, 5);
    std::cout << "RACE RESULTS";
    console->setCursorPosition(30, 6);
    std::cout << "============";
    
    console->setCursorPosition(25, 8);
    std::cout << "Position  Runner      Time";
    console->setCursorPosition(25, 9);
    std::cout << "--------  ------      ----";
    
    for (size_t i = 0; i < results.size() && i < 4; ++i) {
        console->setCursorPosition(25, 11 + static_cast<int>(i));
        std::cout << "   " << (i + 1) << "      " << std::setw(8) << std::left << results[i].name.substr(0, 8) << " " << std::fixed << std::setprecision(2) << results[i].finishTime << "s";
    }
    
    console->setCursorPosition(25, 18);
    std::cout << "Press R to restart or Q to quit";
}