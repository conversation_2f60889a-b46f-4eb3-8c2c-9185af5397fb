#include "../include/console_utils.h"
#include <iostream>
#include <conio.h>
#include <locale>

// This is the new function to be called from main.cpp
void setup_utf8_console() {
#ifdef _WIN32
    // Set console output code page to UTF-8
    if (SetConsoleOutputCP(CP_UTF8) == 0) {
        std::cerr << "Error: Unable to set console output code page to UTF-8." << std::endl;
    }
    // Set console input code page to UTF-8
    if (SetConsoleCP(CP_UTF8) == 0) {
        std::cerr << "Error: Unable to set console input code page to UTF-8." << std::endl;
    }
    // Set the C++ locale to system default to ensure C++ streams work correctly
    try {
        std::locale::global(std::locale(""));
    } catch (const std::runtime_error& e) {
        std::cerr << "Error: Unable to set C++ locale. " << e.what() << std::endl;
    }
#endif
}


ConsoleUtils::ConsoleUtils() : consoleHandle(GetStdHandle(STD_OUTPUT_HANDLE)),
                             inputHandle(GetStdHandle(STD_INPUT_HANDLE)),
                             consoleWidth(80), consoleHeight(25), defaultAttributes(7)
{
    if (consoleHandle == INVALID_HANDLE_VALUE || inputHandle == INVALID_HANDLE_VALUE) {
        // In a real application, you might throw an exception here
        return;
    }
    getDefaultAttributes();
    getConsoleSize(consoleWidth, consoleHeight);
    enableVirtualTerminalProcessing();
    disableQuickEditMode();
}

ConsoleUtils::~ConsoleUtils() {
    // Reset attributes or other cleanup if necessary
}


void ConsoleUtils::setConsoleSize(int width, int height) {
    if (consoleHandle == INVALID_HANDLE_VALUE) return;
    
    SMALL_RECT windowSize = {0, 0, static_cast<SHORT>(width - 1), static_cast<SHORT>(height - 1)};
    COORD bufferSize = {static_cast<SHORT>(width), static_cast<SHORT>(height)};
    
    SetConsoleWindowInfo(consoleHandle, TRUE, &windowSize);
    SetConsoleScreenBufferSize(consoleHandle, bufferSize);
    
    consoleWidth = width;
    consoleHeight = height;
}

void ConsoleUtils::setConsoleTitle(const std::string& title) {
    SetConsoleTitleA(title.c_str());
}

void ConsoleUtils::setCursorPosition(int x, int y) {
    if (consoleHandle == INVALID_HANDLE_VALUE) return;
    
    COORD position = {static_cast<SHORT>(x), static_cast<SHORT>(y)};
    SetConsoleCursorPosition(consoleHandle, position);
}

void ConsoleUtils::setCursorVisibility(bool visible) {
    if (consoleHandle == INVALID_HANDLE_VALUE) return;
    
    CONSOLE_CURSOR_INFO cursorInfo;
    GetConsoleCursorInfo(consoleHandle, &cursorInfo);
    cursorInfo.bVisible = visible;
    SetConsoleCursorInfo(consoleHandle, &cursorInfo);
}

void ConsoleUtils::clearScreen() {
    if (consoleHandle == INVALID_HANDLE_VALUE) return;
    
    COORD topLeft = {0, 0};
    DWORD written;
    CONSOLE_SCREEN_BUFFER_INFO screenInfo;
    
    GetConsoleScreenBufferInfo(consoleHandle, &screenInfo);
    DWORD consoleSize = screenInfo.dwSize.X * screenInfo.dwSize.Y;
    
    FillConsoleOutputCharacterA(consoleHandle, ' ', consoleSize, topLeft, &written);
    FillConsoleOutputAttribute(consoleHandle, defaultAttributes, consoleSize, topLeft, &written);
    
    setCursorPosition(0, 0);
}

void ConsoleUtils::setTextColor(WORD color) {
    if (consoleHandle == INVALID_HANDLE_VALUE) return;
    SetConsoleTextAttribute(consoleHandle, color);
}

void ConsoleUtils::resetTextColor() {
    if (consoleHandle == INVALID_HANDLE_VALUE) return;
    SetConsoleTextAttribute(consoleHandle, defaultAttributes);
}



void ConsoleUtils::setConsoleBufferSize(int width, int height) {
    if (consoleHandle == INVALID_HANDLE_VALUE) return;
    
    COORD bufferSize = {static_cast<SHORT>(width), static_cast<SHORT>(height)};
    SetConsoleScreenBufferSize(consoleHandle, bufferSize);
}

int ConsoleUtils::getConsoleWidth() const {
    return consoleWidth;
}

int ConsoleUtils::getConsoleHeight() const {
    return consoleHeight;
}

void ConsoleUtils::flushInputBuffer() {
    if (inputHandle == INVALID_HANDLE_VALUE) return;
    FlushConsoleInputBuffer(inputHandle);
}

bool ConsoleUtils::keyPressed() const {
    return _kbhit() != 0;
}

int ConsoleUtils::getKey() const {
    return _getch();
}

void ConsoleUtils::sleep(int milliseconds) {
    ::Sleep(milliseconds);
}

int ConsoleUtils::getDisplayRefreshRate() const {
    // Get the primary display device context
    HDC hdc = GetDC(NULL);
    if (hdc == NULL) {
        return 60; // Default fallback
    }

    // Get the refresh rate from the device context
    int refreshRate = GetDeviceCaps(hdc, VREFRESH);
    ReleaseDC(NULL, hdc);

    // If GetDeviceCaps returns 0 or 1, try alternative method
    if (refreshRate <= 1) {
        DEVMODE devMode;
        ZeroMemory(&devMode, sizeof(devMode));
        devMode.dmSize = sizeof(devMode);

        if (EnumDisplaySettings(NULL, ENUM_CURRENT_SETTINGS, &devMode)) {
            refreshRate = devMode.dmDisplayFrequency;
        } else {
            refreshRate = 60; // Default fallback
        }
    }

    // Sanity check - ensure reasonable refresh rate
    if (refreshRate < 30 || refreshRate > 300) {
        refreshRate = 60; // Default fallback
    }

    return refreshRate;
}

void ConsoleUtils::enableVSync() {
    // For console applications, we can't enable hardware VSync
    // but we can implement software VSync by timing our frame updates
    // This is handled in the animation loop timing
}

// ANSI escape sequence functions for flicker-free rendering
void ConsoleUtils::clearScreenANSI() {
    // Move cursor to top-left and clear screen
    std::cout << "\033[H\033[2J" << std::flush;
}

void ConsoleUtils::setCursorPositionANSI(int x, int y) {
    // ANSI coordinates are 1-based, so add 1 to x and y
    std::cout << "\033[" << (y + 1) << ";" << (x + 1) << "H" << std::flush;
}

void ConsoleUtils::hideCursorANSI() {
    std::cout << "\033[?25l" << std::flush;
}

void ConsoleUtils::showCursorANSI() {
    std::cout << "\033[?25h" << std::flush;
}

void ConsoleUtils::clearLineANSI() {
    std::cout << "\033[2K" << std::flush;
}

void ConsoleUtils::clearFromCursorANSI() {
    std::cout << "\033[0J" << std::flush;
}

void ConsoleUtils::clearToEndOfLineANSI() {
    std::cout << "\033[0K" << std::flush;
}

void ConsoleUtils::saveCursorPositionANSI() {
    std::cout << "\033[s" << std::flush;
}

void ConsoleUtils::restoreCursorPositionANSI() {
    std::cout << "\033[u" << std::flush;
}

void ConsoleUtils::setupUtf8Console() {
#ifdef _WIN32
    // Set console output code page to UTF-8
    if (SetConsoleOutputCP(CP_UTF8) == 0) {
        std::cerr << "Error: Unable to set console output code page to UTF-8." << std::endl;
    }
    // Set console input code page to UTF-8
    if (SetConsoleCP(CP_UTF8) == 0) {
        std::cerr << "Error: Unable to set console input code page to UTF-8." << std::endl;
    }
    // Set the C++ locale to system default to ensure C++ streams work correctly
    try {
        std::locale::global(std::locale(""));
    } catch (const std::runtime_error& e) {
        std::cerr << "Error: Unable to set C++ locale. " << e.what() << std::endl;
    }
#endif
}

void ConsoleUtils::enableVirtualTerminalProcessing() {
    if (consoleHandle == INVALID_HANDLE_VALUE) return;

    DWORD consoleMode;
    GetConsoleMode(consoleHandle, &consoleMode);
    consoleMode |= ENABLE_VIRTUAL_TERMINAL_PROCESSING;
    SetConsoleMode(consoleHandle, consoleMode);
}

void ConsoleUtils::disableQuickEditMode() {
    if (inputHandle == INVALID_HANDLE_VALUE) return;

    DWORD consoleMode;
    GetConsoleMode(inputHandle, &consoleMode);
    consoleMode &= ~ENABLE_QUICK_EDIT_MODE;
    SetConsoleMode(inputHandle, consoleMode);
}

// Private methods are now correctly part of the class
bool ConsoleUtils::getConsoleSize(int& width, int& height) {
    if (consoleHandle == INVALID_HANDLE_VALUE) return false;
    
    CONSOLE_SCREEN_BUFFER_INFO screenInfo;
    if (GetConsoleScreenBufferInfo(consoleHandle, &screenInfo)) {
        width = screenInfo.srWindow.Right - screenInfo.srWindow.Left + 1;
        height = screenInfo.srWindow.Bottom - screenInfo.srWindow.Top + 1;
        return true;
    }
    return false;
}

void ConsoleUtils::getDefaultAttributes() {
    if (consoleHandle == INVALID_HANDLE_VALUE) return;
    
    CONSOLE_SCREEN_BUFFER_INFO screenInfo;
    if (GetConsoleScreenBufferInfo(consoleHandle, &screenInfo)) {
        defaultAttributes = screenInfo.wAttributes;
    }
}