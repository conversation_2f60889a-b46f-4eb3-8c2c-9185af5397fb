#include <iostream>
#include <windows.h>

int main() {
    std::cout << "System Display Refresh Rate Detection" << std::endl;
    std::cout << "=====================================" << std::endl;

    // Method 1: GetDeviceCaps
    HDC hdc = GetDC(NULL);
    if (hdc != NULL) {
        int refreshRate1 = GetDeviceCaps(hdc, VREFRESH);
        std::cout << "Method 1 (GetDeviceCaps): " << refreshRate1 << " Hz" << std::endl;
        ReleaseDC(NULL, hdc);
    }

    // Method 2: EnumDisplaySettings
    DEVMODE devMode;
    ZeroMemory(&devMode, sizeof(devMode));
    devMode.dmSize = sizeof(devMode);

    if (EnumDisplaySettings(NULL, ENUM_CURRENT_SETTINGS, &devMode)) {
        std::cout << "Method 2 (EnumDisplaySettings): " << devMode.dmDisplayFrequency << " Hz" << std::endl;
        std::cout << "Resolution: " << devMode.dmPelsWidth << "x" << devMode.dmPelsHeight << std::endl;
        std::cout << "Color depth: " << devMode.dmBitsPerPel << " bits" << std::endl;
    }

    // Method 3: Multiple monitors
    std::cout << "\nAll Display Devices:" << std::endl;
    DISPLAY_DEVICE dd;
    ZeroMemory(&dd, sizeof(dd));
    dd.cb = sizeof(dd);

    for (int i = 0; EnumDisplayDevices(NULL, i, &dd, 0); i++) {
        std::cout << "Device " << i << ": " << dd.DeviceString << std::endl;

        DEVMODE dm;
        ZeroMemory(&dm, sizeof(dm));
        dm.dmSize = sizeof(dm);

        if (EnumDisplaySettings(dd.DeviceName, ENUM_CURRENT_SETTINGS, &dm)) {
            std::cout << "  Refresh Rate: " << dm.dmDisplayFrequency << " Hz" << std::endl;
            std::cout << "  Resolution: " << dm.dmPelsWidth << "x" << dm.dmPelsHeight << std::endl;
        }
        std::cout << std::endl;
    }

    std::cout << "Press any key to exit..." << std::endl;
    std::cin.get();

    return 0;
}
