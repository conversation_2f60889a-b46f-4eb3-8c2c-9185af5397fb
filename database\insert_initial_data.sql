-- Initial data for Race Simulator Database
-- Insert default roles, sample teams, and players

-- Insert default player roles
INSERT OR IGNORE INTO roles (name, description, base_speed_min, base_speed_max, base_power_min, base_power_max, 
                             base_endurance_min, base_endurance_max, base_stamina_min, base_stamina_max,
                             mid_race_accel_chance, start_wisdom_chance, late_race_power_chance) VALUES
('Sprinter', 'High speed, low endurance specialist', 180, 255, 160, 220, 100, 150, 120, 180, 0.4, 0.5, 0.2),
('Endurance Runner', 'High endurance, steady pace', 120, 180, 140, 200, 200, 255, 180, 240, 0.2, 0.3, 0.6),
('Power Runner', 'High power and body strength', 140, 200, 200, 255, 160, 220, 160, 220, 0.3, 0.2, 0.5),
('Balanced Runner', 'Well-rounded attributes', 140, 200, 140, 200, 140, 200, 140, 200, 0.3, 0.3, 0.3),
('Technical Runner', 'High intelligence and adaptability', 130, 190, 130, 190, 150, 210, 150, 210, 0.2, 0.6, 0.2),
('Rookie', 'Beginner with growth potential', 80, 140, 80, 140, 80, 140, 100, 160, 0.1, 0.1, 0.1);

-- Insert sample teams
INSERT OR IGNORE INTO teams (name, description, owner_name) VALUES
('Lightning Bolts', 'Elite sprinting team focused on speed', 'Coach Johnson'),
('Thunder Runners', 'Balanced team with diverse talents', 'Coach Smith'),
('Storm Chasers', 'Endurance specialists', 'Coach Williams'),
('Flash Squad', 'Young and upcoming talents', 'Coach Davis');

-- Insert sample players for Lightning Bolts (Team 1)
INSERT OR IGNORE INTO players (name, team_id, role_id, mood, stamina, speed, power, endurance, intelligence, body, 
                              adaptability, has_mid_race_accel, has_start_wisdom, has_late_race_power, stamina_current) VALUES
-- Lightning Bolts sprinters
('Usain Lightning', 1, 1, 200, 180, 240, 200, 140, 160, 220, 2, 1, 1, 0, 255),
('Carl Speedster', 1, 1, 180, 160, 220, 180, 120, 140, 200, 1, 1, 0, 0, 255),
('Ben Rocket', 1, 1, 190, 170, 230, 190, 130, 150, 210, 2, 0, 1, 0, 255),
('Justin Flash', 1, 4, 170, 150, 200, 170, 160, 170, 180, 1, 0, 0, 1, 255);

-- Insert sample players for Thunder Runners (Team 2)
INSERT OR IGNORE INTO players (name, team_id, role_id, mood, stamina, speed, power, endurance, intelligence, body, 
                              adaptability, has_mid_race_accel, has_start_wisdom, has_late_race_power, stamina_current) VALUES
('Mike Thunder', 2, 4, 160, 170, 180, 180, 180, 180, 180, 2, 1, 1, 1, 255),
('Sarah Storm', 2, 5, 180, 160, 160, 150, 170, 200, 160, 3, 0, 1, 0, 255),
('Alex Hurricane', 2, 3, 150, 180, 170, 210, 160, 140, 200, 1, 1, 0, 1, 255),
('Emma Cyclone', 2, 2, 170, 200, 150, 160, 220, 160, 170, 2, 0, 0, 1, 255);

-- Insert sample players for Storm Chasers (Team 3)
INSERT OR IGNORE INTO players (name, team_id, role_id, mood, stamina, speed, power, endurance, intelligence, body, 
                              adaptability, has_mid_race_accel, has_start_wisdom, has_late_race_power, stamina_current) VALUES
('David Marathon', 3, 2, 180, 220, 140, 170, 240, 170, 180, 2, 0, 0, 1, 255),
('Lisa Endurance', 3, 2, 170, 210, 130, 160, 230, 180, 170, 1, 0, 1, 1, 255),
('Tom Steady', 3, 4, 160, 190, 160, 180, 200, 160, 180, 2, 0, 1, 0, 255),
('Nina Persistent', 3, 2, 190, 200, 150, 170, 220, 190, 160, 3, 0, 0, 1, 255);

-- Insert sample players for Flash Squad (Team 4) - younger/rookie players
INSERT OR IGNORE INTO players (name, team_id, role_id, mood, stamina, speed, power, endurance, intelligence, body, 
                              adaptability, has_mid_race_accel, has_start_wisdom, has_late_race_power, stamina_current) VALUES
('Jake Newbie', 4, 6, 140, 120, 110, 100, 100, 120, 130, 1, 0, 0, 0, 255),
('Amy Rookie', 4, 6, 150, 130, 120, 110, 110, 130, 120, 2, 0, 1, 0, 255),
('Sam Prospect', 4, 1, 160, 140, 160, 130, 120, 140, 150, 1, 1, 0, 0, 255),
('Zoe Rising', 4, 5, 170, 150, 140, 120, 140, 160, 140, 2, 0, 1, 0, 255);

-- Insert a sample tournament
INSERT OR IGNORE INTO tournaments (name, description, status, max_participants, entry_fee, prize_pool, 
                                  distance, track_type, weather, wind_direction, wind_speed) VALUES
('Spring Championship 2025', 'Annual spring racing championship', 'planned', 16, 100, 5000, 
 100.0, 0, 0, 1, 2.5),
('Summer Sprint Series', 'Fast-paced summer competition', 'planned', 12, 50, 2000,
 100.0, 0, 1, 0, 1.0),
('Rookie Cup', 'Competition for new and developing runners', 'planned', 8, 25, 500,
 100.0, 1, 2, 0, 0.5);

-- Insert tournament stages for Spring Championship
INSERT OR IGNORE INTO tournament_stages (tournament_id, name, stage_order, advancement_count) VALUES
(1, 'Qualifying Round', 1, 8),
(1, 'Quarter Finals', 2, 4),
(1, 'Semi Finals', 3, 2),
(1, 'Final', 4, 1);

-- Insert tournament stages for Summer Sprint Series
INSERT OR IGNORE INTO tournament_stages (tournament_id, name, stage_order, advancement_count) VALUES
(2, 'Heats', 1, 6),
(2, 'Semi Finals', 2, 3),
(2, 'Final', 3, 1);

-- Insert tournament stages for Rookie Cup
INSERT OR IGNORE INTO tournament_stages (tournament_id, name, stage_order, advancement_count) VALUES
(3, 'Preliminary', 1, 4),
(3, 'Final', 2, 1);

-- Sample tournament registrations (some players registered for tournaments)
INSERT OR IGNORE INTO tournament_participants (tournament_id, player_id, status) VALUES
-- Spring Championship participants
(1, 1, 'registered'), (1, 2, 'registered'), (1, 3, 'registered'), (1, 4, 'registered'),
(1, 5, 'registered'), (1, 6, 'registered'), (1, 7, 'registered'), (1, 8, 'registered'),
(1, 9, 'registered'), (1, 10, 'registered'), (1, 11, 'registered'), (1, 12, 'registered'),

-- Summer Sprint participants
(2, 1, 'registered'), (2, 3, 'registered'), (2, 5, 'registered'), (2, 7, 'registered'),
(2, 9, 'registered'), (2, 11, 'registered'),

-- Rookie Cup participants (only rookies and some balanced runners)
(3, 13, 'registered'), (3, 14, 'registered'), (3, 15, 'registered'), (3, 16, 'registered'),
(3, 4, 'registered'), (3, 8, 'registered');

-- Sample training sessions
INSERT OR IGNORE INTO training_sessions (player_id, training_type, attribute_improved, improvement_amount, stamina_cost) VALUES
(13, 'Speed Training', 'speed', 2, 8),
(13, 'Endurance Training', 'endurance', 1, 6),
(14, 'Power Training', 'power', 2, 7),
(15, 'Technical Training', 'intelligence', 3, 5),
(16, 'Adaptability Training', 'adaptability', 1, 4);
