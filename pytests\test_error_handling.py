"""
Error Handling Tests

Tests for graceful degradation and error handling including:
- DLL unavailable scenarios
- Invalid input data handling
- Network/IO errors
- Resource unavailability
- User-friendly error messages
"""

import os
import sys
import unittest
from unittest.mock import patch, MagicMock
import tempfile
import json
import shutil

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from ui_controller import UIController
from data_loader import DataLoader
from race_simulator_types import RaceSimulatorAPI, create_sample_runners


class TestErrorHandling(unittest.TestCase):
    """Test suite for error handling and graceful degradation."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.sample_runners = create_sample_runners()
        
    def tearDown(self):
        """Clean up test fixtures."""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_dll_unavailable_fallback(self):
        """Test graceful fallback when DLL is unavailable."""
        # Test DataLoader with non-existent DLL
        loader = DataLoader("nonexistent.dll")
        
        # Should use fallback simulation
        race_data = loader.calculate_race(self.sample_runners, 100.0)
        
        self.assertIsNotNone(race_data)
        self.assertIn('race_info', race_data)
        self.assertIn('results', race_data)
        self.assertIn('frames', race_data)
        
        # Results should still be reasonable
        results = race_data['results']
        self.assertEqual(len(results), len(self.sample_runners))
        
        for result in results:
            self.assertIn('finish_time', result)
            self.assertIn('final_position', result)
            self.assertTrue(result['did_finish'])
    
    def test_invalid_json_file_handling(self):
        """Test handling of invalid JSON files."""
        # Create malformed JSON file
        invalid_json_path = os.path.join(self.temp_dir, "invalid.json")
        with open(invalid_json_path, 'w') as f:
            f.write('{"invalid": json content}')
        
        loader = DataLoader()
        runners = loader.load_runners_from_file(invalid_json_path)
        
        # Should return sample data as fallback
        self.assertIsInstance(runners, list)
        self.assertGreater(len(runners), 0)
    
    def test_missing_json_file_handling(self):
        """Test handling of missing JSON files."""
        loader = DataLoader()
        runners = loader.load_runners_from_file("definitely_missing.json")
        
        # Should return sample data as fallback
        self.assertIsInstance(runners, list)
        self.assertGreater(len(runners), 0)
    
    def test_empty_json_file_handling(self):
        """Test handling of empty JSON files."""
        empty_json_path = os.path.join(self.temp_dir, "empty.json")
        with open(empty_json_path, 'w') as f:
            f.write('')
        
        loader = DataLoader()
        runners = loader.load_runners_from_file(empty_json_path)
        
        # Should return sample data as fallback
        self.assertIsInstance(runners, list)
        self.assertGreater(len(runners), 0)
    
    def test_malformed_runner_data_handling(self):
        """Test handling of malformed runner data."""
        malformed_runners = [
            {"id": "not_a_number", "max_speed": 12.5, "acceleration": 2.8, "endurance": 0.85},
            {"id": 1, "max_speed": "not_a_number", "acceleration": 2.8, "endurance": 0.85},
            {"id": 1, "max_speed": 12.5},  # Missing required fields
            {},  # Empty runner
            None,  # None runner
        ]
        
        for malformed_runner in malformed_runners:
            with self.subTest(runner=malformed_runner):
                loader = DataLoader()
                
                # Should handle gracefully without crashing
                try:
                    race_data = loader.calculate_race([malformed_runner], 100.0)
                    # If it doesn't crash, that's acceptable
                    self.assertIsNotNone(race_data)
                except (KeyError, TypeError, ValueError):
                    # Expected for malformed data
                    pass
    
    def test_negative_distance_handling(self):
        """Test handling of negative race distances."""
        loader = DataLoader()
        
        # Should handle gracefully
        try:
            race_data = loader.calculate_race(self.sample_runners, -100.0)
            # May use absolute value or other handling
            self.assertIsNotNone(race_data)
        except ValueError:
            # Expected for negative distance
            pass
    
    def test_zero_distance_handling(self):
        """Test handling of zero race distance."""
        loader = DataLoader()
        race_data = loader.calculate_race(self.sample_runners, 0.0)
        
        # Should handle gracefully
        self.assertIsNotNone(race_data)
        self.assertEqual(race_data['race_info']['distance'], 0.0)
    
    def test_very_large_distance_handling(self):
        """Test handling of very large race distances."""
        loader = DataLoader()
        race_data = loader.calculate_race(self.sample_runners, 1000000.0)  # 1000km
        
        # Should handle gracefully
        self.assertIsNotNone(race_data)
        self.assertEqual(race_data['race_info']['distance'], 1000000.0)
    
    def test_zero_runners_handling(self):
        """Test handling of empty runner list."""
        loader = DataLoader()
        race_data = loader.calculate_race([], 100.0)
        
        # Should handle gracefully
        self.assertIsNotNone(race_data)
        self.assertEqual(race_data['race_info']['total_runners'], 0)
        self.assertEqual(len(race_data['results']), 0)
    
    def test_invalid_export_path_handling(self):
        """Test handling of invalid export paths."""
        controller = UIController()
        controller.race_data = {
            'race_info': {'distance': 100.0, 'total_runners': 5},
            'results': [{'runner_id': 1, 'finish_time': 10.0}],
            'frames': []
        }
        
        # Mock data_loader
        mock_loader = MagicMock()
        mock_loader.export_results.return_value = False
        controller.data_loader = mock_loader
        
        # Test with invalid path
        success = controller.export_results("/invalid/path/results.json")
        self.assertFalse(success)
    
    def test_permission_denied_export_handling(self):
        """Test handling of permission denied during export."""
        controller = UIController()
        controller.race_data = {
            'race_info': {'distance': 100.0, 'total_runners': 5},
            'results': [{'runner_id': 1, 'finish_time': 10.0}],
            'frames': []
        }
        
        # Mock data_loader to simulate permission error
        mock_loader = MagicMock()
        mock_loader.export_results.side_effect = PermissionError("Permission denied")
        controller.data_loader = mock_loader
        
        # Should handle gracefully
        success = controller.export_results("/root/protected.json")
        self.assertFalse(success)
    
    def test_corrupt_config_file_handling(self):
        """Test handling of corrupt configuration files."""
        # Create corrupt config file
        config_path = os.path.join(self.temp_dir, "config.json")
        with open(config_path, 'w') as f:
            f.write('{"corrupted": json content}')
        
        # Test with corrupt config
        with patch('os.path.exists', return_value=True):
            controller = UIController()
            
            # Should use defaults
            config = controller.config
            self.assertIsNotNone(config)
            self.assertIn('window', config)
            self.assertIn('race', config)
    
    def test_missing_config_file_handling(self):
        """Test handling of missing configuration file."""
        with patch('os.path.exists', return_value=False):
            controller = UIController()
            
            # Should use defaults
            config = controller.config
            self.assertIsNotNone(config)
            self.assertEqual(config['window']['width'], 1200)
            self.assertEqual(config['race']['default_distance'], 100.0)
    
    def test_dll_corruption_handling(self):
        """Test handling of corrupted DLL file."""
        # Create fake corrupted DLL
        dll_path = os.path.join(self.temp_dir, "corrupted.dll")
        with open(dll_path, 'w') as f:
            f.write("This is not a valid DLL")
        
        # Test with corrupted DLL
        try:
            api = RaceSimulatorAPI(dll_path)
            self.fail("Should have failed to load corrupted DLL")
        except (RuntimeError, OSError):
            # Expected behavior
            pass
    
    def test_memory_limit_handling(self):
        """Test handling of memory limitations."""
        # Test with very large race configuration
        large_runners = [
            {"id": i, "max_speed": 12.0, "acceleration": 3.0, "endurance": 0.85}
            for i in range(1000)  # Many runners
        ]
        
        loader = DataLoader()
        
        # Should handle gracefully without crashing
        try:
            race_data = loader.calculate_race(large_runners, 100.0)
            self.assertIsNotNone(race_data)
        except MemoryError:
            # Expected on memory-constrained systems
            pass
        except Exception as e:
            # Other exceptions should also be handled gracefully
            self.assertIn('race_info', str(e).lower() or 'fallback' in str(e).lower())
    
    def test_unicode_path_handling(self):
        """Test handling of Unicode paths."""
        unicode_dir = os.path.join(self.temp_dir, "测试目录_тест_🚀")
        os.makedirs(unicode_dir, exist_ok=True)
        
        unicode_file = os.path.join(unicode_dir, "тестовые_бегуны.json")
        with open(unicode_file, 'w', encoding='utf-8') as f:
            json.dump(self.sample_runners, f, ensure_ascii=False)
        
        loader = DataLoader()
        
        # Should handle Unicode paths gracefully
        try:
            runners = loader.load_runners_from_file(unicode_file)
            self.assertIsInstance(runners, list)
        except UnicodeError:
            # Expected on some systems
            pass
    
    def test_concurrent_access_handling(self):
        """Test handling of concurrent file access."""
        # Create test file
        test_file = os.path.join(self.temp_dir, "concurrent_test.json")
        with open(test_file, 'w') as f:
            json.dump(self.sample_runners, f)
        
        # Test multiple simultaneous accesses
        loaders = [DataLoader() for _ in range(5)]
        
        for loader in loaders:
            try:
                runners = loader.load_runners_from_file(test_file)
                self.assertIsInstance(runners, list)
            except Exception:
                # Should handle gracefully
                pass
    
    def test_network_path_handling(self):
        """Test handling of network paths."""
        # Test with UNC path (Windows) or NFS path (Unix)
        if os.name == 'nt':
            network_path = r"\\server\share\runners.json"
        else:
            network_path = "/mnt/nfs/runners.json"
        
        loader = DataLoader()
        
        # Should handle gracefully
        runners = loader.load_runners_from_file(network_path)
        self.assertIsInstance(runners, list)
    
    def test_recovery_from_partial_failure(self):
        """Test recovery from partial system failure."""
        # Test scenario: DLL available but returns partial data
        with patch('race_simulator_types.RaceSimulatorAPI.calculate_race') as mock_calc:
            # Simulate partial failure - return incomplete data
            mock_calc.return_value = {
                'total_runners': len(self.sample_runners),
                'race_distance': 100.0,
                'results': [],  # Missing results
                'snapshots': []  # Missing snapshots
            }
            
            loader = DataLoader("fake_dll.dll")
            race_data = loader.calculate_race(self.sample_runners, 100.0)
            
            # Should handle gracefully and provide fallback
            self.assertIsNotNone(race_data)
            self.assertIn('race_info', race_data)


class TestUserFriendlyErrorMessages(unittest.TestCase):
    """Test suite for user-friendly error messages."""
    
    def test_dll_not_found_message(self):
        """Test user-friendly DLL not found message."""
        with patch('builtins.print') as mock_print:
            try:
                api = RaceSimulatorAPI("definitely_missing.dll")
            except FileNotFoundError as e:
                # Should contain helpful information
                error_msg = str(e)
                self.assertIn('race_simulator.dll', error_msg)
                self.assertIn('application directory', error_msg.lower())
    
    def test_invalid_json_message(self):
        """Test user-friendly invalid JSON message."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            f.write('{"invalid": json content}')
            invalid_file = f.name
        
        try:
            with patch('builtins.print') as mock_print:
                loader = DataLoader()
                runners = loader.load_runners_from_file(invalid_file)
                
                # Should print helpful message
                print_calls = [str(call) for call in mock_print.call_args_list]
                self.assertTrue(any('json' in str(call).lower() for call in print_calls))
        finally:
            os.unlink(invalid_file)
    
    def test_missing_file_message(self):
        """Test user-friendly missing file message."""
        with patch('builtins.print') as mock_print:
            loader = DataLoader()
            runners = loader.load_runners_from_file("missing_file.json")
            
            # Should print helpful message
            print_calls = [str(call) for call in mock_print.call_args_list]
            self.assertTrue(any('not found' in str(call).lower() for call in print_calls))
    
    def test_calculation_error_message(self):
        """Test user-friendly calculation error message."""
        with patch('builtins.print') as mock_print:
            # Test with invalid runner data
            invalid_runner = {"id": "invalid", "max_speed": 12.5, "acceleration": 2.8, "endurance": 0.85}
            
            loader = DataLoader()
            try:
                race_data = loader.calculate_race([invalid_runner], 100.0)
            except Exception:
                pass
            
            # Should handle gracefully without crashing
            self.assertTrue(True)  # If we get here, no crash occurred


class TestGracefulDegradation(unittest.TestCase):
    """Test suite for graceful degradation scenarios."""
    
    def test_no_dll_workflow(self):
        """Test complete workflow without DLL."""
        controller = UIController()
        
        # Test complete headless workflow without DLL
        with patch('builtins.print'):
            with patch('ui_controller.DataLoader'):
                args = ['--no-gui', '--sample', '--distance', '100']
                exit_code = controller.run(args)
                
                # Should complete successfully using fallback
                self.assertEqual(exit_code, 0)
    
    def test_corrupted_config_workflow(self):
        """Test workflow with corrupted configuration."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            f.write('{"corrupted": json content}')
            config_file = f.name
        
        try:
            with patch('builtins.print'):
                with patch('os.path.exists', return_value=True):
                    controller = UIController()
                    
                    # Should use defaults and complete successfully
                    config = controller.config
                    self.assertIsNotNone(config)
                    self.assertEqual(config['window']['width'], 1200)
        finally:
            os.unlink(config_file)
    
    def test_partial_system_failure(self):
        """Test handling of partial system failure."""
        # Simulate partial failure where some components work
        controller = UIController()
        
        # Mock partial failure scenarios
        scenarios = [
            # DLL fails, fallback works
            lambda: patch('race_simulator_types.RaceSimulatorAPI', side_effect=FileNotFoundError),
            # JSON parsing fails, sample data used
            lambda: patch('json.load', side_effect=json.JSONDecodeError("test", "doc", 0)),
            # File system errors, defaults used
            lambda: patch('builtins.open', side_effect=IOError("Disk full")),
        ]
        
        for scenario_factory in scenarios:
            with scenario_factory():
                try:
                    # Should handle gracefully
                    config = controller.config
                    self.assertIsNotNone(config)
                except Exception:
                    # Should not crash completely
                    pass


if __name__ == '__main__':
    unittest.main()