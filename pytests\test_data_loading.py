"""
Data Loading Tests

Tests for race calculation and result parsing including:
- JSON file loading and parsing
- Runner data validation
- Race calculation accuracy
- Results formatting and export
- Error handling for malformed data
"""

import os
import sys
import unittest
import json
import tempfile
from typing import Dict, Any, List

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from data_loader import DataLoader
from race_simulator_types import create_sample_runners


class TestDataLoading(unittest.TestCase):
    """Test suite for data loading and race calculation."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.sample_runners = create_sample_runners()
        self.test_distance = 100.0
    
    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_load_valid_json_file(self):
        """Test loading runner data from valid JSON file."""
        # Create test JSON file
        test_data = {
            "runners": [
                {"id": 1, "max_speed": 12.5, "acceleration": 2.8, "endurance": 0.85},
                {"id": 2, "max_speed": 11.8, "acceleration": 3.2, "endurance": 0.90}
            ]
        }
        
        file_path = os.path.join(self.temp_dir, "test_runners.json")
        with open(file_path, 'w') as f:
            json.dump(test_data, f)
        
        loader = DataLoader()
        runners = loader.load_runners_from_file(file_path)
        
        self.assertEqual(len(runners), 2)
        self.assertEqual(runners[0]['id'], 1)
        self.assertEqual(runners[0]['max_speed'], 12.5)
    
    def test_load_json_array_format(self):
        """Test loading runner data from JSON array format."""
        test_data = [
            {"id": 1, "max_speed": 12.5, "acceleration": 2.8, "endurance": 0.85},
            {"id": 2, "max_speed": 11.8, "acceleration": 3.2, "endurance": 0.90}
        ]
        
        file_path = os.path.join(self.temp_dir, "test_runners_array.json")
        with open(file_path, 'w') as f:
            json.dump(test_data, f)
        
        loader = DataLoader()
        runners = loader.load_runners_from_file(file_path)
        
        self.assertEqual(len(runners), 2)
        self.assertEqual(runners[1]['id'], 2)
    
    def test_load_nonexistent_file(self):
        """Test handling of non-existent file."""
        loader = DataLoader()
        runners = loader.load_runners_from_file("nonexistent.json")
        
        # Should return sample data as fallback
        self.assertIsInstance(runners, list)
        self.assertGreater(len(runners), 0)
    
    def test_load_malformed_json(self):
        """Test handling of malformed JSON."""
        file_path = os.path.join(self.temp_dir, "malformed.json")
        with open(file_path, 'w') as f:
            f.write("{ invalid json }")
        
        loader = DataLoader()
        runners = loader.load_runners_from_file(file_path)
        
        # Should return sample data as fallback
        self.assertIsInstance(runners, list)
        self.assertGreater(len(runners), 0)
    
    def test_load_invalid_json_structure(self):
        """Test handling of invalid JSON structure."""
        invalid_structures = [
            {"invalid": "structure"},  # Missing runners key
            {"runners": "not a list"},  # Runners not a list
            {},  # Empty object
            [],  # Empty array
        ]
        
        for i, structure in enumerate(invalid_structures):
            with self.subTest(structure=structure):
                file_path = os.path.join(self.temp_dir, f"invalid_{i}.json")
                with open(file_path, 'w') as f:
                    json.dump(structure, f)
                
                loader = DataLoader()
                runners = loader.load_runners_from_file(file_path)
                
                # Should return sample data as fallback
                self.assertIsInstance(runners, list)
    
    def test_runner_data_validation(self):
        """Test validation of runner data fields."""
        invalid_runners = [
            {"id": "string", "max_speed": 12.5, "acceleration": 2.8, "endurance": 0.85},
            {"id": 1, "max_speed": "fast", "acceleration": 2.8, "endurance": 0.85},
            {"id": 1, "max_speed": 12.5, "acceleration": "quick", "endurance": 0.85},
            {"id": 1, "max_speed": 12.5, "acceleration": 2.8, "endurance": "high"},
            {"id": 1},  # Missing required fields
            {"max_speed": 12.5, "acceleration": 2.8, "endurance": 0.85},  # Missing id
        ]
        
        for invalid_runner in invalid_runners:
            with self.subTest(invalid_runner=invalid_runner):
                file_path = os.path.join(self.temp_dir, "invalid_runner.json")
                with open(file_path, 'w') as f:
                    json.dump([invalid_runner], f)
                
                loader = DataLoader()
                # Should handle gracefully without crashing
                try:
                    race_data = loader.calculate_race([invalid_runner], self.test_distance)
                    self.assertIsNotNone(race_data)
                except (KeyError, ValueError, TypeError):
                    pass  # Expected for invalid data
    
    def test_race_calculation_basic(self):
        """Test basic race calculation functionality."""
        loader = DataLoader()
        race_data = loader.calculate_race(self.sample_runners, self.test_distance)
        
        # Verify data structure
        self.assertIsNotNone(race_data)
        self.assertIn('race_info', race_data)
        self.assertIn('results', race_data)
        self.assertIn('frames', race_data)
        
        # Verify race info
        race_info = race_data['race_info']
        self.assertEqual(race_info['distance'], self.test_distance)
        self.assertEqual(race_info['total_runners'], len(self.sample_runners))
        self.assertGreater(race_info['total_frames'], 0)
    
    def test_race_results_structure(self):
        """Test structure and content of race results."""
        loader = DataLoader()
        race_data = loader.calculate_race(self.sample_runners, self.test_distance)
        
        results = race_data['results']
        self.assertEqual(len(results), len(self.sample_runners))
        
        for result in results:
            self.assertIn('runner_id', result)
            self.assertIn('finish_time', result)
            self.assertIn('max_speed_achieved', result)
            self.assertIn('average_speed', result)
            self.assertIn('final_position', result)
            self.assertIn('did_finish', result)
            
            # Verify data types
            self.assertIsInstance(result['runner_id'], int)
            self.assertIsInstance(result['finish_time'], (int, float))
            self.assertIsInstance(result['final_position'], int)
            self.assertIsInstance(result['did_finish'], bool)
    
    def test_race_results_ordering(self):
        """Test that results are properly ordered by finish time."""
        loader = DataLoader()
        race_data = loader.calculate_race(self.sample_runners, self.test_distance)
        
        results = race_data['results']
        sorted_results = sorted(results, key=lambda x: x['final_position'])
        
        # Results should be in order (position 1, 2, 3, ...)
        for i, result in enumerate(sorted_results, 1):
            self.assertEqual(result['final_position'], i)
    
    def test_animation_frames_structure(self):
        """Test structure of animation frames."""
        loader = DataLoader()
        race_data = loader.calculate_race(self.sample_runners, self.test_distance)
        
        frames = race_data['frames']
        self.assertIsInstance(frames, list)
        self.assertGreater(len(frames), 0)
        
        # Check frame structure
        for frame in frames:
            self.assertIn('time', frame)
            self.assertIn('runners', frame)
            self.assertIsInstance(frame['time'], (int, float))
            self.assertIsInstance(frame['runners'], dict)
    
    def test_frame_runner_data(self):
        """Test runner data within animation frames."""
        loader = DataLoader()
        race_data = loader.calculate_race(self.sample_runners, self.test_distance)
        
        frames = race_data['frames']
        sample_frame = frames[len(frames) // 2]  # Middle frame
        
        for runner_id, runner_data in sample_frame['runners'].items():
            self.assertIn('position', runner_data)
            self.assertIn('speed', runner_data)
            self.assertIn('acceleration', runner_data)
            
            # Verify data types and ranges
            self.assertIsInstance(runner_data['position'], (int, float))
            self.assertIsInstance(runner_data['speed'], (int, float))
            self.assertIsInstance(runner_data['acceleration'], (int, float))
            
            # Position should be within race distance
            self.assertGreaterEqual(runner_data['position'], 0)
            self.assertLessEqual(runner_data['position'], self.test_distance)
    
    def test_time_based_frame_retrieval(self):
        """Test retrieving frames based on time."""
        loader = DataLoader()
        race_data = loader.calculate_race(self.sample_runners, self.test_distance)
        
        total_duration = loader.get_total_duration()
        self.assertGreater(total_duration, 0)
        
        # Test frame retrieval at various times
        test_times = [0.0, total_duration / 2, total_duration]
        
        for test_time in test_times:
            frame = loader.get_frame_at_time(test_time)
            self.assertIsNotNone(frame)
            self.assertAlmostEqual(frame['time'], test_time, places=2)
    
    def test_export_results_json(self):
        """Test exporting race results to JSON."""
        loader = DataLoader()
        race_data = loader.calculate_race(self.sample_runners, self.test_distance)
        loader.race_data = race_data
        
        export_path = os.path.join(self.temp_dir, "exported_results.json")
        success = loader.export_results(export_path)
        
        self.assertTrue(success)
        self.assertTrue(os.path.exists(export_path))
        
        # Verify exported JSON structure
        with open(export_path, 'r') as f:
            exported_data = json.load(f)
        
        self.assertIn('race_info', exported_data)
        self.assertIn('results', exported_data)
        self.assertIn('frames', exported_data)
    
    def test_export_without_data(self):
        """Test export when no race data is available."""
        loader = DataLoader()
        
        export_path = os.path.join(self.temp_dir, "empty_export.json")
        success = loader.export_results(export_path)
        
        self.assertFalse(success)
        self.assertFalse(os.path.exists(export_path))
    
    def test_race_summary_generation(self):
        """Test generation of race summary."""
        loader = DataLoader()
        race_data = loader.calculate_race(self.sample_runners, self.test_distance)
        loader.race_data = race_data
        
        summary = loader.get_race_summary()
        
        self.assertIsInstance(summary, str)
        self.assertIn("Race Summary", summary)
        self.assertIn(str(len(self.sample_runners)), summary)
        self.assertIn(str(self.test_distance), summary)
        
        # Should contain results for each runner
        for runner in self.sample_runners:
            self.assertIn(str(runner['id']), summary)
    
    def test_different_race_distances(self):
        """Test race calculation with different distances."""
        test_distances = [50.0, 100.0, 200.0, 400.0, 800.0]
        
        for distance in test_distances:
            with self.subTest(distance=distance):
                loader = DataLoader()
                race_data = loader.calculate_race(self.sample_runners, distance)
                
                self.assertEqual(race_data['race_info']['distance'], distance)
                self.assertEqual(race_data['race_info']['total_runners'], len(self.sample_runners))
    
    def test_empty_runners_list(self):
        """Test handling of empty runners list."""
        loader = DataLoader()
        race_data = loader.calculate_race([], self.test_distance)
        
        self.assertEqual(race_data['race_info']['total_runners'], 0)
        self.assertEqual(len(race_data['results']), 0)
        self.assertEqual(len(race_data['frames']), 0)
    
    def test_single_runner_race(self):
        """Test race calculation with single runner."""
        single_runner = [self.sample_runners[0]]
        loader = DataLoader()
        race_data = loader.calculate_race(single_runner, self.test_distance)
        
        self.assertEqual(race_data['race_info']['total_runners'], 1)
        self.assertEqual(len(race_data['results']), 1)
        self.assertEqual(race_data['results'][0]['final_position'], 1)
    
    def test_runner_colors_generation(self):
        """Test generation of consistent runner colors."""
        loader = DataLoader()
        race_data = loader.calculate_race(self.sample_runners, self.test_distance)
        loader.race_data = race_data
        
        colors = loader.get_runner_colors()
        
        self.assertIsInstance(colors, dict)
        self.assertEqual(len(colors), len(self.sample_runners))
        
        # All runners should have colors assigned
        for runner in self.sample_runners:
            self.assertIn(runner['id'], colors)
            self.assertIsInstance(colors[runner['id']], tuple)
            self.assertEqual(len(colors[runner['id']]), 3)  # RGB tuple


class TestDataEdgeCases(unittest.TestCase):
    """Test edge cases and boundary conditions for data loading."""
    
    def test_extreme_runner_values(self):
        """Test handling of extreme runner parameter values."""
        extreme_runners = [
            {"id": 1, "max_speed": 0.1, "acceleration": 0.01, "endurance": 0.01},  # Very slow
            {"id": 2, "max_speed": 50.0, "acceleration": 20.0, "endurance": 1.0},  # Very fast
            {"id": 3, "max_speed": 0, "acceleration": 0, "endurance": 0},  # Zero values
            {"id": 4, "max_speed": -10, "acceleration": -5, "endurance": -0.5},  # Negative values
        ]
        
        loader = DataLoader()
        race_data = loader.calculate_race(extreme_runners, 100.0)
        
        # Should handle gracefully without crashing
        self.assertIsNotNone(race_data)
        self.assertEqual(race_data['race_info']['total_runners'], len(extreme_runners))
    
    def test_duplicate_runner_ids(self):
        """Test handling of duplicate runner IDs."""
        duplicate_runners = [
            {"id": 1, "max_speed": 12.5, "acceleration": 2.8, "endurance": 0.85},
            {"id": 1, "max_speed": 11.8, "acceleration": 3.2, "endurance": 0.90},  # Duplicate ID
            {"id": 2, "max_speed": 13.1, "acceleration": 2.5, "endurance": 0.80},
        ]
        
        loader = DataLoader()
        race_data = loader.calculate_race(duplicate_runners, 100.0)
        
        # Should handle gracefully
        self.assertIsNotNone(race_data)
        
    def test_very_long_race_distance(self):
        """Test handling of very long race distances."""
        loader = DataLoader()
        race_data = loader.calculate_race(self.sample_runners, 10000.0)  # 10km
        
        self.assertIsNotNone(race_data)
        self.assertEqual(race_data['race_info']['distance'], 10000.0)


if __name__ == '__main__':
    unittest.main()