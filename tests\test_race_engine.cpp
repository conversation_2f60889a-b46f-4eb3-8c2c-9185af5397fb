#include <iostream>
#include <cassert>
#include <memory>
#include <vector>
#include <chrono>
#include <thread>
#include "race_engine.h"
#include "runner_attributes.h"
#include "race_simulator.h"

class TestRaceEngine : public RaceEngine {
public:
    // Expose protected methods for testing
    using RaceEngine::generateTrackSegment;
    using RaceEngine::calculateRunnerPosition;
    using RaceEngine::updateAnimation;
};

void test_generateTrackSegment() {
    TestRaceEngine engine;
    
    // Test basic track generation
    TrackSegment segment = engine.generateTrackSegment(0, 100.0f);
    assert(segment.startDistance == 0.0f);
    assert(segment.endDistance == 100.0f);
    assert(segment.type >= 0 && segment.type <= 2);
    
    std::cout << "test_generateTrackSegment PASSED" << std::endl;
}

void test_calculateRunnerPosition() {
    TestRaceEngine engine;
    
    // Test position calculation
    float time = 10.0f;
    float totalDistance = 100.0f;
    float expectedPosition = 50.0f;  // Halfway
    
    float position = engine.calculateRunnerPosition(time, totalDistance);
    assert(position >= 0.0f && position <= 100.0f);
    
    std::cout << "test_calculateRunnerPosition PASSED" << std::endl;
}

void test_race_initialization() {
    TestRaceEngine engine;
    
    // Test runner creation
    std::vector<RunnerAttributes> runners;
    
    RunnerAttributes runner1;
    runner1.speed = 150;
    runner1.power = 120;
    runner1.intelligence = 100;
    
    RunnerAttributes runner2;
    runner2.speed = 130;
    runner2.power = 140;
    runner2.intelligence = 110;
    
    runners.push_back(runner1);
    runners.push_back(runner2);
    
    assert(runners.size() == 2);
    assert(runners[0].speed == 150);
    assert(runners[1].power == 140);
    
    std::cout << "test_race_initialization PASSED" << std::endl;
}

void test_animation_frame_rate() {
    TestRaceEngine engine;
    
    // Test frame rate calculation
    auto start = std::chrono::steady_clock::now();
    
    // Simulate 60 frames
    for (int i = 0; i < 60; i++) {
        engine.updateAnimation();
        std::this_thread::sleep_for(std::chrono::milliseconds(16)); // ~60 FPS
    }
    
    auto end = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();
    
    // Should take approximately 960ms (60 * 16ms)
    assert(duration >= 950 && duration <= 1000);
    
    std::cout << "test_animation_frame_rate PASSED" << std::endl;
}

void test_runner_attributes() {
    // Test runner attribute validation
    RunnerAttributes runner;
    runner.speed = 200;
    runner.power = 180;
    runner.intelligence = 160;
    
    // Ensure attributes are within valid range
    assert(runner.speed <= 255 && runner.speed >= 0);
    assert(runner.power <= 255 && runner.power >= 0);
    assert(runner.intelligence <= 255 && runner.intelligence >= 0);
    
    std::cout << "test_runner_attributes PASSED" << std::endl;
}

void test_track_segments() {
    TestRaceEngine engine;
    
    // Test track segment creation
    std::vector<TrackSegment> segments;
    
    float totalDistance = 100.0f;
    float segmentSize = 20.0f;
    
    for (float start = 0.0f; start < totalDistance; start += segmentSize) {
        float end = std::min(start + segmentSize, totalDistance);
        TrackSegment segment = engine.generateTrackSegment(start, end);
        segments.push_back(segment);
    }
    
    assert(segments.size() == 5);  // 100 / 20 = 5 segments
    assert(segments[0].startDistance == 0.0f);
    assert(segments[4].endDistance == 100.0f);
    
    std::cout << "test_track_segments PASSED" << std::endl;
}

int main() {
    std::cout << "Running tests for Race Engine..." << std::endl;
    
    test_generateTrackSegment();
    test_calculateRunnerPosition();
    test_race_initialization();
    test_runner_attributes();
    test_track_segments();
    test_animation_frame_rate();
    
    std::cout << "All Race Engine tests finished successfully." << std::endl;
    return 0;
}