#include <iostream>
#include <cassert>
#include <cmath>
#include <vector>
#include <numeric>
#include "skill_system.h"
#include "runner_attributes.h"

// A simple assertion function for floating point comparisons
void assert_close(float a, float b, const std::string& message) {
    if (std::abs(a - b) > 1e-5) {
        std::cerr << "Asser<PERSON> failed: " << message << std::endl;
        std::cerr << "  Expected: " << b << ", Got: " << a << std::endl;
        assert(false);
    }
}

RunnerAttributes CreateRunner(uint8_t speed, uint8_t power, uint8_t intelligence) {
    RunnerAttributes runner;
    runner.speed = speed;
    runner.power = power;
    runner.intelligence = intelligence;
    return runner;
}

void test_CalculateStartWisdom() {
    auto runner = CreateRunner(100, 100, 100);
    auto effect_triggered = RaceSimulator::CalculateStartWisdom(&runner, true);
    assert(effect_triggered.triggered);
    assert_close(effect_triggered.reactionTimeReduction, 0.3f, "CalculateStartWisdom: Triggered reduction");

    auto effect_not_triggered = RaceSimulator::CalculateStartWisdom(&runner, false);
    assert(!effect_not_triggered.triggered);
    assert_close(effect_not_triggered.reactionTimeReduction, 0.0f, "CalculateStartWisdom: Not triggered reduction");
    std::cout << "test_CalculateStartWisdom PASSED" << std::endl;
}

void test_ApplyStartWisdom() {
    RaceSimulator::StartWisdomEffect effect_triggered = { 0.3f, true };
    float originalTime = 0.5f;
    float expectedTime = (0.5f - 0.1f) * 0.7f + 0.1f;
    float newTime = RaceSimulator::ApplyStartWisdom(originalTime, effect_triggered);
    assert_close(newTime, expectedTime, "ApplyStartWisdom: Triggered");

    RaceSimulator::StartWisdomEffect effect_not_triggered = { 0.0f, false };
    newTime = RaceSimulator::ApplyStartWisdom(originalTime, effect_not_triggered);
    assert_close(newTime, originalTime, "ApplyStartWisdom: Not triggered");
    std::cout << "test_ApplyStartWisdom PASSED" << std::endl;
}

void test_CalculateMidRaceAccel() {
    auto runner = CreateRunner(255, 255, 255);
    auto effect = RaceSimulator::CalculateMidRaceAccel(&runner, true);
    float expectedBoost = 0.5f + 1.0f * (255.0f / 255.0f) * (255.0f / 255.0f);
    assert(effect.triggered);
    assert_close(effect.speedBoost, expectedBoost, "CalculateMidRaceAccel: Triggered boost");
    assert_close(effect.triggerStart, 30.0f, "CalculateMidRaceAccel: Trigger start");
    assert_close(effect.triggerEnd, 70.0f, "CalculateMidRaceAccel: Trigger end");

    auto effect_not_triggered = RaceSimulator::CalculateMidRaceAccel(&runner, false);
    assert(!effect_not_triggered.triggered);
    assert_close(effect_not_triggered.speedBoost, 0.0f, "CalculateMidRaceAccel: Not triggered boost");
    std::cout << "test_CalculateMidRaceAccel PASSED" << std::endl;
}

void test_ApplyMidRaceAccel() {
    RaceSimulator::MidRaceAccelEffect effect = { 1.5f, 30.0f, 70.0f, true };
    float segmentTime = 10.0f;

    // No overlap
    float newTime_no_overlap = RaceSimulator::ApplyMidRaceAccel(segmentTime, 10.0f, 20.0f, effect);
    assert_close(newTime_no_overlap, segmentTime, "ApplyMidRaceAccel: No overlap");

    // Full overlap
    float newTime_full_overlap = RaceSimulator::ApplyMidRaceAccel(segmentTime, 40.0f, 50.0f, effect);
    float expected_reduction_full = (1.5f / 10.0f) * 1.0f;
    assert_close(newTime_full_overlap, segmentTime - expected_reduction_full, "ApplyMidRaceAccel: Full overlap");
    
    // Partial overlap
    float newTime_partial_overlap = RaceSimulator::ApplyMidRaceAccel(segmentTime, 60.0f, 80.0f, effect);
    float overlap_ratio_partial = (70.0f - 60.0f) / (80.0f - 60.0f);
    float expected_reduction_partial = (1.5f / 10.0f) * overlap_ratio_partial;
    assert_close(newTime_partial_overlap, segmentTime - expected_reduction_partial, "ApplyMidRaceAccel: Partial overlap");
    
    std::cout << "test_ApplyMidRaceAccel PASSED" << std::endl;
}

void test_CalculateLateRacePower() {
    auto runner = CreateRunner(100, 100, 100);
    auto effect = RaceSimulator::CalculateLateRacePower(&runner, true);
    assert(effect.triggered);
    assert_close(effect.powerBoost, 30.0f, "CalculateLateRacePower: Triggered boost");
    assert_close(effect.triggerDistance, 60.0f, "CalculateLateRacePower: Trigger distance");

    auto effect_not_triggered = RaceSimulator::CalculateLateRacePower(&runner, false);
    assert(!effect_not_triggered.triggered);
    assert_close(effect_not_triggered.powerBoost, 0.0f, "CalculateLateRacePower: Not triggered boost");
    std::cout << "test_CalculateLateRacePower PASSED" << std::endl;
}

void test_ApplyLateRacePower() {
    RaceSimulator::LateRacePowerEffect effect = { 30.0f, 60.0f, true };
    float segmentTime = 10.0f;
    uint8_t originalPower = 100;

    // Before trigger
    float newTime_before = RaceSimulator::ApplyLateRacePower(segmentTime, 40.0f, 50.0f, effect, originalPower);
    assert_close(newTime_before, segmentTime, "ApplyLateRacePower: Before trigger");

    // Full overlap
    float newTime_full = RaceSimulator::ApplyLateRacePower(segmentTime, 70.0f, 80.0f, effect, originalPower);
    float powerIncrease = (100.0f + 30.0f) / 100.0f;
    float powerFactor = 1.0f / (1.0f + (powerIncrease - 1.0f) * 0.3f);
    float expectedTime_full = segmentTime * (1.0f - 1.0f * (1.0f - powerFactor));
    assert_close(newTime_full, expectedTime_full, "ApplyLateRacePower: Full overlap");

    // Partial overlap
    float newTime_partial = RaceSimulator::ApplyLateRacePower(segmentTime, 50.0f, 70.0f, effect, originalPower);
    float ratio_partial = (70.0f - 60.0f) / (70.0f - 50.0f);
    float expectedTime_partial = segmentTime * (1.0f - ratio_partial * (1.0f - powerFactor));
    assert_close(newTime_partial, expectedTime_partial, "ApplyLateRacePower: Partial overlap");

    std::cout << "test_ApplyLateRacePower PASSED" << std::endl;
}

void test_CalculateSkillImpact() {
    auto runner = CreateRunner(255, 100, 255);
    bool triggers_none[] = {false, false, false};
    float baseTimes[] = {0.5f, 2.0f, 3.0f, 3.0f, 2.5f};
    const float segmentDistances[] = {0.0f, 20.0f, 40.0f, 70.0f, 100.0f};
    
    float impact_none = RaceSimulator::CalculateSkillImpact(&runner, triggers_none, baseTimes, segmentDistances);
    assert_close(impact_none, 0.0f, "CalculateSkillImpact: No skills triggered");

    bool triggers_all[] = {true, true, true}; // Corresponds to: [MidRace, StartWisdom, LatePower]
    
    // Manually calculate expected impact for verification, mimicking CalculateSkillImpact's logic
    float expected_total_impact = 0.0f;

    // Wisdom (triggers)
    if (triggers_all[1]) {
        RaceSimulator::StartWisdomEffect wisdom_effect = RaceSimulator::CalculateStartWisdom(&runner, triggers_all[1]);
        expected_total_impact += RaceSimulator::ApplyStartWisdom(baseTimes[0], wisdom_effect) - baseTimes[0];
    }

    // Accel (triggers)
    if (triggers_all[0]) {
        RaceSimulator::MidRaceAccelEffect accel_effect = RaceSimulator::CalculateMidRaceAccel(&runner, triggers_all[0]);
        for (int i = 1; i < 5; ++i) {
            expected_total_impact += RaceSimulator::ApplyMidRaceAccel(baseTimes[i], segmentDistances[i-1], segmentDistances[i], accel_effect) - baseTimes[i];
        }
    }

    // Power (triggers)
    if (triggers_all[2]) {
        RaceSimulator::LateRacePowerEffect power_effect = RaceSimulator::CalculateLateRacePower(&runner, triggers_all[2]);
         for (int i = 1; i < 5; ++i) {
            if (segmentDistances[i] > power_effect.triggerDistance) {
                expected_total_impact += RaceSimulator::ApplyLateRacePower(baseTimes[i], segmentDistances[i-1], segmentDistances[i], power_effect, runner.power) - baseTimes[i];
            }
        }
    }
    
    float actual_total_impact = RaceSimulator::CalculateSkillImpact(&runner, triggers_all, baseTimes, segmentDistances);
    assert_close(actual_total_impact, expected_total_impact, "CalculateSkillImpact: All skills triggered");

    std::cout << "test_CalculateSkillImpact PASSED" << std::endl;
}


int main() {
    std::cout << "Running tests for Skill System..." << std::endl;
    
    test_CalculateStartWisdom();
    test_ApplyStartWisdom();
    test_CalculateMidRaceAccel();
    test_ApplyMidRaceAccel();
    test_CalculateLateRacePower();
    test_ApplyLateRacePower();
    test_CalculateSkillImpact();

    std::cout << "All Skill System tests finished successfully." << std::endl;
    return 0;
}
