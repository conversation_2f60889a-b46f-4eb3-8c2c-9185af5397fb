# Build Verification Script for ASCII Running Simulation
# This script verifies that the build system works correctly

param(
    [switch]$Verbose = $false,
    [switch]$Quick = $false
)

$ErrorActionPreference = "Stop"

Write-Host "ASCII Running Simulation - Build Verification" -ForegroundColor Cyan
Write-Host "============================================" -ForegroundColor Cyan

$verificationResults = @()

function Test-Environment {
    Write-Host "1. Checking Environment..." -ForegroundColor Yellow
    
    $results = @{
        Name = "Environment"
        Status = "PASS"
        Details = @()
    }
    
    # Check Visual Studio
    $vswhere = "${env:ProgramFiles(x86)}\Microsoft Visual Studio\Installer\vswhere.exe"
    if (Test-Path $vswhere) {
        $vsPath = & $vswhere -latest -products * -requires Microsoft.Component.MSBuild -property installationPath
        if ($vsPath) {
            $results.Details += "Visual Studio: $vsPath"
        } else {
            $results.Status = "FAIL"
            $results.Details += "Visual Studio not found"
        }
    } else {
        $results.Status = "FAIL"
        $results.Details += "VSWhere not found"
    }
    
    # Check CMake
    $cmake = Get-Command "cmake.exe" -ErrorAction SilentlyContinue
    if ($cmake) {
        $version = & cmake --version | Select-Object -First 1
        $results.Details += "CMake: $version"
    } else {
        $results.Status = "FAIL"
        $results.Details += "CMake not found"
    }
    
    # Check MSBuild
    $msbuild = Find-MSBuild
    if ($msbuild) {
        $results.Details += "MSBuild: $msbuild"
    } else {
        $results.Status = "FAIL"
        $results.Details += "MSBuild not found"
    }
    
    $verificationResults += $results
    Write-Host "   Status: $($results.Status)" -ForegroundColor $(if ($results.Status -eq "PASS") { "Green" } else { "Red" })
    return $results.Status -eq "PASS"
}

function Find-MSBuild {
    $vswhere = "${env:ProgramFiles(x86)}\Microsoft Visual Studio\Installer\vswhere.exe"
    if (Test-Path $vswhere) {
        $vsPath = & $vswhere -latest -products * -requires Microsoft.Component.MSBuild -property installationPath
        if ($vsPath) {
            $msbuild = Join-Path $vsPath "MSBuild\Current\Bin\MSBuild.exe"
            if (Test-Path $msbuild) {
                return $msbuild
            }
        }
    }
    return $null
}

function Test-Build {
    Write-Host "2. Testing Build Process..." -ForegroundColor Yellow
    
    $results = @{
        Name = "Build Process"
        Status = "PASS"
        Details = @()
    }
    
    try {
        # Clean previous build
        if (Test-Path "build") {
            Remove-Item "build" -Recurse -Force
        }
        
        # Build with PowerShell script
        Write-Host "   Running build.ps1..." -ForegroundColor Gray
        & .\build.ps1 -Configuration Release -Platform x64
        
        if ($LASTEXITCODE -ne 0) {
            throw "Build script failed"
        }
        
        # Check if files were created
        $expectedFiles = @(
            "bin\AsciiRaceSimulator.exe",
            "lib\RaceSimulation.dll"
        )
        
        foreach ($file in $expectedFiles) {
            if (Test-Path $file) {
                $size = (Get-Item $file).Length
                $results.Details += "$file ($("{0:N0}" -f $size) bytes)"
            } else {
                $results.Status = "FAIL"
                $results.Details += "$file NOT FOUND"
            }
        }
        
    } catch {
        $results.Status = "FAIL"
        $results.Details += $_.Exception.Message
    }
    
    $verificationResults += $results
    Write-Host "   Status: $($results.Status)" -ForegroundColor $(if ($results.Status -eq "PASS") { "Green" } else { "Red" })
    return $results.Status -eq "PASS"
}

function Test-DLLIntegration {
    Write-Host "3. Testing DLL Integration..." -ForegroundColor Yellow
    
    $results = @{
        Name = "DLL Integration"
        Status = "PASS"
        Details = @()
    }
    
    try {
        # Test DLL loading
        $dllPath = Resolve-Path "lib\RaceSimulation.dll"
        
        if (-not (Test-Path $dllPath)) {
            throw "DLL not found at $dllPath"
        }
        
        # Test basic DLL functionality
        $testExe = "tests\test_skill_system.exe"
        if (Test-Path $testExe) {
            $output = & $testExe 2>&1
            if ($LASTEXITCODE -eq 0) {
                $results.Details += "Skill system tests passed"
            } else {
                $results.Status = "FAIL"
                $results.Details += "Skill system tests failed"
            }
        } else {
            $results.Status = "FAIL"
            $results.Details += "Skill system test executable not found"
        }
        
    } catch {
        $results.Status = "FAIL"
        $results.Details += $_.Exception.Message
    }
    
    $verificationResults += $results
    Write-Host "   Status: $($results.Status)" -ForegroundColor $(if ($results.Status -eq "PASS") { "Green" } else { "Red" })
    return $results.Status -eq "PASS"
}

function Test-Functionality {
    Write-Host "4. Testing Functionality..." -ForegroundColor Yellow
    
    if ($Quick) {
        Write-Host "   Skipping functionality test (quick mode)" -ForegroundColor Gray
        return $true
    }
    
    $results = @{
        Name = "Functionality"
        Status = "PASS"
        Details = @()
    }
    
    try {
        # Test basic execution
        $exePath = "bin\AsciiRaceSimulator.exe"
        if (-not (Test-Path $exePath)) {
            throw "Executable not found at $exePath"
        }
        
        # Run with test mode
        $process = Start-Process -FilePath $exePath -ArgumentList "--test-mode" -PassThru -NoNewWindow -Wait -Timeout 10
        
        if ($process.ExitCode -eq 0) {
            $results.Details += "Basic execution test passed"
        } else {
            $results.Status = "FAIL"
            $results.Details += "Basic execution test failed (exit code: $($process.ExitCode))"
        }
        
    } catch {
        $results.Status = "FAIL"
        $results.Details += $_.Exception.Message
    }
    
    $verificationResults += $results
    Write-Host "   Status: $($results.Status)" -ForegroundColor $(if ($results.Status -eq "PASS") { "Green" } else { "Red" })
    return $results.Status -eq "PASS"
}

function Test-Performance {
    Write-Host "5. Testing Performance..." -ForegroundColor Yellow
    
    $results = @{
        Name = "Performance"
        Status = "PASS"
        Details = @()
    }
    
    try {
        # Test startup time
        $exePath = "bin\AsciiRaceSimulator.exe"
        
        $startTime = Get-Date
        $process = Start-Process -FilePath $exePath -ArgumentList "--test-mode" -PassThru -NoNewWindow -Wait -Timeout 5
        $endTime = Get-Date
        
        $startupTime = ($endTime - $startTime).TotalSeconds
        
        if ($startupTime -lt 2.0) {
            $results.Details += "Startup time: $([math]::Round($startupTime, 2))s (good)"
        } else {
            $results.Status = "WARNING"
            $results.Details += "Startup time: $([math]::Round($startupTime, 2))s (slow)"
        }
        
        # Test file sizes
        $exeSize = (Get-Item $exePath).Length
        $dllSize = (Get-Item "lib\RaceSimulation.dll").Length
        
        $totalSize = $exeSize + $dllSize
        $results.Details += "Total size: $([math]::Round($totalSize/1MB, 2)) MB"
        
    } catch {
        $results.Status = "FAIL"
        $results.Details += $_.Exception.Message
    }
    
    $verificationResults += $results
    Write-Host "   Status: $($results.Status)" -ForegroundColor $(if ($results.Status -eq "PASS") { "Green" } elseif ($results.Status -eq "WARNING") { "Yellow" } else { "Red" })
    return $results.Status -eq "PASS" -or $results.Status -eq "WARNING"
}

function Show-Summary {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "VERIFICATION SUMMARY" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
    
    $allPassed = $true
    
    foreach ($result in $verificationResults) {
        $color = if ($result.Status -eq "PASS") { "Green" } 
                 elseif ($result.Status -eq "WARNING") { "Yellow" } 
                 else { "Red" }
        
        Write-Host "$("{0,-20}" -f $result.Name): $($result.Status)" -ForegroundColor $color
        
        if ($Verbose) {
            foreach ($detail in $result.Details) {
                Write-Host "  $detail" -ForegroundColor Gray
            }
        }
        
        if ($result.Status -eq "FAIL") {
            $allPassed = $false
        }
    }
    
    Write-Host ""
    if ($allPassed) {
        Write-Host "🎉 ALL VERIFICATIONS PASSED!" -ForegroundColor Green
        Write-Host "The build system is ready for development." -ForegroundColor Green
    } else {
        Write-Host "❌ SOME VERIFICATIONS FAILED!" -ForegroundColor Red
        Write-Host "Please check the issues above." -ForegroundColor Red
    }
    
    return $allPassed
}

# Main execution
$success = $true

$success = $success -and (Test-Environment)
$success = $success -and (Test-Build)
$success = $success -and (Test-DLLIntegration)
$success = $success -and (Test-Functionality)
$success = $success -and (Test-Performance)

$finalSuccess = Show-Summary

exit $finalSuccess ? 0 : 1