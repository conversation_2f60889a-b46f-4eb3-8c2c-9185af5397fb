#include <iostream>
#include <vector>
#include <string>
#include <chrono>
#include <thread>
#include <windows.h>
#include <algorithm>
#include <iomanip>
#include <cstdlib>
#include <ctime>
#include <cmath>
#include "../include/console_utils.h"

// Simplified ASCII Running Simulator
class AsciiRaceSimulator {
private:
    struct Runner {
        char symbol;
        std::string name;
        double position;
        double baseSpeed;           // 基础速度 (m/s)
        double currentSpeed;        // 当前速度 (考虑疲劳等因素)
        double totalTime;           // 总用时
        double finishTime;          // 完成时间 (如果完成的话)
        bool finished;              // 是否完成比赛
        double maxSpeed;            // 最高速度
        double minSpeed;            // 最低速度
        double averageSpeed;        // 平均速度
        int rank;                   // 排名
    };

    std::vector<Runner> runners;
    double raceDistance = 100.0;
    bool raceFinished = false;
    
public:
    AsciiRaceSimulator() {
        // Initialize 5 runners with realistic speeds
        // Target finish times: 9.5-11.5 seconds (world class to good amateur)
        // World record: 9.58s (<PERSON><PERSON>), Average speed: 10.44 m/s
        srand(static_cast<unsigned>(time(nullptr)));

        std::vector<std::string> names = {"Usain Bolt", "<PERSON> <PERSON>", "<PERSON> <PERSON>", "<PERSON> Gatlin", "<PERSON> Gay"};
        std::vector<char> symbols = {'A', 'B', 'C', 'D', 'E'};

        for (int i = 0; i < 5; i++) {
            Runner runner;
            runner.symbol = symbols[i];
            runner.name = names[i];
            runner.position = 0.0;
            // 设置更现实的速度范围 (基于真实100m成绩 9.5-11.5秒)
            // 平均速度应该在 8.7-10.5 m/s 之间
            runner.baseSpeed = 8.7 + (rand() % 18) / 10.0; // 8.7-10.4 m/s
            runner.currentSpeed = 0.0; // 起跑时速度为0
            runner.totalTime = 0.0;
            runner.finishTime = 0.0;
            runner.finished = false;
            runner.maxSpeed = runner.baseSpeed * 1.05; // 最高速度只比平均速度高5%
            runner.minSpeed = runner.baseSpeed * 0.6;  // 起跑和疲劳时的最低速度
            runner.averageSpeed = 0.0;
            runner.rank = 0;
            runners.push_back(runner);
        }
    }

    void clearScreen() {
        // Use ANSI escape sequences for flicker-free clearing
        ConsoleUtils console;
        console.clearScreenANSI();
    }

    void clearScreenOptimized() {
        // Move cursor to top and clear only what's needed
        ConsoleUtils console;
        console.setCursorPositionANSI(0, 0);
        console.clearFromCursorANSI();
    }

    void setCursorPosition(int x, int y) {
        COORD coord = {(SHORT)x, (SHORT)y};
        SetConsoleCursorPosition(GetStdHandle(STD_OUTPUT_HANDLE), coord);
    }

    void drawTrack() {
        const int trackWidth = 80;

        // Use optimized ANSI clearing - move to top and clear from cursor
        ConsoleUtils console;
        console.setCursorPositionANSI(0, 0);

        std::cout << "======================== ASCII Race Simulator ========================\n";
        std::cout << "Distance: " << raceDistance << "m | Refresh Rate: 60 Hz | Frame Time: 16.67ms\n\n";
        
        // 绘制赛道 - 使用覆盖打印避免闪烁
        for (int lane = 0; lane < 5; lane++) {
            // 移动到当前行的开始位置
            console.setCursorPositionANSI(0, 3 + lane);

            // 先清除整行，然后重新绘制
            console.clearLineANSI();
            console.setCursorPositionANSI(0, 3 + lane);

            std::cout << runners[lane].symbol << ": ";

            int currentPos = static_cast<int>((runners[lane].position / raceDistance) * trackWidth);
            for (int i = 0; i < trackWidth; i++) {
                if (i == currentPos) {
                    if (runners[lane].finished) {
                        std::cout << "*"; // 完成标记
                    } else {
                        std::cout << runners[lane].symbol;
                    }
                } else if (i == trackWidth - 1) {
                    std::cout << "|"; // 终点线
                } else {
                    std::cout << "-";
                }
            }

            // 显示详细信息
            std::cout << " " << std::fixed << std::setprecision(1) << runners[lane].position << "m";
            if (runners[lane].finished) {
                std::cout << " [FINISHED: " << std::setprecision(2) << runners[lane].finishTime << "s]";
            } else {
                std::cout << " [" << std::setprecision(1) << runners[lane].currentSpeed << " m/s]";
            }
        }
        
        // 显示状态信息和实时统计
        console.setCursorPositionANSI(0, 9);
        console.clearLineANSI();
        console.setCursorPositionANSI(0, 9);

        if (!raceFinished) {
            // 找到当前领先者
            Runner leader = runners[0];
            for (const auto& runner : runners) {
                if (runner.position > leader.position) {
                    leader = runner;
                }
            }

            std::cout << "Race Time: " << std::fixed << std::setprecision(2) << leader.totalTime
                      << "s | Leader: " << leader.name << " (" << std::setprecision(1)
                      << leader.position << "m)";
        } else {
            std::cout << "Race finished! Press any key to view detailed results";
        }

        // 显示完成者信息
        console.setCursorPositionANSI(0, 10);
        console.clearLineANSI();
        console.setCursorPositionANSI(0, 10);

        int finishedCount = 0;
        for (const auto& runner : runners) {
            if (runner.finished) finishedCount++;
        }

        if (finishedCount > 0) {
            std::cout << "Finished: " << finishedCount << "/" << runners.size() << " runners";
        }
    }

    void updateRace(double deltaTime) {
        bool allFinished = true;

        for (auto& runner : runners) {
            if (!runner.finished) {
                // 模拟真实的跑步动态：起跑、加速、维持、疲劳
                double progress = runner.position / raceDistance;

                // 真实的短跑速度曲线模拟
                if (progress < 0.15) {
                    // 起跑和加速阶段 (0-15m)：从0逐渐加速到最高速度
                    double accelProgress = progress / 0.15;
                    runner.currentSpeed = runner.maxSpeed * accelProgress;
                } else if (progress < 0.6) {
                    // 最高速度维持阶段 (15-60m)：维持在最高速度附近
                    double variation = (rand() % 11 - 5) / 100.0; // -5% to +5%
                    runner.currentSpeed = runner.maxSpeed * (1.0 + variation);
                } else if (progress < 0.8) {
                    // 轻微疲劳阶段 (60-80m)：速度开始轻微下降
                    double fatigueStart = (progress - 0.6) / 0.2; // 0 to 1
                    runner.currentSpeed = runner.maxSpeed * (1.0 - fatigueStart * 0.05);
                } else {
                    // 明显疲劳阶段 (80-100m)：速度明显下降
                    double fatigue = (progress - 0.8) / 0.2; // 0 to 1
                    runner.currentSpeed = runner.maxSpeed * (0.95 - fatigue * 0.15);
                }

                // 限制速度范围
                if (runner.currentSpeed > runner.maxSpeed) {
                    runner.currentSpeed = runner.maxSpeed;
                }
                if (runner.currentSpeed < runner.minSpeed) {
                    runner.currentSpeed = runner.minSpeed;
                }

                // 更新位置和时间
                runner.position += runner.currentSpeed * deltaTime;
                runner.totalTime += deltaTime;

                // 检查是否完成比赛
                if (runner.position >= raceDistance) {
                    runner.position = raceDistance;
                    runner.finished = true;
                    runner.finishTime = runner.totalTime;
                    runner.averageSpeed = raceDistance / runner.totalTime;
                } else {
                    allFinished = false;
                }

                // 更新最高和最低速度记录
                if (runner.currentSpeed > runner.maxSpeed) {
                    runner.maxSpeed = runner.currentSpeed;
                }
                if (runner.currentSpeed < runner.minSpeed) {
                    runner.minSpeed = runner.currentSpeed;
                }
            }
        }

        raceFinished = allFinished;

        // 如果比赛结束，计算排名
        if (raceFinished) {
            calculateRankings();
        }
    }

    void calculateRankings() {
        // 创建一个包含索引的向量用于排序
        std::vector<std::pair<double, int>> finishTimes;
        for (int i = 0; i < runners.size(); i++) {
            finishTimes.push_back({runners[i].finishTime, i});
        }

        // 按完成时间排序
        std::sort(finishTimes.begin(), finishTimes.end());

        // 分配排名
        for (int i = 0; i < finishTimes.size(); i++) {
            int runnerIndex = finishTimes[i].second;
            runners[runnerIndex].rank = i + 1;
        }
    }

    void displayResults() {
        ConsoleUtils console;
        console.clearScreenANSI();
        console.showCursorANSI();

        std::cout << "==================== RACE RESULTS ====================\n";
        std::cout << "Distance: " << raceDistance << "m Sprint Championship\n\n";

        // Sort by rank (which was calculated based on finish time)
        std::sort(runners.begin(), runners.end(),
                  [](const Runner& a, const Runner& b) {
                      return a.rank < b.rank;
                  });

        // Display detailed results
        std::cout << "FINAL STANDINGS:\n";
        std::cout << "================\n";
        for (const auto& runner : runners) {
            std::cout << std::fixed;
            std::cout << "#" << runner.rank << " " << runner.name
                      << " (" << runner.symbol << ")\n";
            std::cout << "   Finish Time: " << std::setprecision(3) << runner.finishTime << "s\n";
            std::cout << "   Average Speed: " << std::setprecision(2) << runner.averageSpeed << " m/s\n";
            std::cout << "   Top Speed: " << std::setprecision(2) << runner.maxSpeed << " m/s\n";
            std::cout << "\n";
        }

        // Display race statistics
        std::cout << "RACE STATISTICS:\n";
        std::cout << "================\n";

        // Find winner and slowest manually
        Runner winner = runners[0];
        Runner slowest = runners[0];
        for (const auto& runner : runners) {
            if (runner.finishTime < winner.finishTime) {
                winner = runner;
            }
            if (runner.finishTime > slowest.finishTime) {
                slowest = runner;
            }
        }

        std::cout << "Winner: " << winner.name << " (" << std::setprecision(3) << winner.finishTime << "s)\n";
        std::cout << "Winning Margin: " << std::setprecision(3)
                  << (runners[1].finishTime - winner.finishTime) << "s\n";
        std::cout << "Race Duration: " << std::setprecision(3) << slowest.finishTime << "s\n";

        double avgTime = 0;
        double avgSpeed = 0;
        for (const auto& runner : runners) {
            avgTime += runner.finishTime;
            avgSpeed += runner.averageSpeed;
        }
        avgTime /= runners.size();
        avgSpeed /= runners.size();

        std::cout << "Average Finish Time: " << std::setprecision(3) << avgTime << "s\n";
        std::cout << "Average Speed: " << std::setprecision(2) << avgSpeed << " m/s\n";

        std::cout << "\nPress any key to exit..." << std::endl;
        std::cin.ignore();
    }

    void runRace() {
        // Get display refresh rate and calculate frame time
        ConsoleUtils console;
        int refreshRate = console.getDisplayRefreshRate();
        const double frameTime = 1.0 / refreshRate;

        // Hide cursor for better visual experience
        console.hideCursorANSI();

        auto lastTime = std::chrono::steady_clock::now();
        auto targetFrameTime = std::chrono::duration<double>(frameTime);

        while (!raceFinished) {
            auto currentTime = std::chrono::steady_clock::now();
            auto deltaTime = std::chrono::duration<double>(currentTime - lastTime);

            if (deltaTime >= targetFrameTime) {
                updateRace(frameTime);
                drawTrack();
                lastTime = currentTime;
            } else {
                // Sleep for a short time to prevent busy waiting
                auto sleepTime = targetFrameTime - deltaTime;
                if (sleepTime > std::chrono::microseconds(100)) {
                    std::this_thread::sleep_for(sleepTime - std::chrono::microseconds(100));
                }
            }
        }
        
        // 显示最终结果
        drawTrack();
        std::this_thread::sleep_for(std::chrono::seconds(2));

        // Show cursor before displaying results
        console.showCursorANSI();
        displayResults();
    }
};

int main() {
    // Setup UTF-8 console for proper character display
    ConsoleUtils console;
    console.setupUtf8Console();

    // Display refresh rate information
    int refreshRate = console.getDisplayRefreshRate();
    double frameTime = 1.0 / refreshRate;

    std::cout << "ASCII Race Simulator v1.0" << std::endl;
    std::cout << "=========================" << std::endl;
    std::cout << "Display refresh rate: " << refreshRate << " Hz" << std::endl;
    std::cout << "Frame time: " << (frameTime * 1000.0) << " ms" << std::endl;
    std::cout << "Animation will sync to " << refreshRate << " FPS" << std::endl;
    std::cout << "=========================" << std::endl;
    std::cout << "Press Enter to start race..." << std::endl;
    std::cin.ignore();

    AsciiRaceSimulator simulator;
    simulator.runRace();

    return 0;
}