@echo off
echo Complete System Test - ASCII Running Simulation
echo =================================================
echo.

echo STEP 1: Environment Check
echo -------------------------
where cmake >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ CMake not found. Please install CMake from https://cmake.org
    pause
    exit /b 1
)

where cl >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Visual Studio compiler not found. Please install Visual Studio.
    pause
    exit /b 1
)

echo ✅ Environment check passed

echo.
echo STEP 2: Creating Test Build
echo ---------------------------

REM Clean previous build
if exist build rmdir /s /q build
if exist bin rmdir /s /q bin
if exist lib rmdir /s /q lib

REM Create directories
mkdir build 2>nul
mkdir bin 2>nul
mkdir lib 2>nul

echo.
echo STEP 3: Building with CMake
echo ----------------------------

pushd build

REM Configure with CMake
echo Configuring CMake for MSBuild...
cmake .. -G "Visual Studio 17 2022" -A x64 -DCMAKE_BUILD_TYPE=Release
if %errorlevel% neq 0 (
    echo ❌ CMake configuration failed
    popd
    pause
    exit /b 1
)

echo Building with MSBuild...
cmake --build . --config Release --parallel
if %errorlevel% neq 0 (
    echo ❌ Build failed
    popd
    pause
    exit /b 1
)

popd

echo.
echo STEP 4: Copying Files
echo ---------------------

REM Copy DLL and executable to standard locations
if exist "build\bin\Release\race_simulator.dll" (
    copy "build\bin\Release\race_simulator.dll" "lib\RaceSimulation.dll" >nul
    echo ✅ DLL copied to lib\RaceSimulation.dll
) else if exist "build\lib\Release\race_simulator.dll" (
    copy "build\lib\Release\race_simulator.dll" "lib\RaceSimulation.dll" >nul
    echo ✅ DLL copied to lib\RaceSimulation.dll
) else (
    echo ❌ race_simulator.dll not found
    pause
    exit /b 1
)

if exist "build\bin\Release\AsciiRaceSimulator.exe" (
    copy "build\bin\Release\AsciiRaceSimulator.exe" "bin\" >nul
    copy "lib\RaceSimulation.dll" "bin\RaceSimulation.dll" >nul
    echo ✅ Executable copied to bin\AsciiRaceSimulator.exe
) else (
    echo ❌ AsciiRaceSimulator.exe not found
    pause
    exit /b 1
)

echo.
echo STEP 5: Testing DLL Integration
echo --------------------------------

REM Test DLL loading
if exist "build\bin\Release\test_skill_system.exe" (
    echo Testing skill system...
    build\bin\Release\test_skill_system.exe
    if %errorlevel% neq 0 (
        echo ❌ Skill system tests failed
        pause
        exit /b 1
    )
    echo ✅ Skill system tests passed
) else (
    echo ⚠️  Skill system tests not found, skipping
)

echo.
echo STEP 6: Testing Basic Functionality
echo -----------------------------------

if exist "bin\AsciiRaceSimulator.exe" (
    echo Testing basic execution...
    bin\AsciiRaceSimulator.exe --test-mode 2>nul
    if %errorlevel% neq 0 (
        echo ⚠️  Basic execution test returned non-zero (might be normal)
    ) else (
        echo ✅ Basic execution test passed
    )
) else (
    echo ❌ Executable not found
    pause
    exit /b 1
)

echo.
echo STEP 7: File Verification
echo --------------------------

REM Verify file sizes
for %%i in ("bin\AsciiRaceSimulator.exe") do set EXE_SIZE=%%~zi
for %%i in ("lib\RaceSimulation.dll") do set DLL_SIZE=%%~zi

set /a TOTAL_KB=(%EXE_SIZE%+%DLL_SIZE%)/1024

echo File sizes:
echo   AsciiRaceSimulator.exe: %EXE_SIZE% bytes
echo   RaceSimulation.dll:   %DLL_SIZE% bytes
echo   Total:                %TOTAL_KB% KB

echo.
echo STEP 8: Summary
echo ---------------

echo ✅ BUILD SYSTEM VERIFICATION COMPLETE!
echo.
echo You can now run:
echo   bin\AsciiRaceSimulator.exe - to start the simulation
echo   build.bat release - to build using batch script
echo   build.ps1 -Run - to build and run using PowerShell
echo.
echo All build artifacts are ready for use.
pause