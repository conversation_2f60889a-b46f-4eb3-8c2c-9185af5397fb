#!/usr/bin/env python3
"""
Build script for creating standalone Windows executable.

This script uses PyInstaller to create a single executable file that includes
all dependencies and can run without Python installation.
"""

import os
import sys
import shutil
import subprocess
import json
from pathlib import Path


class BuildManager:
    """Manages the build process for creating standalone executable."""
    
    def __init__(self):
        """Initialize build manager."""
        self.project_root = Path(__file__).parent.absolute()
        self.src_dir = self.project_root / "src"
        self.build_dir = self.project_root / "build"
        self.dist_dir = self.project_root / "dist"
        self.spec_file = self.project_root / "race_simulator.spec"
        
    def check_requirements(self) -> bool:
        """
        Check if all required dependencies are installed.
        
        Returns:
            True if all requirements are met, False otherwise
        """
        try:
            import pygame
            print(f"✓ Pygame {pygame.version.ver} found")
        except ImportError:
            print("✗ Pygame not found. Install with: pip install pygame")
            return False
            
        try:
            import PyInstaller
            print(f"✓ PyInstaller {PyInstaller.__version__} found")
        except ImportError:
            print("✗ PyInstaller not found. Install with: pip install pyinstaller")
            return False
            
        return True
    
    def clean_build(self) -> None:
        """Clean previous build artifacts."""
        print("Cleaning build artifacts...")
        
        # Remove build directories
        for dir_path in [self.build_dir, self.dist_dir]:
            if dir_path.exists():
                shutil.rmtree(dir_path)
                print(f"Removed {dir_path}")
        
        # Remove spec file
        if self.spec_file.exists():
            self.spec_file.unlink()
            print(f"Removed {self.spec_file}")
    
    def create_config_file(self) -> None:
        """Create default configuration file."""
        config = {
            "window": {
                "width": 1200,
                "height": 800,
                "fps": 30
            },
            "race": {
                "default_distance": 100.0,
                "dll_path": None
            },
            "ui": {
                "show_fps": False,
                "auto_play": False
            }
        }
        
        config_file = self.project_root / "config.json"
        with open(config_file, 'w') as f:
            json.dump(config, f, indent=2)
        print(f"Created configuration file: {config_file}")
    
    def create_sample_data(self) -> None:
        """Create sample runner data file."""
        from src.race_simulator_types import create_sample_runners
        
        sample_data = {
            "description": "Sample runner data for race simulation",
            "race_distance": 100.0,
            "runners": create_sample_runners()
        }
        
        sample_file = self.project_root / "sample_runners.json"
        with open(sample_file, 'w') as f:
            json.dump(sample_data, f, indent=2)
        print(f"Created sample runner file: {sample_file}")
    
    def create_spec_file(self) -> None:
        """Create PyInstaller spec file for building executable."""
        spec_content = '''# -*- mode: python ; coding: utf-8 -*-

import sys
import os
from pathlib import Path

# Get the project root directory
project_root = Path(SPECPATH)
src_dir = project_root / "src"

# PyInstaller spec for race simulation executable
block_cipher = None

a = Analysis(
    [str(src_dir / 'main.py')],
    pathex=[str(src_dir)],
    binaries=[],
    datas=[
        ('config.json', '.'),
        ('sample_runners.json', '.'),
        ('*.dll', '.'),
    ],
    hiddenimports=[
        'pygame',
        'pygame._sdl2',
        'ctypes',
        'json',
        'argparse',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'numpy',
        'scipy',
        'pandas',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='RaceSimulator',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # Hide console window for GUI application
    disable_windowed_traceback=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # Add icon='icon.ico' if you have an icon file
)

# Create a single-file executable
coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='RaceSimulator'
)
'''
        
        with open(self.spec_file, 'w') as f:
            f.write(spec_content)
        print(f"Created spec file: {self.spec_file}")
    
    def build_executable(self, clean: bool = True) -> bool:
        """
        Build the executable using PyInstaller.
        
        Args:
            clean: Whether to clean previous build artifacts first
            
        Returns:
            True if build successful, False otherwise
        """
        if not self.check_requirements():
            return False
        
        if clean:
            self.clean_build()
        
        # Create supporting files
        self.create_config_file()
        self.create_sample_data()
        self.create_spec_file()
        
        print("\nBuilding executable...")
        
        # Build command
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--onefile",
            "--windowed",  # Hide console window
            "--name", "RaceSimulator",
            "--distpath", str(self.dist_dir),
            "--workpath", str(self.build_dir),
            "--specpath", str(self.project_root),
            str(self.spec_file)
        ]
        
        try:
            # Run PyInstaller
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✓ Build completed successfully!")
                
                # Show the location of the built executable
                exe_path = self.dist_dir / "RaceSimulator.exe"
                if exe_path.exists():
                    print(f"Executable created: {exe_path}")
                    print(f"File size: {exe_path.stat().st_size / (1024*1024):.1f} MB")
                
                return True
            else:
                print(f"Build failed with return code: {result.returncode}")
                print(result.stderr)
                return False
                
        except subprocess.CalledProcessError as e:
            print(f"Build failed: {e}")
            print(e.stderr)
            return False
        except Exception as e:
            print(f"Build error: {e}")
            return False
    
    def create_distribution_package(self) -> None:
        """Create a complete distribution package with documentation."""
        dist_package = self.project_root / "RaceSimulator_Distribution"
        
        if dist_package.exists():
            shutil.rmtree(dist_package)
        
        os.makedirs(dist_package, exist_ok=True)
        
        # Copy executable
        exe_source = self.dist_dir / "RaceSimulator.exe"
        if exe_source.exists():
            shutil.copy2(exe_source, dist_package)
        
        # Copy sample files
        files_to_copy = [
            "config.json",
            "sample_runners.json",
            "README.md"
        ]
        
        for file_name in files_to_copy:
            source = self.project_root / file_name
            if source.exists():
                shutil.copy2(source, dist_package)
        
        # Create README if it doesn't exist
        readme_path = dist_package / "README.txt"
        if not readme_path.exists():
            readme_content = """Race Simulator - Windows Executable
==================================

This package contains the Race Simulator application.

FILES:
- RaceSimulator.exe   : Main executable
- config.json         : Application configuration
- sample_runners.json : Sample runner data

USAGE:
1. Double-click RaceSimulator.exe to run with sample data
2. Edit sample_runners.json to customize runners
3. Use config.json to adjust application settings

CONTROLS:
- SPACE : Play/Pause animation
- R     : Reset animation
- ESC   : Exit application
- Click progress bar to seek

TROUBLESHOOTING:
- Ensure race_simulator.dll is in the same folder as the executable
- Check config.json for DLL path configuration
- Run from command line for error messages: RaceSimulator.exe --verbose
"""
            with open(readme_path, 'w') as f:
                f.write(readme_content)
        
        print(f"Distribution package created: {dist_package}")
    
    def verify_build(self) -> bool:
        """Verify the built executable works correctly."""
        exe_path = self.dist_dir / "RaceSimulator.exe"
        
        if not exe_path.exists():
            print("✗ Executable not found")
            return False
        
        print("Verifying executable...")
        
        try:
            # Test with --no-gui and --sample flags
            cmd = [str(exe_path), "--no-gui", "--sample", "--verbose"]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print("✓ Executable verification passed")
                return True
            else:
                print("✗ Executable verification failed")
                print("STDOUT:", result.stdout)
                print("STDERR:", result.stderr)
                return False
                
        except subprocess.TimeoutExpired:
            print("✗ Executable verification timed out")
            return False
        except Exception as e:
            print(f"✗ Executable verification error: {e}")
            return False


def main():
    """Main build script entry point."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Build Race Simulator executable")
    parser.add_argument("--clean", action="store_true", help="Clean before building")
    parser.add_argument("--no-verify", action="store_true", help="Skip verification")
    parser.add_argument("--package", action="store_true", help="Create distribution package")
    parser.add_argument("--help-build", action="store_true", help="Show build help")
    
    args = parser.parse_args()
    
    if args.help_build:
        print("""
Race Simulator Build Help
========================

Prerequisites:
1. Install required packages:
   pip install pygame pyinstaller

2. Ensure race_simulator.dll is in the project root

Build Commands:
- python build.py --clean          # Clean and build
- python build.py                  # Build without cleaning
- python build.py --package        # Create distribution package
- python build.py --no-verify      # Skip executable verification

Output:
- Executable: dist/RaceSimulator.exe
- Package: RaceSimulator_Distribution/

Usage:
- Double-click RaceSimulator.exe
- Or run from command line: RaceSimulator.exe --help
        """)
        return
    
    print("Race Simulator Build Script")
    print("=" * 40)
    
    builder = BuildManager()
    
    if builder.build_executable(clean=args.clean):
        if not args.no_verify:
            builder.verify_build()
        
        if args.package:
            builder.create_distribution_package()
        
        print("\nBuild process completed!")
    else:
        print("\nBuild process failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()