# Running Race Simulation Visualization System

A complete Windows application for visualizing running race simulations with DLL integration, real-time animation, and standalone executable packaging.

## Features

- **DLL Integration**: Uses ctypes to interface with race calculation DLL
- **Real-time Animation**: 30 FPS Pygame-based visualization
- **Interactive Controls**: Play/pause with spacebar, seek functionality
- **Complete Results**: Full race results with positions, times, and statistics
- **Standalone Executable**: Single-file Windows executable with PyInstaller
- **Customizable**: Load custom runner data and adjust race parameters

## Quick Start

### Running from Source

1. Install dependencies:
   ```bash
   pip install pygame
   ```

2. Ensure race_simulator.dll is in the project root

3. Run the application:
   ```bash
   python src/main.py
   ```

### Using the Executable

1. Build the executable (see Build Instructions below)
2. Run `RaceSimulator.exe` from the dist folder
3. Or use the complete distribution package

## Usage

### Command Line Options

```bash
python src/main.py [OPTIONS]

Options:
  -r, --runners FILE      JSON file with runner data
  -d, --distance FLOAT    Race distance in meters (default: 100)
  --dll PATH              Path to race_simulator.dll
  -e, --export FILE       Export results to JSON file
  --sample                Use sample runner data
  --no-gui                Calculate without GUI (for testing)
  -v, --verbose           Enable verbose output
  --create-sample FILE    Create sample runner file
```

### Examples

```bash
# Run with sample data
python src/main.py

# Load custom runners
python src/main.py --runners my_runners.json

# 200m race
python src/main.py --distance 200

# Export results
python src/main.py --export results.json --sample

# Create sample file
python build.py --create-sample custom_runners.json
```

### Controls in Animation

- **Space**: Play/Pause
- **R**: Reset animation
- **Click progress bar**: Seek to position
- **ESC**: Exit application

## File Structure

```
race-simulation/
├── src/
│   ├── race_simulator_types.py  # DLL interface and data structures
│   ├── data_loader.py          # Race calculation and data management
│   ├── animation_engine.py     # Pygame visualization
│   ├── ui_controller.py        # Main application controller
│   └── main.py                 # Entry point
├── build.py                    # PyInstaller build script
├── config.json                 # Application configuration
├── sample_runners.json         # Sample runner data
└── README.md                   # This file
```

## Runner Data Format

Runner data should be in JSON format:

```json
{
  "description": "My race data",
  "race_distance": 100,
  "runners": [
    {
      "id": 1,
      "max_speed": 12.5,
      "acceleration": 2.8,
      "endurance": 0.85
    }
  ]
}
```

### Runner Parameters

- **id**: Unique identifier for each runner
- **max_speed**: Maximum speed in m/s (typical range: 10-15)
- **acceleration**: Acceleration rate in m/s² (typical range: 2-4)
- **endurance**: Endurance factor 0-1 (higher = better endurance)

## Configuration

Edit `config.json` to customize application behavior:

```json
{
  "window": {
    "width": 1200,
    "height": 800,
    "fps": 30
  },
  "race": {
    "default_distance": 100.0,
    "dll_path": null
  },
  "ui": {
    "show_fps": false,
    "auto_play": false
  }
}
```

## Build Instructions

### Prerequisites

1. Install build dependencies:
   ```bash
   pip install pygame pyinstaller
   ```

2. Ensure race_simulator.dll is available

### Building

```bash
# Clean build
python build.py --clean

# Create distribution package
python build.py --clean --package

# Build help
python build.py --help-build
```

### Build Output

- **Executable**: `dist/RaceSimulator.exe`
- **Distribution Package**: `RaceSimulator_Distribution/` (includes docs and samples)

## DLL Integration

The application expects `race_simulator.dll` to provide the following functions:

```c
// Calculate race simulation
int calculate_race(RunnerPerformance* runners, int num_runners, 
                   double distance, RaceData* out_data);

// Free allocated race data
void free_race_data(RaceData* data);

// Get last error message
const char* get_last_error();
```

### Data Structures

```c
typedef struct {
    int id;
    double max_speed;
    double acceleration;
    double endurance;
} RunnerPerformance;

typedef struct {
    int runner_id;
    double finish_time;
    double max_speed_achieved;
    double average_speed;
    int final_position;
    bool did_finish;
} RaceResult;

typedef struct {
    double time;
    int runner_id;
    double position;
    double current_speed;
    double current_acceleration;
} RaceSnapshot;
```

## Troubleshooting

### Common Issues

1. **DLL Not Found**
   - Ensure race_simulator.dll is in the same folder as executable
   - Check config.json for custom DLL path

2. **Import Errors**
   - Install missing dependencies: `pip install pygame`
   - Check Python path configuration

3. **Build Failures**
   - Ensure PyInstaller is installed: `pip install pyinstaller`
   - Check for antivirus interference
   - Run as administrator if needed

### Debug Mode

Enable verbose logging:
```bash
python src/main.py --verbose --sample
```

## Development

### Testing

Run tests without GUI:
```bash
python src/main.py --no-gui --sample --verbose
```

### Extending

The modular architecture allows easy extension:
- Add new visualization features in `animation_engine.py`
- Extend runner attributes in `race_simulator_types.py`
- Add new data sources in `data_loader.py`

## License

This project is provided as-is for educational and demonstration purposes.