"""
Animation Engine Tests

Tests for animation functionality including:
- Frame interpolation accuracy
- Runner positioning calculations
- Animation timing and synchronization
- Visual rendering accuracy
- Performance validation
"""

import os
import sys
import unittest
from unittest.mock import patch, MagicMock
import json
import tempfile

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

try:
    import pygame
    from animation_engine import AnimationEngine
    from data_loader import DataLoader
    from race_simulator_types import create_sample_runners
    PYGAME_AVAILABLE = True
except ImportError:
    PYGAME_AVAILABLE = False


class TestAnimationEngine(unittest.TestCase):
    """Test suite for animation engine functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        if not PYGAME_AVAILABLE:
            self.skipTest("Pygame not available")
        
        self.sample_runners = create_sample_runners()
        self.test_distance = 100.0
        
        # Mock pygame to avoid actual display
        self.pygame_patcher = patch('pygame.init')
        self.mock_init = self.pygame_patcher.start()
        
        self.display_patcher = patch('pygame.display.set_mode')
        self.mock_display = self.display_patcher.start()
        self.mock_display.return_value = MagicMock()
        
        self.clock_patcher = patch('pygame.time.Clock')
        self.mock_clock = self.clock_patcher.start()
        self.mock_clock_instance = MagicMock()
        self.mock_clock.return_value = self.mock_clock_instance
        
    def tearDown(self):
        """Clean up test fixtures."""
        if PYGAME_AVAILABLE:
            patch.stopall()
    
    def test_initialization(self):
        """Test animation engine initialization."""
        engine = AnimationEngine(width=800, height=600, fps=30)
        
        self.assertEqual(engine.width, 800)
        self.assertEqual(engine.height, 600)
        self.assertEqual(engine.fps, 30)
        self.assertFalse(engine.is_playing)
        self.assertEqual(engine.current_time, 0.0)
    
    def test_load_race_data(self):
        """Test loading race data into animation engine."""
        loader = DataLoader()
        race_data = loader.calculate_race(self.sample_runners, self.test_distance)
        
        engine = AnimationEngine()
        engine.load_race_data(race_data)
        
        self.assertIsNotNone(engine.animation_data)
        self.assertEqual(engine.animation_data['race_info']['total_runners'], len(self.sample_runners))
        self.assertEqual(engine.current_time, 0.0)
        self.assertFalse(engine.is_playing)
    
    def test_play_pause_toggle(self):
        """Test play/pause functionality."""
        engine = AnimationEngine()
        
        # Initial state should be paused
        self.assertFalse(engine.is_playing)
        
        # Toggle to play
        engine.toggle_play_pause()
        self.assertTrue(engine.is_playing)
        
        # Toggle to pause
        engine.toggle_play_pause()
        self.assertFalse(engine.is_playing)
    
    def test_reset_functionality(self):
        """Test animation reset functionality."""
        loader = DataLoader()
        race_data = loader.calculate_race(self.sample_runners, self.test_distance)
        
        engine = AnimationEngine()
        engine.load_race_data(race_data)
        
        # Set some state
        engine.current_time = 5.0
        engine.is_playing = True
        
        # Reset
        engine.reset()
        
        self.assertEqual(engine.current_time, 0.0)
        self.assertFalse(engine.is_playing)
    
    def test_frame_retrieval_by_time(self):
        """Test retrieving frames based on current time."""
        loader = DataLoader()
        race_data = loader.calculate_race(self.sample_runners, self.test_distance)
        
        engine = AnimationEngine()
        engine.load_race_data(race_data)
        
        # Test at different time points
        engine.current_time = 0.0
        frame = engine.get_current_frame()
        self.assertIsNotNone(frame)
        
        engine.current_time = engine.total_duration / 2
        frame = engine.get_current_frame()
        self.assertIsNotNone(frame)
        
        engine.current_time = engine.total_duration
        frame = engine.get_current_frame()
        self.assertIsNotNone(frame)
    
    def test_frame_interpolation_accuracy(self):
        """Test accuracy of frame interpolation."""
        loader = DataLoader()
        race_data = loader.calculate_race(self.sample_runners, self.test_distance)
        
        engine = AnimationEngine()
        engine.load_race_data(race_data)
        
        frames = race_data['frames']
        
        # Test that frames are ordered by time
        times = [frame['time'] for frame in frames]
        self.assertEqual(times, sorted(times))
        
        # Test frame count consistency
        expected_frame_count = len(frames)
        actual_frame_count = race_data['race_info']['total_frames']
        self.assertEqual(actual_frame_count, expected_frame_count)
    
    def test_runner_position_calculation(self):
        """Test runner position calculations on track."""
        loader = DataLoader()
        race_data = loader.calculate_race(self.sample_runners, self.test_distance)
        
        engine = AnimationEngine()
        engine.load_race_data(race_data)
        
        # Test runner positions in a sample frame
        engine.current_time = engine.total_duration / 2
        frame = engine.get_current_frame()
        
        if frame and 'runners' in frame:
            for runner_id, runner_data in frame['runners'].items():
                position = runner_data['position']
                
                # Position should be within race bounds
                self.assertGreaterEqual(position, 0)
                self.assertLessEqual(position, self.test_distance)
    
    def test_track_dimension_calculation(self):
        """Test track dimension calculations."""
        engine = AnimationEngine(width=1200, height=800)
        
        self.assertEqual(engine.track_start_x, 100)
        self.assertEqual(engine.track_end_x, 1100)
        self.assertEqual(engine.track_width, 1000)
        self.assertEqual(engine.track_y, 400)
    
    def test_color_assignment_consistency(self):
        """Test consistent color assignment for runners."""
        loader = DataLoader()
        race_data = loader.calculate_race(self.sample_runners, self.test_distance)
        
        engine = AnimationEngine()
        engine.load_race_data(race_data)
        
        # Verify colors are assigned to all runners
        self.assertEqual(len(engine.runner_colors), len(self.sample_runners))
        
        # Verify color format (RGB tuples)
        for color in engine.runner_colors.values():
            self.assertIsInstance(color, tuple)
            self.assertEqual(len(color), 3)
            for component in color:
                self.assertIsInstance(component, int)
                self.assertGreaterEqual(component, 0)
                self.assertLessEqual(component, 255)
    
    def test_animation_timing_accuracy(self):
        """Test animation timing and frame rate consistency."""
        engine = AnimationEngine(fps=30)
        
        # Mock clock to return consistent delta time
        mock_clock_instance = MagicMock()
        mock_clock_instance.tick.return_value = 33  # ~30 FPS
        engine.clock = mock_clock_instance
        
        # Test timing calculation
        delta_time = engine.clock.tick(30) / 1000.0
        self.assertAlmostEqual(delta_time, 0.033, places=2)
    
    def test_empty_race_data_handling(self):
        """Test handling of empty or invalid race data."""
        engine = AnimationEngine()
        
        # Test with None data
        engine.load_race_data(None)
        self.assertIsNone(engine.animation_data)
        self.assertEqual(engine.total_duration, 0.0)
        
        # Test with empty data
        empty_data = {
            'race_info': {'total_runners': 0, 'total_frames': 0},
            'results': [],
            'frames': []
        }
        engine.load_race_data(empty_data)
        self.assertEqual(engine.animation_data['race_info']['total_runners'], 0)
    
    def test_boundary_time_handling(self):
        """Test handling of boundary time values."""
        loader = DataLoader()
        race_data = loader.calculate_race(self.sample_runners, self.test_distance)
        
        engine = AnimationEngine()
        engine.load_race_data(race_data)
        
        # Test negative time
        engine.current_time = -1.0
        frame = engine.get_current_frame()
        if frame:
            self.assertEqual(frame, race_data['frames'][0])
        
        # Test time beyond duration
        engine.current_time = engine.total_duration + 1.0
        frame = engine.get_current_frame()
        if frame:
            self.assertEqual(frame, race_data['frames'][-1])
    
    def test_progress_bar_calculation(self):
        """Test progress bar calculation accuracy."""
        loader = DataLoader()
        race_data = loader.calculate_race(self.sample_runners, self.test_distance)
        
        engine = AnimationEngine()
        engine.load_race_data(race_data)
        
        # Test progress calculation at different points
        test_progress_values = [0.0, 0.25, 0.5, 0.75, 1.0]
        
        for progress in test_progress_values:
            engine.current_time = progress * engine.total_duration
            calculated_progress = engine.current_time / engine.total_duration if engine.total_duration > 0 else 0
            self.assertAlmostEqual(calculated_progress, progress, places=2)


class TestFrameInterpolation(unittest.TestCase):
    """Test suite for frame interpolation accuracy."""
    
    def setUp(self):
        """Set up test fixtures."""
        if not PYGAME_AVAILABLE:
            self.skipTest("Pygame not available")
    
    def test_linear_interpolation_accuracy(self):
        """Test accuracy of linear interpolation between frames."""
        loader = DataLoader()
        race_data = loader.calculate_race(create_sample_runners(), 100.0)
        
        frames = race_data['frames']
        
        # Test that interpolation maintains consistent progression
        for i in range(1, len(frames)):
            prev_frame = frames[i-1]
            curr_frame = frames[i]
            
            # Time should always increase
            self.assertGreater(curr_frame['time'], prev_frame['time'])
            
            # Check runner position progression
            for runner_id in curr_frame['runners']:
                if runner_id in prev_frame['runners']:
                    curr_pos = curr_frame['runners'][runner_id]['position']
                    prev_pos = prev_frame['runners'][runner_id]['position']
                    
                    # Position should generally increase (or stay same if finished)
                    self.assertGreaterEqual(curr_pos, prev_pos)
    
    def test_frame_count_consistency(self):
        """Test consistency between frame count and timing."""
        loader = DataLoader()
        race_data = loader.calculate_race(create_sample_runners(), 100.0)
        
        frames = race_data['frames']
        total_frames = len(frames)
        
        # Calculate expected frame count based on 30 FPS
        if total_frames > 0:
            total_duration = frames[-1]['time']
            expected_frames = int(total_duration * 30) + 1
            
            # Allow some tolerance for rounding
            self.assertAlmostEqual(total_frames, expected_frames, delta=2)
    
    def test_runner_tracking_continuity(self):
        """Test that runners are tracked continuously across frames."""
        loader = DataLoader()
        race_data = loader.calculate_race(create_sample_runners(), 100.0)
        
        frames = race_data['frames']
        
        # Check that all runners appear in all frames
        runner_ids = {runner['id'] for runner in create_sample_runners()}
        
        for frame in frames:
            frame_runners = set(frame['runners'].keys())
            # All runners should appear in each frame
            self.assertEqual(frame_runners, runner_ids)


class TestAnimationPerformance(unittest.TestCase):
    """Test suite for animation performance validation."""
    
    def setUp(self):
        """Set up test fixtures."""
        if not PYGAME_AVAILABLE:
            self.skipTest("Pygame not available")
    
    def test_frame_rate_consistency(self):
        """Test consistent frame rate across different race configurations."""
        test_configs = [
            {"runners": 1, "distance": 100},
            {"runners": 5, "distance": 100},
            {"runners": 10, "distance": 200},
            {"runners": 15, "distance": 400},
        ]
        
        for config in test_configs:
            with self.subTest(config=config):
                # Create test data
                test_runners = [
                    {"id": i+1, "max_speed": 12.0, "acceleration": 3.0, "endurance": 0.85}
                    for i in range(config['runners'])
                ]
                
                loader = DataLoader()
                race_data = loader.calculate_race(test_runners, config['distance'])
                
                # Verify frame rate consistency
                frames = race_data['frames']
                if len(frames) > 1:
                    time_diffs = [
                        frames[i]['time'] - frames[i-1]['time']
                        for i in range(1, len(frames))
                    ]
                    
                    # All time differences should be approximately equal (1/30 second)
                    expected_diff = 1.0 / 30.0
                    for diff in time_diffs:
                        self.assertAlmostEqual(diff, expected_diff, places=2)
    
    def test_memory_usage_scaling(self):
        """Test memory usage scaling with race complexity."""
        test_scales = [1, 5, 10, 15]
        
        for num_runners in test_scales:
            with self.subTest(runners=num_runners):
                test_runners = [
                    {"id": i+1, "max_speed": 12.0, "acceleration": 3.0, "endurance": 0.85}
                    for i in range(num_runners)
                ]
                
                loader = DataLoader()
                race_data = loader.calculate_race(test_runners, 100.0)
                
                # Calculate expected frame data size
                frames = race_data['frames']
                total_snapshots = len(frames) * num_runners
                
                # Should scale linearly with runners
                expected_snapshots = len(frames) * num_runners
                self.assertEqual(total_snapshots, expected_snapshots)


if __name__ == '__main__':
    if PYGAME_AVAILABLE:
        unittest.main()
    else:
        print("Pygame not available, skipping animation tests")