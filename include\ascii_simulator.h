#pragma once

#include <string>
#include <vector>
#include <memory>

class RaceEngine;
class AnimationEngine;
class ConsoleUtils;

class AsciiSimulator {
public:
    AsciiSimulator();
    ~AsciiSimulator();

    bool initialize();
    void run();
    void shutdown();

    void startRace();
    void displayResults();

private:
    std::unique_ptr<RaceEngine> raceEngine;
    std::unique_ptr<AnimationEngine> animationEngine;
    std::unique_ptr<ConsoleUtils> console;

    bool isRunning;
    bool raceInProgress;
    
    void processInput();
    void update();
    void render();
    
    void displayMenu();
    void displayRace();
    void displayWinner();
};