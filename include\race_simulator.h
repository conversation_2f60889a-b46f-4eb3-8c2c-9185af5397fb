#pragma once

#ifdef _WIN32
    #ifdef RACE_SIMULATOR_EXPORTS
        #define RACE_SIMULATOR_API __declspec(dllexport)
    #else
        #define RACE_SIMULATOR_API __declspec(dllimport)
    #endif
#else
    #define RACE_SIMULATOR_API __attribute__((visibility("default")))
#endif

#include <cstdint>




enum class Mood {
    EXCELLENT = 0,  
    GOOD = 1,       
    POOR = 2,       
    BAD = 3         
};


enum class Adaptability {
    EXCELLENT = 0,  
    GOOD = 1,       
    POOR = 2,       
    BAD = 3         
};


enum class TrackType {
    PLASTIC = 0,    
    GRASS = 1,      
    CINDER = 2,     
    DIRT = 3        
};


enum class Weather {
    SUNNY = 0,      
    VERY_SUNNY = 1, 
    CLOUDY = 2,     
    LIGHT_RAIN = 3, 
    SMALL_RAIN = 4, 
    MEDIUM_RAIN = 5, 
    HEAVY_RAIN = 6  
};


enum class WindDirection {
    HEADWIND = 0,   
    TAILWIND = 1    
};


enum class InherentSkill {
    MID_RACE_ACCEL = 0,     
    START_WISDOM = 1,       
    LATE_RACE_POWER = 2     
};


struct RunnerAttributes {
    uint8_t mood;           
    uint8_t stamina;        
    uint8_t speed;          
    uint8_t power;          
    uint8_t endurance;      
    uint8_t intelligence;   
    uint8_t body;           
    Adaptability adaptability; 
    bool hasMidRaceAccel;   
    bool hasStartWisdom;    
    bool hasLateRacePower;  
};


struct Environment {
    TrackType trackType;    
    Weather weather;        
    WindDirection windDir;  
    float windSpeed;        
};


struct RaceResult {
    float totalTime;        
    float reactionTime;     
    float segment1Time;     
    float segment2Time;     
    float segment3Time;     
    float segment4Time;     
    bool falseStart;        
    bool skillActivated[3]; 
};

#ifdef __cplusplus
extern "C" {
#endif


RACE_SIMULATOR_API void InitializeRaceSimulator();


RACE_SIMULATOR_API RaceResult CalculateRaceResult(
    const RunnerAttributes* runner,
    const Environment* env
);


RACE_SIMULATOR_API const char* GetLastErrorMessage();


RACE_SIMULATOR_API void SetRandomSeed(uint32_t seed);


RACE_SIMULATOR_API int RunTournament(const char* dbPath);


RACE_SIMULATOR_API void FreeString(const char* str);

#ifdef __cplusplus
}
#endif