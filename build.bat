@echo off
setlocal enabledelayedexpansion

REM ASCII Running Simulation Build Script for Windows
REM Enhanced for MSBuild and Visual Studio

echo ASCII Running Simulation Build System
echo =====================================
echo.

REM Check if we want to clean
if /i "%1"=="clean" goto :clean
if /i "%1"=="test" goto :test
if /i "%1"=="run" goto :run
if /i "%1"=="setup" goto :setup

REM Default configuration
set CONFIGURATION=Release
set PLATFORM=x64

REM Parse command line arguments
:parse_args
if "%~1"=="" goto :main
if /i "%~1"=="debug" set CONFIGURATION=Debug
if /i "%~1"=="release" set CONFIGURATION=Release
if /i "%~1"=="x86" set PLATFORM=Win32
if /i "%~1"=="x64" set PLATFORM=x64
shift
goto :parse_args

:setup
REM Check for required tools
echo Checking build environment...
echo.

REM Check for Visual Studio
where cl >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Visual Studio compiler (cl.exe) not found!
    echo Please install Visual Studio 2019 or later with C++ workload
    echo.
    echo Alternative: Install Build Tools for Visual Studio
    echo Download from: https://visualstudio.microsoft.com/downloads/
    pause
    exit /b 1
)

REM Check for CMake
where cmake >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: CMake not found!
    echo Please install CMake from: https://cmake.org/download/
    pause
    exit /b 1
)

echo Build environment check complete!
echo Visual Studio: OK
echo CMake: OK
echo.
echo Environment is ready for building.
pause
goto :end

:clean
echo Cleaning build directories...
if exist build rmdir /s /q build
if exist bin rmdir /s /q bin
if exist obj rmdir /s /q obj
if exist lib rmdir /s /q lib
for /f "delims=" %%i in ('dir /s /b /a:d CMakeFiles 2^>nul') do rmdir /s /q "%%i"
for /f "delims=" %%i in ('dir /s /b /a:d CMakeCache 2^>nul') do rmdir /s /q "%%i"
echo Clean complete!
goto :end

:main
echo Building ASCII Running Simulation...
echo Configuration: %CONFIGURATION%
echo Platform: %PLATFORM%
echo.

REM Create build directory
set BUILD_DIR=build\%PLATFORM%\%CONFIGURATION%
if not exist "%BUILD_DIR%" mkdir "%BUILD_DIR%"

REM Change to build directory
pushd "%BUILD_DIR%"

REM Configure with CMake
echo Configuring with CMake...
cmake ..\..\.. -G "Visual Studio 17 2022" -A %PLATFORM% -DCMAKE_BUILD_TYPE=%CONFIGURATION%
if %errorlevel% neq 0 (
    echo ERROR: CMake configuration failed!
    pause
    popd
    exit /b 1
)

REM Build with MSBuild
echo Building with MSBuild...
cmake --build . --config %CONFIGURATION% --parallel
if %errorlevel% neq 0 (
    echo ERROR: Build failed!
    pause
    popd
    exit /b 1
)

popd

REM Copy dependencies
echo Copying dependencies...
if not exist "lib" mkdir lib
if not exist "bin" mkdir bin

REM Copy DLL and executable
if exist "%BUILD_DIR%\bin\%CONFIGURATION%\race_simulator.dll" (
    copy "%BUILD_DIR%\bin\%CONFIGURATION%\race_simulator.dll" "lib\RaceSimulation.dll" >nul
    echo DLL copied to lib\RaceSimulation.dll
)

if exist "%BUILD_DIR%\bin\%CONFIGURATION%\AsciiRaceSimulator.exe" (
    copy "%BUILD_DIR%\bin\%CONFIGURATION%\AsciiRaceSimulator.exe" "bin\" >nul
    copy "lib\RaceSimulation.dll" "bin\RaceSimulation.dll" >nul
    echo Executable copied to bin\AsciiRaceSimulator.exe
)

echo.
echo Build completed successfully!
echo.
goto :end

:test
echo Running tests...
if exist "bin\AsciiRaceSimulator.exe" (
    echo Testing basic functionality...
    bin\AsciiRaceSimulator.exe --test-mode
    if %errorlevel% neq 0 (
        echo Test failed!
        pause
        exit /b 1
    )
    echo Tests passed!
) else (
    echo ERROR: Executable not found. Please build first.
    echo Run: build.bat release
)
goto :end

:run
echo Starting ASCII Running Simulation...
if exist "bin\AsciiRaceSimulator.exe" (
    bin\AsciiRaceSimulator.exe
) else (
    echo ERROR: Executable not found. Please build first.
    echo Run: build.bat release
    pause
)
goto :end

:end
echo.
echo Build process completed!