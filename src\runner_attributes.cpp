#include "race_simulator.h"
#include "runner_attributes.h"
#include "random_generator.h"
#include <algorithm>
#include <cmath>

namespace RaceSimulator {

    Mood GetMoodFromValue(uint8_t moodValue) {
        if (moodValue >= 200) return Mood::EXCELLENT;
        if (moodValue >= 150) return Mood::GOOD;
        if (moodValue >= 100) return Mood::GOOD;
        if (moodValue >= 50) return Mood::POOR;
        return Mood::BAD;
    }

    float CalculateMoodMultiplier(Mood mood) {
        switch (mood) {
            case Mood::EXCELLENT: return 1.1f;
            case Mood::GOOD: return 1.05f;
            case Mood::POOR: return 0.95f;
            case Mood::BAD: return 0.9f;
            default: return 1.0f;
        }
    }

    float CalculateAttributeVariance(const RunnerAttributes* runner) {
        if (!runner) return 0.05f;

        // Calculate variance based on attributes
        float speedVariance = (255 - runner->speed) / 2550.0f;
        float staminaVariance = (255 - runner->stamina) / 2550.0f;
        float powerVariance = (255 - runner->power) / 2550.0f;
        float enduranceVariance = (255 - runner->endurance) / 2550.0f;

        // Higher attributes mean more consistency (lower variance)
        float avgVariance = (speedVariance + staminaVariance + powerVariance + enduranceVariance) / 4.0f;

        // Body and intelligence also affect consistency
        float bodyFactor = static_cast<float>(runner->body) / 255.0f;
        float intelligenceFactor = static_cast<float>(runner->intelligence) / 255.0f;

        float consistencyBonus = (bodyFactor + intelligenceFactor) * 0.02f;

        return std::max(0.01f, std::min(avgVariance - consistencyBonus, 0.1f));
    }

    float GenerateAttributeBasedTime(float baseTime, float range, const RunnerAttributes* runner) {
        if (!runner) {
            return RandomNormal(baseTime, range);
        }

        float variance = CalculateAttributeVariance(runner);
        Mood mood = GetMoodFromValue(runner->mood);
        float moodMultiplier = CalculateMoodMultiplier(mood);

        // Generate time with attribute-based variance
        float adjustedRange = range * variance;
        float time = RandomNormal(baseTime, adjustedRange);

        // Apply mood effect
        time *= moodMultiplier;

        return std::max(baseTime * 0.8f, std::min(time, baseTime * 1.2f));
    }



}
