#include "race_simulator.h"
#include "random_generator.h"
#include <algorithm>
#include <cmath>
#include <random>
#include "environment.h"

namespace RaceSimulator {

    float CalculateMoodMultiplier(Mood mood) {
        switch (mood) {
            case Mood::EXCELLENT: return 1.02f;  // +2%
            case Mood::GOOD: return 1.0f;        // 无影响
            case Mood::POOR: return 0.97f;       // -3%
            case Mood::BAD: return 0.95f;        // -5%
            default: return 1.0f;
        }
    }

    // 根据心情值获取心情状态
    Mood GetMoodFromValue(uint8_t moodValue) {
        if (moodValue >= 200) return Mood::EXCELLENT;
        else if (moodValue >= 100) return Mood::GOOD;
        else if (moodValue >= 50) return Mood::POOR;
        else return Mood::BAD;
    }

    // 适应性系数计算
    float CalculateAdaptabilityMultiplier(Adaptability adapt, TrackType trackType) {
        switch (adapt) {
            case Adaptability::EXCELLENT:
                return 1.0f;  // 所有跑道都擅长
            case Adaptability::GOOD:
                // 擅长塑胶/煤渣/土地，不擅长草地
                return (trackType == TrackType::GRASS) ? 0.95f : 1.0f;
            case Adaptability::POOR:
                // 只擅长塑胶/煤渣
                return (trackType == TrackType::GRASS || trackType == TrackType::DIRT) ? 0.9f : 1.0f;
            case Adaptability::BAD:
                // 只擅长塑胶
                return (trackType == TrackType::PLASTIC) ? 1.0f : 0.85f;
            default:
                return 1.0f;
        }
    }

    // 体力系数计算
    float CalculateStaminaMultiplier(uint8_t stamina) {
        const float threshold = 0.7f;
        const float minMultiplier = 0.7f;
        const float power = 2.0f;
    
        float staminaRatio = static_cast<float>(stamina) / 255.0f;
    
        if (staminaRatio >= threshold) {
            return 1.0f; // No penalty
        } else {
            // Normalize the ratio to the range for the penalty part
            float normalizedRatio = staminaRatio / threshold;
            
            // Apply the power function
            float factor = std::pow(normalizedRatio, power);
            
            // Map the result to the desired multiplier range [minMultiplier, 1.0]
            return minMultiplier + factor * (1.0f - minMultiplier);
        }
    }

    // 耐力系数计算（用于100米比赛）
    float CalculateEnduranceMultiplier(uint8_t endurance) {
        const float threshold = 100.0f;
        const float minMultiplier = 0.8f;
        const float power = 2.0f;
    
        if (static_cast<float>(endurance) >= threshold) {
            return 1.0f; // No penalty
        } else {
            // Normalize endurance to the range
            float normalizedRatio = static_cast<float>(endurance) / threshold;
            
            // Apply the power function
            float factor = std::pow(normalizedRatio, power);
            
            // Map the result to the desired multiplier range [minMultiplier, 1.0]
            return minMultiplier + factor * (1.0f - minMultiplier);
        }
    }

    // 起跑反应时间计算
    float CalculateReactionTime(const RunnerAttributes* runner) {
        float baseReactionTime = 0.15f;  // 基础反应时间
        
        // 力量影响：250+力量可达0.1-0.12s
        if (runner->power >= 250) {
            std::random_device rd;
            std::mt19937 gen(rd());
            std::uniform_real_distribution<float> distrib(0.1f, 0.12f);
            baseReactionTime = distrib(gen);
        } else if (runner->power >= 200) {
            baseReactionTime = 0.11f;
        } else if (runner->power >= 150) {
            baseReactionTime = 0.12f;
        } else if (runner->power >= 100) {
            baseReactionTime = 0.13f;
        } else {
            baseReactionTime = 0.15f;
        }

        // 智力影响稳定性：智力越高越稳定
        float range = 0.05f;  // 基础范围
        if (runner->intelligence >= 250) {
            range = 0.01f;  // 非常稳定
        } else if (runner->intelligence >= 200) {
            range = 0.02f;
        } else if (runner->intelligence >= 150) {
            range = 0.03f;
        } else if (runner->intelligence >= 100) {
            range = 0.04f;
        }

        // 使用正态分布生成随机数
        float reactionTime = RandomNormal(baseReactionTime, range / 3.0f); // range is 99.7% confidence
        
        // 确保不低于0.1s
        return std::max(0.1f, reactionTime);
    }

    // 抢跑概率计算
    float CalculateFalseStartProbability(const RunnerAttributes* runner) {
        Mood mood = GetMoodFromValue(runner->mood);
        float baseProb = 0.0f;

        // 心情影响抢跑概率
        switch (mood) {
            case Mood::EXCELLENT: baseProb = 0.001f; break;
            case Mood::GOOD: baseProb = 0.005f; break;
            case Mood::POOR: baseProb = 0.02f; break;
            case Mood::BAD: baseProb = 0.05f; break;
        }

        // 智力降低抢跑概率
        float intelligenceFactor = 1.0f - (static_cast<float>(runner->intelligence) / 255.0f) * 0.8f;
        
        return baseProb * intelligenceFactor;
    }

    // 综合属性系数计算
    float CalculateOverallAttributeMultiplier(const RunnerAttributes* runner, const Environment* env) {
        Mood mood = GetMoodFromValue(runner->mood);
        
        float multiplier = 1.0f;
        
        multiplier *= CalculateMoodMultiplier(mood);
        multiplier *= CalculateAdaptabilityMultiplier(runner->adaptability, env->trackType);
        multiplier *= CalculateStaminaMultiplier(runner->stamina);
        multiplier *= CalculateEnduranceMultiplier(runner->endurance);
        
        // 基础属性综合影响
        float avgAttribute = (static_cast<float>(runner->speed) + 
                             static_cast<float>(runner->power) + 
                             static_cast<float>(runner->body)) / 3.0f;
        
        float attributeFactor = 0.5f + 0.5f * (avgAttribute / 255.0f);
        multiplier *= attributeFactor;
        
        return multiplier;
    }

}