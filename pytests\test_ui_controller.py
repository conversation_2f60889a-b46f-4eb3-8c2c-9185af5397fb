"""
UI Controller Tests

Tests for user interface and interaction including:
- Play/pause controls functionality
- User input handling
- Configuration loading
- Command line argument parsing
- Export functionality
"""

import os
import sys
import unittest
from unittest.mock import patch, MagicMock
import tempfile
import json
import argparse

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from ui_controller import UIController
from race_simulator_types import create_sample_runners


class TestUIController(unittest.TestCase):
    """Test suite for UI controller functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.sample_runners = create_sample_runners()
        
    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_initialization(self):
        """Test UI controller initialization."""
        controller = UIController()
        
        self.assertIsNotNone(controller)
        self.assertIsNone(controller.data_loader)
        self.assertIsNone(controller.animation_engine)
        self.assertIsNone(controller.race_data)
        self.assertIsNotNone(controller.config)
    
    def test_config_loading_default(self):
        """Test loading default configuration."""
        controller = UIController()
        
        config = controller.config
        self.assertIn('window', config)
        self.assertIn('race', config)
        self.assertIn('ui', config)
        
        # Check default values
        self.assertEqual(config['window']['width'], 1200)
        self.assertEqual(config['window']['height'], 800)
        self.assertEqual(config['window']['fps'], 30)
        self.assertEqual(config['race']['default_distance'], 100.0)
    
    def test_config_loading_from_file(self):
        """Test loading configuration from JSON file."""
        # Create test config file
        config_data = {
            "window": {
                "width": 800,
                "height": 600,
                "fps": 60
            },
            "race": {
                "default_distance": 200.0,
                "dll_path": "/custom/path.dll"
            },
            "ui": {
                "show_fps": True,
                "auto_play": True
            }
        }
        
        config_path = os.path.join(self.temp_dir, "config.json")
        with open(config_path, 'w') as f:
            json.dump(config_data, f)
        
        # Mock os.path.exists to return True for our test file
        with patch('os.path.exists') as mock_exists:
            mock_exists.return_value = True
            
            controller = UIController()
            
            # Manually test config loading
            loaded_config = controller._load_config()
            
            # Should use provided values where available
            self.assertEqual(loaded_config['window']['width'], 1200)  # Default
            self.assertEqual(loaded_config['window']['height'], 800)  # Default
            self.assertEqual(loaded_config['race']['default_distance'], 100.0)  # Default
    
    def test_argument_parsing_default(self):
        """Test parsing default command line arguments."""
        controller = UIController()
        
        args = controller.parse_arguments([])
        
        self.assertIsNone(args.runners)
        self.assertEqual(args.distance, 100.0)
        self.assertIsNone(args.dll)
        self.assertIsNone(args.export)
        self.assertFalse(args.sample)
        self.assertFalse(args.no_gui)
        self.assertFalse(args.verbose)
    
    def test_argument_parsing_custom(self):
        """Test parsing custom command line arguments."""
        controller = UIController()
        
        test_args = [
            "--runners", "test_runners.json",
            "--distance", "200",
            "--dll", "/path/to/dll.dll",
            "--export", "results.json",
            "--sample",
            "--no-gui",
            "--verbose"
        ]
        
        args = controller.parse_arguments(test_args)
        
        self.assertEqual(args.runners, "test_runners.json")
        self.assertEqual(args.distance, 200.0)
        self.assertEqual(args.dll, "/path/to/dll.dll")
        self.assertEqual(args.export, "results.json")
        self.assertTrue(args.sample)
        self.assertTrue(args.no_gui)
        self.assertTrue(args.verbose)
    
    def test_argument_parsing_short_flags(self):
        """Test parsing short command line flags."""
        controller = UIController()
        
        test_args = [
            "-r", "runners.json",
            "-d", "150",
            "-e", "output.json",
            "-v"
        ]
        
        args = controller.parse_arguments(test_args)
        
        self.assertEqual(args.runners, "runners.json")
        self.assertEqual(args.distance, 150.0)
        self.assertEqual(args.export, "output.json")
        self.assertTrue(args.verbose)
    
    def test_load_runner_data_from_file(self):
        """Test loading runner data from file."""
        # Create test runner file
        runner_data = [
            {"id": 1, "max_speed": 12.5, "acceleration": 2.8, "endurance": 0.85},
            {"id": 2, "max_speed": 11.8, "acceleration": 3.2, "endurance": 0.90}
        ]
        
        file_path = os.path.join(self.temp_dir, "test_runners.json")
        with open(file_path, 'w') as f:
            json.dump(runner_data, f)
        
        controller = UIController()
        
        # Mock data_loader to avoid DLL dependency
        with patch.object(controller, 'data_loader', create=True):
            runners = controller.load_runner_data(file_path)
            
            self.assertEqual(len(runners), 2)
            self.assertEqual(runners[0]['id'], 1)
    
    def test_load_runner_data_sample_fallback(self):
        """Test fallback to sample data when file doesn't exist."""
        controller = UIController()
        
        with patch.object(controller, 'data_loader', create=True):
            runners = controller.load_runner_data("nonexistent.json")
            
            # Should return sample runners
            self.assertIsInstance(runners, list)
            self.assertGreater(len(runners), 0)
    
    def test_race_calculation_success(self):
        """Test successful race calculation."""
        controller = UIController()
        
        # Mock data_loader
        mock_loader = MagicMock()
        mock_loader.calculate_race.return_value = {
            'race_info': {'distance': 100.0, 'total_runners': 5},
            'results': [{'runner_id': 1, 'finish_time': 10.0}],
            'frames': []
        }
        mock_loader.get_race_summary.return_value = "Test summary"
        
        controller.data_loader = mock_loader
        
        success = controller.calculate_race(self.sample_runners, 100.0)
        
        self.assertTrue(success)
        self.assertIsNotNone(controller.race_data)
    
    def test_race_calculation_failure(self):
        """Test race calculation failure handling."""
        controller = UIController()
        
        # Mock data_loader to fail
        mock_loader = MagicMock()
        mock_loader.calculate_race.return_value = None
        
        controller.data_loader = mock_loader
        
        success = controller.calculate_race(self.sample_runners, 100.0)
        
        self.assertFalse(success)
    
    def test_export_results_success(self):
        """Test successful results export."""
        controller = UIController()
        
        # Mock race data
        controller.race_data = {
            'race_info': {'distance': 100.0, 'total_runners': 5},
            'results': [{'runner_id': 1, 'finish_time': 10.0}],
            'frames': []
        }
        
        # Mock data_loader
        mock_loader = MagicMock()
        mock_loader.export_results.return_value = True
        controller.data_loader = mock_loader
        
        export_path = os.path.join(self.temp_dir, "test_export.json")
        success = controller.export_results(export_path)
        
        self.assertTrue(success)
    
    def test_export_results_no_data(self):
        """Test export when no race data is available."""
        controller = UIController()
        controller.race_data = None
        
        success = controller.export_results("test.json")
        
        self.assertFalse(success)
    
    def test_run_headless_mode(self):
        """Test headless mode execution."""
        controller = UIController()
        
        # Mock dependencies
        with patch.object(controller, 'calculate_race', return_value=True):
            with patch('builtins.print'):
                controller.run_headless(self.sample_runners, 100.0)
    
    def test_create_sample_runner_file(self):
        """Test creation of sample runner file."""
        controller = UIController()
        
        file_path = os.path.join(self.temp_dir, "sample.json")
        controller.create_sample_runner_file(file_path)
        
        self.assertTrue(os.path.exists(file_path))
        
        # Verify file content
        with open(file_path, 'r') as f:
            data = json.load(f)
        
        self.assertIn('runners', data)
        self.assertIn('race_distance', data)
        self.assertIn('description', data)


class TestUIControls(unittest.TestCase):
    """Test suite for UI control functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_keyboard_controls(self):
        """Test keyboard control handling."""
        # This would typically require pygame, so we'll test the control mapping
        controller = UIController()
        
        # Test that the controller can handle keyboard events
        # We'll test this through the run method with mocked components
        with patch('ui_controller.UIController.run_animation'):
            with patch('ui_controller.UIController.calculate_race', return_value=True):
                with patch('ui_controller.DataLoader'):
                    # Test with no-gui mode to avoid pygame
                    args = ['--no-gui', '--sample']
                    exit_code = controller.run(args)
                    self.assertEqual(exit_code, 0)
    
    def test_mouse_controls(self):
        """Test mouse control handling."""
        # Test mouse interaction through argument parsing
        controller = UIController()
        
        # Test that mouse events would be handled in the animation engine
        # We'll verify the control structures are set up correctly
        try:
            import pygame
            from animation_engine import AnimationEngine
            
            # Test animation engine setup
            engine = AnimationEngine()
            
            # Verify control rectangles are created
            self.assertTrue(hasattr(engine, 'play_button'))
            self.assertTrue(hasattr(engine, 'reset_button'))
            self.assertTrue(hasattr(engine, 'progress_bar'))
            
            # Verify they are pygame Rect objects
            self.assertIsInstance(engine.play_button, pygame.Rect)
            self.assertIsInstance(engine.reset_button, pygame.Rect)
            self.assertIsInstance(engine.progress_bar, pygame.Rect)
            
        except ImportError:
            self.skipTest("Pygame not available for mouse control testing")
    
    def test_progress_seek_functionality(self):
        """Test progress bar seek functionality."""
        # Test through argument parsing and configuration
        controller = UIController()
        
        # Test that seeking would work by verifying the animation engine supports it
        try:
            import pygame
            from animation_engine import AnimationEngine
            
            loader = DataLoader()
            race_data = loader.calculate_race(create_sample_runners(), 100.0)
            
            engine = AnimationEngine()
            engine.load_race_data(race_data)
            
            # Test that current_time can be set for seeking
            original_time = engine.current_time
            engine.current_time = 5.0
            self.assertEqual(engine.current_time, 5.0)
            
            # Test that seeking updates frame retrieval
            frame = engine.get_current_frame()
            self.assertIsNotNone(frame)
            
        except ImportError:
            self.skipTest("Pygame not available for seek testing")


class TestConfigurationEdgeCases(unittest.TestCase):
    """Test edge cases for configuration and setup."""
    
    def test_malformed_config_file(self):
        """Test handling of malformed configuration file."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            f.write('{"invalid": json content}')
            config_path = f.name
        
        try:
            controller = UIController()
            
            # Should handle gracefully and use defaults
            with patch('os.path.exists', return_value=True):
                config = controller._load_config()
                self.assertIsNotNone(config)
                self.assertIn('window', config)
        finally:
            os.unlink(config_path)
    
    def test_empty_config_file(self):
        """Test handling of empty configuration file."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            f.write('{}')
            config_path = f.name
        
        try:
            controller = UIController()
            
            with patch('os.path.exists', return_value=True):
                config = controller._load_config()
                self.assertIsNotNone(config)
                # Should use defaults for missing keys
                self.assertEqual(config['window']['width'], 1200)
        finally:
            os.unlink(config_path)
    
    def test_invalid_command_line_args(self):
        """Test handling of invalid command line arguments."""
        controller = UIController()
        
        # Test invalid distance
        with self.assertRaises(SystemExit):
            controller.parse_arguments(['--distance', 'invalid'])
        
        # Test missing required file
        args = controller.parse_arguments(['--runners', 'nonexistent.json'])
        self.assertEqual(args.runners, 'nonexistent.json')
    
    def test_help_argument(self):
        """Test help argument handling."""
        controller = UIController()
        
        with self.assertRaises(SystemExit):
            controller.parse_arguments(['--help'])


class TestCompleteWorkflow(unittest.TestCase):
    """Test complete user workflow from configuration to results."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_full_workflow_headless(self):
        """Test complete workflow in headless mode."""
        # Create test runner file
        runner_file = os.path.join(self.temp_dir, "runners.json")
        with open(runner_file, 'w') as f:
            json.dump(create_sample_runners(), f)
        
        # Create test config
        config_file = os.path.join(self.temp_dir, "config.json")
        config_data = {
            "race": {"dll_path": None},
            "ui": {"auto_play": False}
        }
        with open(config_file, 'w') as f:
            json.dump(config_data, f)
        
        controller = UIController()
        
        # Test complete workflow
        args = [
            '--runners', runner_file,
            '--distance', '150',
            '--export', os.path.join(self.temp_dir, 'results.json'),
            '--no-gui'
        ]
        
        with patch('builtins.print'):
            exit_code = controller.run(args)
        
        self.assertEqual(exit_code, 0)
        
        # Verify export file was created
        export_file = os.path.join(self.temp_dir, 'results.json')
        self.assertTrue(os.path.exists(export_file))
        
        # Verify export content
        with open(export_file, 'r') as f:
            results = json.load(f)
        
        self.assertIn('race_info', results)
        self.assertIn('results', results)
        self.assertIn('frames', results)


if __name__ == '__main__':
    unittest.main()