#include <iostream>
#include <windows.h>
#include "include/race_simulator.h"

int main() {
    std::cout << "Testing race_simulator.dll..." << std::endl;
    
    // Test basic functionality
    RunnerAttributes runner = {};
    runner.mood = 150;
    runner.stamina = 200;
    runner.speed = 180;
    runner.power = 160;
    runner.endurance = 170;
    runner.intelligence = 140;
    runner.body = 190;
    runner.adaptability = Adaptability::GOOD;
    runner.hasMidRaceAccel = true;
    runner.hasStartWisdom = true;
    runner.hasLateRacePower = false;
    
    Environment env = {};
    env.trackType = TrackType::PLASTIC;
    env.weather = Weather::SUNNY;
    env.windDir = WindDirection::TAILWIND;
    env.windSpeed = 2.0f;
    
    RaceResult result = CalculateRaceResult(&runner, &env);
    
    std::cout << "Race simulation completed!" << std::endl;
    std::cout << "Total time: " << result.totalTime << " seconds" << std::endl;
    std::cout << "Reaction time: " << result.reactionTime << " seconds" << std::endl;
    std::cout << "False start: " << (result.falseStart ? "Yes" : "No") << std::endl;
    std::cout << "Skills activated: ";
    for (int i = 0; i < 3; i++) {
        std::cout << (result.skillActivated[i] ? "1" : "0") << " ";
    }
    std::cout << std::endl;
    
    if (result.totalTime > 0 && result.totalTime < 20.0f) {
        std::cout << "SUCCESS: DLL is working correctly!" << std::endl;
        return 0;
    } else {
        std::cout << "ERROR: Unexpected result" << std::endl;
        return 1;
    }
}
