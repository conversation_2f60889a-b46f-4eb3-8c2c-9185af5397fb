"""
DLL Integration Tests

Tests for verifying ctypes interface and DLL communication including:
- DLL loading and initialization
- Function signature verification
- Race calculation accuracy
- Error handling for DLL failures
- Fallback mechanism when DLL unavailable
"""

import os
import sys
import unittest
from unittest.mock import patch, MagicMock
import tempfile
import json

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from race_simulator_types import RaceSimulatorAPI, create_sample_runners
from data_loader import DataLoader


class TestDLLIntegration(unittest.TestCase):
    """Test suite for DLL integration and ctypes interface."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.sample_runners = create_sample_runners()
        self.test_distance = 100.0
        self.temp_dir = tempfile.mkdtemp()
    
    def test_dll_loading_success(self):
        """Test successful DLL loading with valid path."""
        # Test with mock since actual DLL may not exist
        with patch('ctypes.CDLL') as mock_cdll:
            mock_dll = MagicMock()
            mock_cdll.return_value = mock_dll
            
            api = RaceSimulatorAPI("fake_dll.dll")
            self.assertIsNotNone(api.dll)
            mock_cdll.assert_called_once_with("fake_dll.dll")
    
    def test_dll_loading_failure_fallback(self):
        """Test graceful handling when DLL loading fails."""
        test_cases = [
            "nonexistent.dll",
            None,  # Will trigger automatic search
            "",    # Empty path
        ]
        
        for dll_path in test_cases:
            with self.subTest(dll_path=dll_path):
                with patch('os.path.exists', return_value=False):
                    with self.assertRaises(FileNotFoundError):
                        RaceSimulatorAPI(dll_path)
    
    def test_dll_function_signature_setup(self):
        """Test that function signatures are properly configured."""
        with patch('ctypes.CDLL') as mock_cdll:
            mock_dll = MagicMock()
            mock_cdll.return_value = mock_dll
            
            api = RaceSimulatorAPI("fake_dll.dll")
            
            # Verify function signatures are set
            self.assertTrue(hasattr(api.dll, 'calculate_race'))
            self.assertTrue(hasattr(api.dll, 'free_race_data'))
            self.assertTrue(hasattr(api.dll, 'get_last_error'))
            
            # Verify argtypes are configured
            self.assertIsNotNone(api.dll.calculate_race.argtypes)
            self.assertIsNotNone(api.dll.free_race_data.argtypes)
            self.assertIsNotNone(api.dll.get_last_error.argtypes)
    
    def test_race_calculation_with_dll(self):
        """Test race calculation through DLL interface."""
        with patch('ctypes.CDLL') as mock_cdll:
            # Setup mock DLL
            mock_dll = MagicMock()
            mock_cdll.return_value = mock_dll
            
            # Configure mock return values
            mock_dll.calculate_race.return_value = 0  # Success
            
            api = RaceSimulatorAPI("fake_dll.dll")
            
            # Test calculation
            result = api.calculate_race(self.sample_runners, self.test_distance)
            
            # Verify structure was called correctly
            self.assertTrue(mock_dll.calculate_race.called)
            self.assertEqual(result['total_runners'], len(self.sample_runners))
            self.assertEqual(result['race_distance'], self.test_distance)
    
    def test_dll_error_handling(self):
        """Test proper error handling when DLL functions fail."""
        with patch('ctypes.CDLL') as mock_cdll:
            mock_dll = MagicMock()
            mock_cdll.return_value = mock_dll
            
            # Simulate DLL function failure
            mock_dll.calculate_race.return_value = 1  # Error code
            mock_dll.get_last_error.return_value = b"Test error message"
            
            api = RaceSimulatorAPI("fake_dll.dll")
            
            with self.assertRaises(RuntimeError) as cm:
                api.calculate_race(self.sample_runners, self.test_distance)
            
            self.assertIn("Test error message", str(cm.exception))
    
    def test_memory_cleanup(self):
        """Test that allocated memory is properly freed."""
        with patch('ctypes.CDLL') as mock_cdll:
            mock_dll = MagicMock()
            mock_cdll.return_value = mock_dll
            mock_dll.calculate_race.return_value = 0
            
            api = RaceSimulatorAPI("fake_dll.dll")
            api.calculate_race(self.sample_runners, self.test_distance)
            
            # Verify cleanup function was called
            self.assertTrue(mock_dll.free_race_data.called)
    
    def test_data_loader_dll_fallback(self):
        """Test DataLoader fallback when DLL is unavailable."""
        # Test DataLoader with non-existent DLL
        loader = DataLoader("nonexistent.dll")
        
        # Should use fallback mechanism
        race_data = loader.calculate_race(self.sample_runners, self.test_distance)
        
        # Verify fallback data structure
        self.assertIsNotNone(race_data)
        self.assertIn('race_info', race_data)
        self.assertIn('results', race_data)
        self.assertIn('frames', race_data)
        self.assertEqual(race_data['race_info']['total_runners'], len(self.sample_runners))
    
    def test_runner_data_conversion(self):
        """Test proper conversion of runner data to ctypes structures."""
        with patch('ctypes.CDLL') as mock_cdll:
            mock_dll = MagicMock()
            mock_cdll.return_value = mock_dll
            mock_dll.calculate_race.return_value = 0
            
            api = RaceSimulatorAPI("fake_dll.dll")
            
            # Test with various runner data formats
            test_runners = [
                {"id": 1, "max_speed": 12.5, "acceleration": 2.8, "endurance": 0.85},
                {"id": 2, "max_speed": 11.8, "acceleration": 3.2, "endurance": 0.90},
            ]
            
            result = api.calculate_race(test_runners, self.test_distance)
            
            # Verify the call was made with correct structure
            call_args = mock_dll.calculate_race.call_args
            self.assertIsNotNone(call_args)
            
    def test_invalid_runner_data_handling(self):
        """Test handling of invalid runner data."""
        invalid_runners = [
            {"id": "invalid", "max_speed": 12.5, "acceleration": 2.8, "endurance": 0.85},  # Invalid ID
            {"id": 1, "max_speed": "fast", "acceleration": 2.8, "endurance": 0.85},  # Invalid speed
            {"id": 1, "max_speed": 12.5},  # Missing required fields
        ]
        
        for invalid_runner in invalid_runners:
            with self.subTest(invalid_data=invalid_runner):
                with patch('ctypes.CDLL') as mock_cdll:
                    mock_dll = MagicMock()
                    mock_cdll.return_value = mock_dll
                    
                    api = RaceSimulatorAPI("fake_dll.dll")
                    
                    # Should handle conversion gracefully
                    try:
                        api.calculate_race([invalid_runner], self.test_distance)
                    except (ValueError, TypeError):
                        pass  # Expected for invalid data


class TestDLLPathResolution(unittest.TestCase):
    """Test DLL path resolution logic."""
    
    def test_automatic_dll_search(self):
        """Test automatic DLL search in common locations."""
        search_paths = [
            "race_simulator.dll",
            "./race_simulator.dll",
            "../race_simulator.dll",
            "lib/race_simulator.dll"
        ]
        
        for path in search_paths:
            with self.subTest(path=path):
                with patch('os.path.exists') as mock_exists:
                    with patch('ctypes.CDLL') as mock_cdll:
                        mock_exists.return_value = True
                        mock_cdll.return_value = MagicMock()
                        
                        api = RaceSimulatorAPI()
                        self.assertIsNotNone(api.dll)
                        mock_cdll.assert_called_with(path)
                        break
    
    def test_custom_dll_path(self):
        """Test using custom DLL path."""
        custom_path = "/custom/path/race_simulator.dll"
        
        with patch('ctypes.CDLL') as mock_cdll:
            mock_dll = MagicMock()
            mock_cdll.return_value = mock_dll
            
            api = RaceSimulatorAPI(custom_path)
            mock_cdll.assert_called_with(custom_path)


if __name__ == '__main__':
    unittest.main()