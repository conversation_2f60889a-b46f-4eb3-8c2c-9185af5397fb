#include "race_simulator.h"
#include "environment.h"
#include "random_generator.h"
#include <algorithm>
#include <cmath>

namespace RaceSimulator {

    float CalculateTrackMultiplier(TrackType trackType) {
        switch (trackType) {
            case TrackType::PLASTIC:
                return 1.05f;
            case TrackType::CINDER:
                return 0.98f;
            case TrackType::GRASS:
                return 0.92f;
            case TrackType::DIRT:
                return 1.02f;
            default:
                return 1.0f;
        }
    }

    float CalculateWeatherMultiplier(Weather weather) {
        switch (weather) {
            case Weather::SUNNY:
                return 1.03f;
            case Weather::VERY_SUNNY:
                return 1.05f;
            case Weather::CLOUDY:
                return 1.0f;
            case Weather::LIGHT_RAIN:
                return 0.98f;
            case Weather::SMALL_RAIN:
                return 0.95f;
            case Weather::MEDIUM_RAIN:
                return 0.92f;
            case Weather::HEAVY_RAIN:
                return 0.88f;
            default:
                return 1.0f;
        }
    }

    float CalculateWindMultiplier(WindDirection windDir, float windSpeed) {
        float baseMultiplier = 1.0f;
        float speedFactor = windSpeed / 10.0f; // Normalize to 0-1 range

        switch (windDir) {
            case WindDirection::HEADWIND:
                baseMultiplier = 1.0f - speedFactor * 0.05f;
                break;
            case WindDirection::TAILWIND:
                baseMultiplier = 1.0f + speedFactor * 0.03f;
                break;
            default:
                baseMultiplier = 1.0f;
                break;
        }

        return std::max(0.85f, std::min(baseMultiplier, 1.15f));
    }

    float CalculateEnvironmentMultiplier(const Environment* env) {
        if (!env) return 1.0f;

        float trackMultiplier = CalculateTrackMultiplier(env->trackType);
        float weatherMultiplier = CalculateWeatherMultiplier(env->weather);
        float windMultiplier = CalculateWindMultiplier(env->windDir, env->windSpeed);

        return trackMultiplier * weatherMultiplier * windMultiplier;
    }

    float CalculateEnvironmentalRandomness(const Environment* env) {
        if (!env) return 0.02f;

        float baseRandomness = 0.02f;

        // Weather affects randomness
        switch (env->weather) {
            case Weather::HEAVY_RAIN:
            case Weather::MEDIUM_RAIN:
                baseRandomness += 0.03f;
                break;
            case Weather::SMALL_RAIN:
            case Weather::LIGHT_RAIN:
                baseRandomness += 0.02f;
                break;
            case Weather::VERY_SUNNY:
                baseRandomness += 0.015f;
                break;
            case Weather::CLOUDY:
                baseRandomness += 0.01f;
                break;
            default:
                break;
        }

        // Wind affects randomness
        baseRandomness += env->windSpeed / 100.0f;

        return std::min(baseRandomness, 0.08f);
    }

    float CalculateEnvironmentalFalseStartImpact(const Environment* env) {
        if (!env) return 0.0f;

        float impact = 0.0f;

        // Weather impact on false starts
        switch (env->weather) {
            case Weather::HEAVY_RAIN:
            case Weather::MEDIUM_RAIN:
                impact += 0.01f;
                break;
            case Weather::SMALL_RAIN:
            case Weather::LIGHT_RAIN:
                impact += 0.005f;
                break;
            case Weather::CLOUDY:
                impact += 0.003f;
                break;
            default:
                break;
        }

        // Wind impact
        if (env->windDir == WindDirection::HEADWIND) {
            impact += env->windSpeed / 1000.0f;
        }

        return std::min(impact, 0.02f);
    }

    float CalculateEnvironmentalSkillImpact(const Environment* env) {
        if (!env) return 1.0f;

        float skillMultiplier = 1.0f;

        // Adverse conditions reduce skill effectiveness
        switch (env->weather) {
            case Weather::HEAVY_RAIN:
            case Weather::MEDIUM_RAIN:
                skillMultiplier *= 0.85f;
                break;
            case Weather::SMALL_RAIN:
            case Weather::LIGHT_RAIN:
                skillMultiplier *= 0.9f;
                break;
            case Weather::CLOUDY:
                skillMultiplier *= 0.92f;
                break;
            case Weather::VERY_SUNNY:
                skillMultiplier *= 0.95f;
                break;
            default:
                break;
        }

        // Strong wind reduces skill effectiveness
        if (env->windSpeed > 5.0f) {
            skillMultiplier *= (1.0f - (env->windSpeed - 5.0f) / 50.0f);
        }

        return std::max(skillMultiplier, 0.7f);
    }

    float CalculateOverallAttributeMultiplier(const RunnerAttributes* runner, const Environment* env) {
        if (!runner) return 1.0f;

        // Base attribute calculation
        float speedFactor = static_cast<float>(runner->speed) / 255.0f;
        float staminaFactor = static_cast<float>(runner->stamina) / 255.0f;
        float powerFactor = static_cast<float>(runner->power) / 255.0f;
        float enduranceFactor = static_cast<float>(runner->endurance) / 255.0f;
        float bodyFactor = static_cast<float>(runner->body) / 255.0f;
        float adaptabilityFactor = static_cast<float>(runner->adaptability) / 255.0f;

        // Weighted combination of attributes
        float baseMultiplier = 0.8f +
            speedFactor * 0.3f +
            staminaFactor * 0.2f +
            powerFactor * 0.25f +
            enduranceFactor * 0.15f +
            bodyFactor * 0.1f;

        // Environmental adaptation
        if (env) {
            float envMultiplier = CalculateEnvironmentMultiplier(env);
            float adaptationBonus = adaptabilityFactor * 0.1f * (1.0f - std::abs(envMultiplier - 1.0f));
            baseMultiplier += adaptationBonus;
        }

        return std::max(0.5f, std::min(baseMultiplier, 1.5f));
    }

}
