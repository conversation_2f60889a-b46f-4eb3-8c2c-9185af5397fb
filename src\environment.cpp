#include "race_simulator.h"
#include "random_generator.h"
#include <algorithm>

namespace RaceSimulator {

    float CalculateTrackMultiplier(TrackType trackType) {
        switch (trackType) {
            case TrackType::PLASTIC:
                return 1.0f;  // 最佳赛道
            case TrackType::CINDER:
                return 1.02f;  // 煤渣稍慢
            case TrackType::GRASS:
                return 1.05f;  // 草地较慢
            case TrackType::DIRT:
                return 1.08f;  // 土地最慢
            default:
                return 1.0f;
        }
    }

    // 天气系数
    float CalculateWeatherMultiplier(Weather weather) {
        switch (weather) {
            case Weather::VERY_SUNNY:
                return 0.98f;  // 大晴有利
            case Weather::SUNNY:
                return 1.0f;   // 标准天气
            case Weather::CLOUDY:
                return 1.01f;  // 乌云轻微影响
            case Weather::LIGHT_RAIN:
                return 1.02f;  // 微雨
            case Weather::SMALL_RAIN:
                return 1.05f;  // 小雨
            case Weather::MEDIUM_RAIN:
                return 1.1f;   // 中雨
            case Weather::HEAVY_RAIN:
                return 1.2f;   // 大雨严重影响
            default:
                return 1.0f;
        }
    }

    // 风速影响计算
    float CalculateWindMultiplier(WindDirection windDir, float windSpeed) {
        // 限制风速范围
        windSpeed = std::max(0.0f, std::min(10.0f, windSpeed));
        
        if (windDir == WindDirection::TAILWIND) {
            // 顺风：减少时间，最大2%提升（10m/s）
            return 1.0f - (windSpeed / 10.0f) * 0.02f;
        } else {
            // 逆风：增加时间，最大3%惩罚（10m/s）
            return 1.0f + (windSpeed / 10.0f) * 0.03f;
        }
    }

    // 综合环境系数
    float CalculateEnvironmentMultiplier(const Environment* env) {
        float multiplier = 1.0f;
        
        multiplier *= CalculateTrackMultiplier(env->trackType);
        multiplier *= CalculateWeatherMultiplier(env->weather);
        multiplier *= CalculateWindMultiplier(env->windDir, env->windSpeed);
        
        return multiplier;
    }

    // 环境对抢跑概率的影响
    float CalculateEnvironmentalFalseStartImpact(const Environment* env) {
        float impact = 0.0f;
        
        // 天气影响抢跑概率
        switch (env->weather) {
            case Weather::HEAVY_RAIN:
                impact += 0.02f;  // 大雨增加2%
                break;
            case Weather::MEDIUM_RAIN:
                impact += 0.01f;  // 中雨增加1%
                break;
            case Weather::SMALL_RAIN:
                impact += 0.005f; // 小雨增加0.5%
                break;
            default:
                break;
        }
        
        // 风速影响
        if (env->windSpeed > 5.0f) {
            impact += 0.005f;  // 大风增加0.5%
        }
        
        return impact;
    }

    // 环境对技能触发的影响
    float CalculateEnvironmentalSkillImpact(const Environment* env) {
        float multiplier = 1.0f;
        
        // 恶劣天气降低技能触发概率
        switch (env->weather) {
            case Weather::HEAVY_RAIN:
                multiplier *= 0.8f;
                break;
            case Weather::MEDIUM_RAIN:
                multiplier *= 0.9f;
                break;
            case Weather::SMALL_RAIN:
                multiplier *= 0.95f;
                break;
            default:
                break;
        }
        
        // 不利赛道也影响技能发挥
        if (env->trackType == TrackType::GRASS || env->trackType == TrackType::DIRT) {
            multiplier *= 0.95f;
        }
        
        return multiplier;
    }

    // 环境随机波动
    float CalculateEnvironmentalRandomness(const Environment* env) {
        float baseNoise = 0.01f;  // 基础1%随机波动
        
        // 恶劣天气增加随机性
        switch (env->weather) {
            case Weather::HEAVY_RAIN:
                baseNoise += 0.02f;
                break;
            case Weather::MEDIUM_RAIN:
                baseNoise += 0.01f;
                break;
            case Weather::SMALL_RAIN:
                baseNoise += 0.005f;
                break;
            default:
                break;
        }
        
        // 不利赛道也增加随机性
        if (env->trackType == TrackType::GRASS || env->trackType == TrackType::DIRT) {
            baseNoise += 0.01f;
        }
        
        return baseNoise;
    }
}