@echo off
echo ASCII Running Simulation - Test Suite
echo =====================================
echo.

set ALL_PASSED=1

REM Run unit tests
echo Running Unit Tests...
echo.

if exist "build\x64\Release\bin\Release\test_skill_system.exe" (
    echo 1. Skill System Tests:
    build\x64\Release\bin\Release\test_skill_system.exe
    if %errorlevel% neq 0 set ALL_PASSED=0
    echo.
) else (
    echo 1. Skill System Tests: NOT FOUND
    set ALL_PASSED=0
)

if exist "build\x64\Release\bin\Release\test_race_engine.exe" (
    echo 2. Race Engine Tests:
    build\x64\Release\bin\Release\test_race_engine.exe
    if %errorlevel% neq 0 set ALL_PASSED=0
    echo.
) else (
    echo 2. Race Engine Tests: NOT FOUND
    set ALL_PASSED=0
)

REM Run integration tests
echo Running Integration Tests...
echo.

if exist "build\x64\Release\bin\Release\integration_test.exe" (
    echo 3. Integration Tests:
    build\x64\Release\bin\Release\integration_test.exe --quick
    if %errorlevel% neq 0 set ALL_PASSED=0
    echo.
) else (
    echo 3. Integration Tests: NOT FOUND
    set ALL_PASSED=0
)

REM Summary
echo =====================================
if %ALL_PASSED%==1 (
    echo ✅ ALL TESTS PASSED!
) else (
    echo ❌ SOME TESTS FAILED!
)
echo.

REM Run CTest if available
if exist "build\x64\Release\CTestTestfile.cmake" (
    echo Running CTest...
    cd build\x64\Release
    ctest --output-on-failure
    cd ..\..\..
)

pause