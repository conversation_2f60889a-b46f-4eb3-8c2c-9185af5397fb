# 跑步比赛模拟游戏C++ DLL设计文档

## 概述

本设计文档基于requirements.md中的需求，定义了一个C++ DLL的架构和实现细节，用于计算100米跑步比赛成绩。系统严格遵循smic_algo.txt中定义的规则，不考虑现实物理模拟。

## 架构设计

### 整体架构
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   游戏客户端    │───▶│  C++ DLL接口层   │───▶│  成绩计算引擎   │
│   (C#/C++/等)   │◀───│  (C风格API)      │◀───│  (核心算法)     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### 核心组件
1. **API接口层**：提供标准C风格DLL接口
2. **数据验证层**：验证输入参数有效性
3. **属性计算器**：计算各属性对成绩的影响
4. **技能处理器**：处理固有技能的触发和效果
5. **环境计算器**：处理环境因素影响
6. **分段计算器**：计算五个赛程阶段的时间

## 组件和接口

### 1. 数据结构定义

#### 1.1 选手属性结构体
```cpp
struct RunnerAttributes {
    unsigned char mood;           // 心情 (0-255)
    unsigned char stamina;        // 体力 (0-255)
    unsigned char speed;          // 速度 (0-255)
    unsigned char power;          // 力量 (0-255)
    unsigned char endurance;      // 耐力 (0-255)
    unsigned char intelligence;   // 智力 (0-255)
    unsigned char body;           // 身体 (0-255)
    unsigned char adaptability;   // 适应性 (0-255)
};
```

#### 1.2 环境参数结构体
```cpp
struct EnvironmentParams {
    int trackType;    // 赛道类型: 0=塑胶, 1=草地, 2=煤渣, 3=土地
    int weather;      // 天气: 0=晴, 1=大晴, 2=乌, 3=微雨, 4=小雨, 5=中雨, 6=大雨
    float windSpeed;  // 风速 (正值顺风，负值逆风)
};
```

#### 1.3 技能配置结构体
```cpp
struct SkillConfig {
    bool hasMidBoost;       // 中途加速
    bool hasStartWisdom;    // 起跑智慧
    bool hasLatePower;      // 后程发力
};
```

### 2. API接口定义

#### 2.1 主计算函数
```cpp
extern "C" {
    // 计算跑步成绩
    __declspec(dllexport) float CalculateRaceTime(
        const RunnerAttributes* runner,
        const EnvironmentParams* env,
        const SkillConfig* skills
    );
    
    // 获取版本信息
    __declspec(dllexport) const char* GetVersion();
    
    // 获取最后错误信息
    __declspec(dllexport) const char* GetLastError();
}
```

### 3. 核心算法模块

#### 3.1 计算流程
```
CalculateRaceTime()
├── 输入验证
├── 基础时间计算
├── 属性效果应用
├── 技能效果应用
├── 环境效果应用
├── 分段计算
└── 结果汇总
```

#### 3.2 分段计算器
```cpp
class SegmentCalculator {
public:
    struct SegmentResult {
        float reactionTime;   // 起跑反应时间
        float segment1;       // 0-30m
        float segment2;       // 30-60m
        float segment3;       // 60-95m
        float segment4;       // 95-100m
    };
    
    SegmentResult calculate(const RunnerAttributes& runner,
                          const EnvironmentParams& env,
                          const SkillConfig& skills);
};
```

## 数据模型

### 1. 属性映射表

#### 1.1 心情效果映射
| 心情值范围 | 档位 | 效果 |
|-----------|------|------|
| 192-255   | 极佳 | +2%  |
| 128-191   | 好   | 0%   |
| 64-127    | 不良 | -3%  |
| 0-63      | 差   | -5%  |

#### 1.2 适应性效果映射
| 适应性值 | 塑胶跑道 | 草地 | 煤渣跑道 | 土地 |
|----------|----------|------|----------|------|
| 192-255  | +1%      | 0%   | -1%      | -2%  |
| 128-191  | 0%       | -1%  | -2%      | -3%  |
| 64-127   | -1%      | -2%  | -3%      | -4%  |
| 0-63     | -2%      | -3%  | -4%      | -5%  |

### 2. 技能效果计算

#### 2.1 起跑智慧计算
```cpp
float calculateStartWisdomEffect(float originalReactionTime) {
    return (originalReactionTime - 0.1f) * 0.7f + 0.1f;
}
```

#### 2.2 力量与反应时间关系
```cpp
float calculateReactionTime(unsigned char power) {
    if (power >= 250) {
        return 0.1f + (rand() % 3) * 0.01f; // 0.10-0.12s
    }
    float base = 0.15f;
    float reduction = (power / 255.0f) * 0.05f;
    return base - reduction;
}
```

## 错误处理

### 1. 错误类型定义
```cpp
enum class ErrorCode {
    SUCCESS = 0,
    INVALID_RUNNER_ATTR = -1,
    INVALID_ENV_PARAMS = -2,
    NULL_POINTER = -3,
    CALCULATION_ERROR = -4
};
```

### 2. 错误处理策略
- **输入验证失败**：返回-1.0f作为错误标志
- **空指针检查**：返回-2.0f
- **边界值处理**：使用最接近的有效值
- **计算异常**：返回-3.0f

### 3. 错误信息获取
```cpp
thread_local std::string lastErrorMessage;

const char* GetLastError() {
    return lastErrorMessage.c_str();
}
```

## 测试策略

### 1. 单元测试
#### 1.1 属性计算测试
- 边界值测试（0, 127, 255）
- 线性插值测试
- 属性组合效果测试

#### 1.2 技能触发测试
- 智力250+时的95%触发率验证
- 技能效果计算准确性
- 多技能组合效果测试

#### 1.3 环境效果测试
- 各种天气条件下的计算
- 不同跑道类型的适应性测试
- 风速影响计算验证

### 2. 集成测试
#### 2.1 端到端测试
- 9.5秒最佳成绩验证
- 极端属性组合测试
- 随机种子一致性测试

#### 2.2 性能测试
- 单次计算时间<1ms
- 内存使用<1MB
- 并发调用测试

### 3. 命令行测试工具
`basic_test.exe` 是一个命令行工具，用于对DLL进行快速的功能验证和基准测试。它提供了以下功能：
- **单次模拟**: 允许用户输入自定义的角色属性和环境参数，运行一次模拟并查看详细结果。
- **多次模拟**: 对同一组参数运行多次模拟，并提供统计数据（如平均时间、最佳/最差时间、技能触发率等）。
- **模拟比赛**: 自动生成8名随机属性的角色，在相同的环境下进行比赛，并输出最终排名。此功能对于验证算法在不同角色之间的区分度非常有用。

### 4. 测试用例示例
```cpp
// 测试最佳成绩
RunnerAttributes optimal = {255, 255, 255, 255, 255, 255, 255, 255};
EnvironmentParams bestEnv = {0, 0, 0.0f}; // 塑胶跑道，晴天，无风
SkillConfig allSkills = {true, true, true};
float result = CalculateRaceTime(&optimal, &bestEnv, &allSkills);
// 预期：result ≈ 9.5f
```

## 实现注意事项

### 1. 数值稳定性
- 使用double进行中间计算，最后转为float
- 避免除零错误
- 处理浮点数精度问题

### 2. 随机性控制
- 使用可预测的随机种子（基于输入参数）
- 确保相同输入产生相同输出（调试模式）
- 支持设置随机种子用于测试

### 3. 性能优化
- 预计算查找表减少运行时计算
- 避免动态内存分配
- 使用内联函数减少函数调用开销

### 4. 线程安全
- 避免全局可变状态
- 使用线程局部存储存储错误信息
- 确保无竞态条件