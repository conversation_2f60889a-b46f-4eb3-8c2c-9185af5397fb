#pragma once

#include <cstdint>
#include "race_simulator.h"

namespace RaceSimulator {
    RACE_SIMULATOR_API float RandomFloat(float min, float max);
    RACE_SIMULATOR_API int RandomInt(int min, int max);
    RACE_SIMULATOR_API float RandomNormal(float mean, float stddev);
    RACE_SIMULATOR_API bool RandomBool(float probability);
    RACE_SIMULATOR_API float AttributeBiasedRandom(float base, float range, uint8_t attribute);
}