"""
Data loader for DLL integration and race calculation.
Handles loading the DLL, calculating race results, and preparing animation data.
"""

import json
import os
from typing import List, Dict, Any, Optional
from race_simulator_types import RaceSimulatorAPI, create_sample_runners


class DataLoader:
    """Handles all data loading and race calculation operations."""
    
    def __init__(self, dll_path: str = None):
        """Initialize the data loader with DLL integration."""
        try:
            self.api = RaceSimulatorAPI(dll_path)
        except (FileNotFoundError, RuntimeError):
            # DLL not available, will use fallback
            self.api = None
        self.race_data = None
        self.animation_frames = None
        
    def load_runners_from_file(self, filepath: str) -> List[Dict[str, float]]:
        """
        Load runner data from JSON file.
        
        Args:
            filepath: Path to JSON file containing runner data
            
        Returns:
            List of runner dictionaries
        """
        try:
            with open(filepath, 'r') as f:
                data = json.load(f)
                
            if isinstance(data, dict) and 'runners' in data:
                return data['runners']
            elif isinstance(data, list):
                return data
            else:
                raise ValueError("Invalid JSON format. Expected list of runners or object with 'runners' key.")
                
        except FileNotFoundError:
            print(f"Runner file {filepath} not found. Using sample data.")
            return create_sample_runners()
        except json.JSONDecodeError as e:
            print(f"Error parsing JSON file {filepath}: {e}. Using sample data.")
            return create_sample_runners()
    
    def calculate_race(self, runners: List[Dict[str, float]], distance: float = 100.0) -> Dict[str, Any]:
        """
        Calculate race results using the DLL or fallback.
        
        Args:
            runners: List of runner dictionaries
            distance: Race distance in meters (default: 100m)
            
        Returns:
            Complete race data including results and animation frames
        """
        try:
            if self.api is not None:
                self.race_data = self.api.calculate_race(runners, distance)
                
                # Organize animation frames by time for efficient playback
                self.animation_frames = self._organize_animation_frames()
                
                return {
                    'race_info': {
                        'distance': distance,
                        'total_runners': len(runners),
                        'total_frames': len(self.animation_frames)
                    },
                    'results': self.race_data['results'],
                    'frames': self.animation_frames
                }
            else:
                # Use fallback simulation
                self.race_data = self._get_fallback_data()
                self.animation_frames = self.race_data['frames']
                return self.race_data
                
        except Exception as e:
            print(f"Error calculating race: {e}")
            # Return sample data as fallback
            return self._get_fallback_data()
    
    def _organize_animation_frames(self) -> List[Dict[str, Any]]:
        """
        Organize animation snapshots into discrete time frames.
        
        Returns:
            List of frames, each containing all runner positions at a specific time
        """
        if not self.race_data or 'snapshots' not in self.race_data:
            return []
        
        snapshots = self.race_data['snapshots']
        
        # Group snapshots by time
        time_groups = {}
        for snapshot in snapshots:
            time_key = round(snapshot['time'], 3)  # Round to milliseconds
            if time_key not in time_groups:
                time_groups[time_key] = []
            time_groups[time_key].append(snapshot)
        
        # Convert to sorted list of frames
        frames = []
        for time in sorted(time_groups.keys()):
            runners = {}
            for snapshot in time_groups[time]:
                runners[snapshot['runner_id']] = {
                    'position': snapshot['position'],
                    'speed': snapshot['current_speed'],
                    'acceleration': snapshot['current_acceleration']
                }
            
            frames.append({
                'time': time,
                'runners': runners
            })
        
        return frames
    
    def _get_fallback_data(self) -> Dict[str, Any]:
        """Generate fallback data when DLL is not available."""
        runners = create_sample_runners()
        
        # Simple linear simulation as fallback
        frames = []
        duration = 12.0  # 12 seconds
        frame_count = int(duration * 30)  # 30 FPS
        
        for i in range(frame_count):
            time = i / 30.0
            frame_runners = {}
            
            for runner in runners:
                runner_id = runner['id']
                # Simple linear progression with some variation
                progress = min(time / (10.0 + runner['id'] * 0.5), 1.0)
                position = progress * 100.0  # 100m race
                
                frame_runners[runner_id] = {
                    'position': position,
                    'speed': runner['max_speed'] * (1.0 - progress * 0.3),
                    'acceleration': 0.0
                }
            
            frames.append({
                'time': time,
                'runners': frame_runners
            })
        
        # Generate mock results
        results = []
        for i, runner in enumerate(runners):
            results.append({
                'runner_id': runner['id'],
                'finish_time': 10.0 + runner['id'] * 0.5,
                'max_speed_achieved': runner['max_speed'],
                'average_speed': 100.0 / (10.0 + runner['id'] * 0.5),
                'final_position': i + 1,
                'did_finish': True
            })
        
        return {
            'race_info': {
                'distance': 100.0,
                'total_runners': len(runners),
                'total_frames': len(frames)
            },
            'results': results,
            'frames': frames
        }
    
    def get_race_summary(self) -> str:
        """Generate a human-readable race summary."""
        if not self.race_data:
            return "No race data available"
        
        results = sorted(self.race_data['results'], key=lambda x: x['final_position'])
        
        race_info = self.race_data.get('race_info', {})
        total_runners = race_info.get('total_runners', len(results))
        distance = race_info.get('distance', 100.0)
        
        summary = f"Race Summary - {total_runners} Runners\n"
        summary += f"Distance: {distance} meters\n\n"
        
        for result in results:
            if result['did_finish']:
                avg_speed = result.get('average_speed', 0.0)
                summary += f"{result['final_position']:2d}. Runner {result['runner_id']:2d}: {result['finish_time']:6.2f}s ({avg_speed:5.2f} m/s avg)\n"
            else:
                summary += f"{result['final_position']:2d}. Runner {result['runner_id']:2d}: DNF\n"
        
        return summary
    
    def export_results(self, filepath: str) -> bool:
        """
        Export race results to JSON file.
        
        Args:
            filepath: Output file path
            
        Returns:
            True if successful, False otherwise
        """
        if not self.race_data:
            return False
        
        try:
            output_data = {
                'race_info': {
                    'distance': self.race_data['race_info']['distance'],
                    'total_runners': self.race_data['race_info']['total_runners'],
                    'total_frames': len(self.animation_frames) if self.animation_frames else 0
                },
                'results': self.race_data['results'],
                'frames': self.animation_frames
            }
            
            with open(filepath, 'w') as f:
                json.dump(output_data, f, indent=2)
            
            return True
            
        except Exception as e:
            print(f"Error exporting results: {e}")
            return False
    
    def get_frame_at_time(self, time: float) -> Optional[Dict[str, Any]]:
        """
        Get the frame closest to the specified time.
        
        Args:
            time: Time in seconds
            
        Returns:
            Frame data or None if not found
        """
        if not self.animation_frames:
            return None
        
        # Find the closest frame
        closest_frame = None
        min_diff = float('inf')
        
        for frame in self.animation_frames:
            diff = abs(frame['time'] - time)
            if diff < min_diff:
                min_diff = diff
                closest_frame = frame
        
        return closest_frame
    
    def get_total_duration(self) -> float:
        """Get the total duration of the race animation."""
        if not self.animation_frames:
            return 0.0
        
        return self.animation_frames[-1]['time']
    
    def get_runner_colors(self) -> Dict[int, tuple]:
        """Generate consistent colors for each runner."""
        if not self.race_data:
            return {}
        
        colors = [
            (255, 0, 0),    # Red
            (0, 255, 0),    # Green
            (0, 0, 255),    # Blue
            (255, 255, 0),  # Yellow
            (255, 0, 255),  # Magenta
            (0, 255, 255),  # Cyan
            (255, 128, 0),  # Orange
            (128, 0, 255),  # Purple
            (255, 192, 203), # Pink
            (128, 128, 128)  # Gray
        ]
        
        runner_colors = {}
        for i, result in enumerate(self.race_data['results']):
            color_index = i % len(colors)
            runner_colors[result['runner_id']] = colors[color_index]
        
        return runner_colors


def main():
    """Test the data loader."""
    loader = DataLoader()
    
    # Test with sample runners
    runners = create_sample_runners()
    race_data = loader.calculate_race(runners, 100.0)
    
    print(loader.get_race_summary())
    print(f"\nAnimation frames: {len(loader.animation_frames)}")
    print(f"Total duration: {loader.get_total_duration():.2f} seconds")


if __name__ == "__main__":
    main()