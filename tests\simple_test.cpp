#include <iostream>
#include <iomanip>
#include <string>
#include "race_simulator.h"
#include "skill_system.h"
#include "runner_attributes.h"
#include "environment.h"

void printHeader() {
    std::cout << "========================================\n";
    std::cout << "    Race Simulator - Manual Test Tool\n";
    std::cout << "========================================\n\n";
}

void printRunnerAttributes(const RunnerAttributes& runner) {
    std::cout << "Runner Attributes:\n";
    std::cout << "  Mood: " << static_cast<int>(runner.mood) << "/255\n";
    std::cout << "  Stamina: " << static_cast<int>(runner.stamina) << "/255\n";
    std::cout << "  Speed: " << static_cast<int>(runner.speed) << "/255\n";
    std::cout << "  Power: " << static_cast<int>(runner.power) << "/255\n";
    std::cout << "  Endurance: " << static_cast<int>(runner.endurance) << "/255\n";
    std::cout << "  Intelligence: " << static_cast<int>(runner.intelligence) << "/255\n";
    std::cout << "  Body: " << static_cast<int>(runner.body) << "/255\n";
    std::cout << "  Adaptability: " << static_cast<int>(runner.adaptability) << "\n";
    std::cout << "  Skills: ";
    if (runner.hasMidRaceAccel) std::cout << "[Mid Race Accel] ";
    if (runner.hasStartWisdom) std::cout << "[Start Wisdom] ";
    if (runner.hasLateRacePower) std::cout << "[Late Race Power] ";
    std::cout << "\n\n";
}

void printEnvironment(const Environment& env) {
    std::cout << "Environment:\n";
    std::cout << "  Track Type: ";
    switch (env.trackType) {
        case TrackType::PLASTIC: std::cout << "Plastic"; break;
        case TrackType::GRASS: std::cout << "Grass"; break;
        case TrackType::CINDER: std::cout << "Cinder"; break;
        case TrackType::DIRT: std::cout << "Dirt"; break;
    }
    std::cout << "\n  Weather: ";
    switch (env.weather) {
        case Weather::SUNNY: std::cout << "Sunny"; break;
        case Weather::VERY_SUNNY: std::cout << "Very Sunny"; break;
        case Weather::CLOUDY: std::cout << "Cloudy"; break;
        case Weather::LIGHT_RAIN: std::cout << "Light Rain"; break;
        case Weather::SMALL_RAIN: std::cout << "Small Rain"; break;
        case Weather::MEDIUM_RAIN: std::cout << "Medium Rain"; break;
        case Weather::HEAVY_RAIN: std::cout << "Heavy Rain"; break;
    }
    std::cout << "\n  Wind Direction: " << (env.windDir == WindDirection::HEADWIND ? "Headwind" : "Tailwind");
    std::cout << "\n  Wind Speed: " << env.windSpeed << " m/s\n\n";
}

void printDetailedCalculations(const RunnerAttributes& runner, const Environment& env) {
    std::cout << "========== Detailed Calculations ==========\n\n";
    
    // Basic attribute calculations
    std::cout << "1. Basic Attribute Calculations:\n";
    Mood mood = RaceSimulator::GetMoodFromValue(runner.mood);
    float moodMultiplier = RaceSimulator::CalculateMoodMultiplier(mood);
    std::cout << "   Mood Multiplier: " << std::fixed << std::setprecision(3) << moodMultiplier << "\n";
    
    float adaptMultiplier = RaceSimulator::CalculateAdaptabilityMultiplier(runner.adaptability, env.trackType);
    std::cout << "   Adaptability Multiplier: " << adaptMultiplier << "\n";
    
    float staminaMultiplier = RaceSimulator::CalculateStaminaMultiplier(runner.stamina);
    std::cout << "   Stamina Multiplier: " << staminaMultiplier << "\n";
    
    float enduranceMultiplier = RaceSimulator::CalculateEnduranceMultiplier(runner.endurance);
    std::cout << "   Endurance Multiplier: " << enduranceMultiplier << "\n";
    
    float overallMultiplier = RaceSimulator::CalculateOverallAttributeMultiplier(&runner, &env);
    std::cout << "   Overall Attribute Multiplier: " << overallMultiplier << "\n\n";
    
    // Reaction time calculation
    std::cout << "2. Start Reaction Time:\n";
    float baseReactionTime = RaceSimulator::CalculateReactionTime(&runner);
    std::cout << "   Base Reaction Time: " << baseReactionTime << " seconds\n";
    
    float falseStartProb = RaceSimulator::CalculateFalseStartProbability(&runner);
    std::cout << "   False Start Probability: " << (falseStartProb * 100) << "%\n\n";
    
    // Skill system
    std::cout << "3. Skill System:\n";
    float skillTriggerProb = RaceSimulator::CalculateSkillTriggerProbability(runner.intelligence);
    std::cout << "   Skill Trigger Probability: " << (skillTriggerProb * 100) << "%\n";
    
    if (runner.hasStartWisdom) {
        auto wisdom = RaceSimulator::CalculateStartWisdom(&runner, true);
        std::cout << "   Start Wisdom Effect: Reaction time reduction " << (wisdom.reactionTimeReduction * 100) << "%\n";
    }
    
    if (runner.hasMidRaceAccel) {
        auto accel = RaceSimulator::CalculateMidRaceAccel(&runner, true);
        std::cout << "   Mid Race Accel Effect: Speed boost " << accel.speedBoost << " m/s (distance " 
                  << accel.triggerStart << "-" << accel.triggerEnd << "m)\n";
    }
    
    if (runner.hasLateRacePower) {
        auto power = RaceSimulator::CalculateLateRacePower(&runner, true);
        std::cout << "   Late Race Power Effect: Power boost " << power.powerBoost 
                  << " (trigger distance " << power.triggerDistance << "m)\n";
    }
    std::cout << "\n";
}

void printRaceResult(const RaceResult& result) {
    std::cout << "========== Race Result ==========\n";
    std::cout << std::fixed << std::setprecision(3);
    std::cout << "Total Time: " << result.totalTime << " seconds\n";
    std::cout << "Reaction Time: " << result.reactionTime << " seconds\n";
    std::cout << "False Start: " << (result.falseStart ? "Yes" : "No") << "\n\n";
    
    std::cout << "Segment Times:\n";
    std::cout << "  0-30m:  " << result.segment1Time << " seconds\n";
    std::cout << "  30-60m: " << result.segment2Time << " seconds\n";
    std::cout << "  60-95m: " << result.segment3Time << " seconds\n";
    std::cout << "  95-100m:" << result.segment4Time << " seconds\n\n";
    
    std::cout << "Skill Activation Status:\n";
    std::cout << "  Mid Race Accel: " << (result.skillActivated[0] ? "Activated" : "Not Activated") << "\n";
    std::cout << "  Start Wisdom: " << (result.skillActivated[1] ? "Activated" : "Not Activated") << "\n";
    std::cout << "  Late Race Power: " << (result.skillActivated[2] ? "Activated" : "Not Activated") << "\n\n";
    
    // Calculate average speed
    float avgSpeed = 100.0f / result.totalTime;
    std::cout << "Average Speed: " << avgSpeed << " m/s (" << (avgSpeed * 3.6f) << " km/h)\n\n";
}

RunnerAttributes inputRunnerAttributes() {
    RunnerAttributes runner = {};
    int temp;
    
    std::cout << "Enter runner attributes (0-255):\n";
    
    std::cout << "Mood (0-255): ";
    std::cin >> temp;
    runner.mood = static_cast<uint8_t>(std::max(0, std::min(255, temp)));
    
    std::cout << "Stamina (0-255): ";
    std::cin >> temp;
    runner.stamina = static_cast<uint8_t>(std::max(0, std::min(255, temp)));
    
    std::cout << "Speed (0-255): ";
    std::cin >> temp;
    runner.speed = static_cast<uint8_t>(std::max(0, std::min(255, temp)));
    
    std::cout << "Power (0-255): ";
    std::cin >> temp;
    runner.power = static_cast<uint8_t>(std::max(0, std::min(255, temp)));
    
    std::cout << "Endurance (0-255): ";
    std::cin >> temp;
    runner.endurance = static_cast<uint8_t>(std::max(0, std::min(255, temp)));
    
    std::cout << "Intelligence (0-255): ";
    std::cin >> temp;
    runner.intelligence = static_cast<uint8_t>(std::max(0, std::min(255, temp)));
    
    std::cout << "Body (0-255): ";
    std::cin >> temp;
    runner.body = static_cast<uint8_t>(std::max(0, std::min(255, temp)));
    
    std::cout << "Adaptability (0=Excellent, 1=Good, 2=Poor, 3=Bad): ";
    std::cin >> temp;
    runner.adaptability = static_cast<Adaptability>(std::max(0, std::min(3, temp)));
    
    std::cout << "Has Mid Race Accel skill? (1=Yes, 0=No): ";
    std::cin >> temp;
    runner.hasMidRaceAccel = (temp == 1);
    
    std::cout << "Has Start Wisdom skill? (1=Yes, 0=No): ";
    std::cin >> temp;
    runner.hasStartWisdom = (temp == 1);
    
    std::cout << "Has Late Race Power skill? (1=Yes, 0=No): ";
    std::cin >> temp;
    runner.hasLateRacePower = (temp == 1);
    
    return runner;
}

Environment inputEnvironment() {
    Environment env = {};
    int temp;
    
    std::cout << "\nEnter environment conditions:\n";
    
    std::cout << "Track Type (0=Plastic, 1=Grass, 2=Cinder, 3=Dirt): ";
    std::cin >> temp;
    env.trackType = static_cast<TrackType>(std::max(0, std::min(3, temp)));
    
    std::cout << "Weather (0=Sunny, 1=Very Sunny, 2=Cloudy, 3=Light Rain, 4=Small Rain, 5=Medium Rain, 6=Heavy Rain): ";
    std::cin >> temp;
    env.weather = static_cast<Weather>(std::max(0, std::min(6, temp)));
    
    std::cout << "Wind Direction (0=Headwind, 1=Tailwind): ";
    std::cin >> temp;
    env.windDir = static_cast<WindDirection>(std::max(0, std::min(1, temp)));
    
    std::cout << "Wind Speed (m/s): ";
    std::cin >> env.windSpeed;
    
    return env;
}

void showPresetRunners() {
    std::cout << "\nPreset Runners:\n";
    std::cout << "1. All-rounder (All attributes 200, All skills)\n";
    std::cout << "2. Speed Type (Speed 255, Intelligence 200, Others 150, Mid Accel + Start Wisdom)\n";
    std::cout << "3. Endurance Type (Endurance 255, Stamina 200, Others 150, Late Power)\n";
    std::cout << "4. Balanced Type (All attributes 180, No skills)\n";
    std::cout << "5. Custom Input\n";
}

RunnerAttributes getPresetRunner(int choice) {
    RunnerAttributes runner = {};

    switch (choice) {
        case 1: // All-rounder
            runner.mood = 200;
            runner.stamina = 200;
            runner.speed = 200;
            runner.power = 200;
            runner.endurance = 200;
            runner.intelligence = 200;
            runner.body = 200;
            runner.adaptability = Adaptability::EXCELLENT;
            runner.hasMidRaceAccel = true;
            runner.hasStartWisdom = true;
            runner.hasLateRacePower = true;
            break;

        case 2: // Speed Type
            runner.mood = 150;
            runner.stamina = 150;
            runner.speed = 255;
            runner.power = 150;
            runner.endurance = 150;
            runner.intelligence = 200;
            runner.body = 150;
            runner.adaptability = Adaptability::GOOD;
            runner.hasMidRaceAccel = true;
            runner.hasStartWisdom = true;
            runner.hasLateRacePower = false;
            break;

        case 3: // Endurance Type
            runner.mood = 150;
            runner.stamina = 200;
            runner.speed = 150;
            runner.power = 180;
            runner.endurance = 255;
            runner.intelligence = 150;
            runner.body = 180;
            runner.adaptability = Adaptability::GOOD;
            runner.hasMidRaceAccel = false;
            runner.hasStartWisdom = false;
            runner.hasLateRacePower = true;
            break;

        case 4: // Balanced Type
            runner.mood = 180;
            runner.stamina = 180;
            runner.speed = 180;
            runner.power = 180;
            runner.endurance = 180;
            runner.intelligence = 180;
            runner.body = 180;
            runner.adaptability = Adaptability::GOOD;
            runner.hasMidRaceAccel = false;
            runner.hasStartWisdom = false;
            runner.hasLateRacePower = false;
            break;

        default:
            return inputRunnerAttributes();
    }

    return runner;
}

Environment getPresetEnvironment(int choice) {
    Environment env = {};

    switch (choice) {
        case 1: // Ideal conditions
            env.trackType = TrackType::PLASTIC;
            env.weather = Weather::SUNNY;
            env.windDir = WindDirection::TAILWIND;
            env.windSpeed = 2.0f;
            break;

        case 2: // Poor conditions
            env.trackType = TrackType::DIRT;
            env.weather = Weather::HEAVY_RAIN;
            env.windDir = WindDirection::HEADWIND;
            env.windSpeed = 5.0f;
            break;

        case 3: // Normal conditions
            env.trackType = TrackType::PLASTIC;
            env.weather = Weather::CLOUDY;
            env.windDir = WindDirection::HEADWIND;
            env.windSpeed = 1.0f;
            break;

        default:
            return inputEnvironment();
    }

    return env;
}

void runSingleSimulation() {
    std::cout << "\n========== Single Simulation ==========\n";

    // Choose runner
    showPresetRunners();
    std::cout << "Choose runner: ";
    int runnerChoice;
    std::cin >> runnerChoice;

    RunnerAttributes runner = getPresetRunner(runnerChoice);

    // Choose environment
    std::cout << "\nEnvironment Presets:\n";
    std::cout << "1. Ideal conditions (Plastic track, Sunny, Tailwind)\n";
    std::cout << "2. Poor conditions (Dirt track, Heavy rain, Headwind)\n";
    std::cout << "3. Normal conditions (Plastic track, Cloudy, Headwind)\n";
    std::cout << "4. Custom Input\n";
    std::cout << "Choose environment: ";
    int envChoice;
    std::cin >> envChoice;

    Environment env = getPresetEnvironment(envChoice);

    // Display input information
    std::cout << "\n";
    printRunnerAttributes(runner);
    printEnvironment(env);

    // Ask if show detailed calculations
    std::cout << "Show detailed calculations? (1=Yes, 0=No): ";
    int showDetails;
    std::cin >> showDetails;

    if (showDetails) {
        printDetailedCalculations(runner, env);
    }

    // Run simulation
    std::cout << "Running simulation...\n\n";
    RaceResult result = CalculateRaceResult(&runner, &env);

    // Display result
    printRaceResult(result);
}

int main() {
    // Initialize random number generator
    InitializeRaceSimulator();

    printHeader();

    int choice;
    do {
        std::cout << "Choose operation:\n";
        std::cout << "1. Run single simulation\n";
        std::cout << "2. Exit\n";
        std::cout << "Please choose: ";
        std::cin >> choice;

        switch (choice) {
            case 1:
                runSingleSimulation();
                break;
            case 2:
                std::cout << "Thank you for using!\n";
                break;
            default:
                std::cout << "Invalid choice, please try again.\n";
                break;
        }

        if (choice != 2) {
            std::cout << "\nPress Enter to continue...";
            std::cin.ignore();
            std::cin.get();
            std::cout << "\n";
        }

    } while (choice != 2);

    return 0;
}
