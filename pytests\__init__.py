"""
Test suite for running race simulation visualization system.

This package contains comprehensive functional tests covering:
- DLL integration and ctypes interface
- Data loading and race calculation
- Animation engine functionality
- UI controls and user interaction
- Build and packaging verification
- Error handling and graceful degradation
"""

import os
import sys

# Add src directory to Python path for testing
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
src_path = os.path.join(project_root, 'src')
if src_path not in sys.path:
    sys.path.insert(0, src_path)