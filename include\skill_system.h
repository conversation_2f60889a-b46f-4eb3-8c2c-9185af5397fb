#pragma once

#include "race_simulator.h"

namespace RaceSimulator {
    
    
    struct MidRaceAccelEffect {
        float speedBoost;       
        float triggerStart;     
        float triggerEnd;       
        bool triggered;
    };

    
    struct StartWisdomEffect {
        float reactionTimeReduction; 
        bool triggered;
    };

    
    struct LateRacePowerEffect {
        float powerBoost;       
        float triggerDistance;  
        bool triggered;
    };

    RACE_SIMULATOR_API void CalculateSkillTriggers(const RunnerAttributes* runner, bool skillTriggers[3]);
    RACE_SIMULATOR_API float CalculateSkillImpact(const RunnerAttributes* runner, const bool triggers[3], float baseTimes[5], const float segmentDistances[5]);
    RACE_SIMULATOR_API StartWisdomEffect CalculateStartWisdom(const RunnerAttributes* runner, bool triggered);
    RACE_SIMULATOR_API float ApplyStartWisdom(float reactionTime, const StartWisdomEffect& effect);
    RACE_SIMULATOR_API MidRaceAccelEffect CalculateMidRaceAccel(const RunnerAttributes* runner, bool triggered);
    RACE_SIMULATOR_API float ApplyMidRaceAccel(float segmentTime, float startDist, float endDist, const MidRaceAccelEffect& effect);
    RACE_SIMULATOR_API LateRacePowerEffect CalculateLateRacePower(const RunnerAttributes* runner, bool triggered);
    RACE_SIMULATOR_API float ApplyLateRacePower(float segmentTime, float startDist, float endDist, const LateRacePowerEffect& effect, uint8_t power);
}