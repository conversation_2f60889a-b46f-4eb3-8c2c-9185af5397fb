我想用c++ 写一个 算法dll，主要是用于 模拟游戏的模拟结果功能，我要模拟的是 跑步比赛
通过我把比赛人物数据发给dll, 计算出每个玩家的成绩
人物有以下属性：  心情， 体力， 速度， 力量，耐力，智力，身体，适应性  还有固有技能
比赛有以下环境属性： 赛道类型（塑胶跑道/草地/煤渣跑道/土地）， 天气（晴/大晴/乌/微雨/小雨/中雨/大雨），风速（逆风，顺风速度）

假设心情处于 最好档位，体力也是满，速度 255， 力量 255   耐力 满足 100m最低要求  ,  身体 255，且在 塑胶跑道，好天气， 顺风情况下，起跑反应速度很快，且触发了 大部分有益技能的条件
玩家才有可能跑出9.5s 的最好成绩， 以这个为 最佳成绩，倒推 算法的实现

不同属性的影响我先做一个初步概念
心情：分4个档位（极佳/好/不良/差）: 会影响所有 比赛数据，可以 极佳 +2%  好是无影响, 不良 -3%, 差 -5%。同时，越低，抢跑犯规概率越大
体力： 0~255， 每次比赛消耗一定体力，完赛 可以是60~130左右的体力消耗， 抢跑也会影响所有人的体力，约5~20不等。主要影响赛程，单场比赛中的效果影响可以考虑：  体力>70% 无惩罚，  之后越低 惩罚越大
速度： 0~255， 代表该玩家的最快速度，直接影响模拟算法
力量： 0~255， 代表该玩家的加速度，直接影响模拟算法，且 影响 起跑反应时间，比如 250以上力量， 反应时间范围就是 0.1~0.12s
耐力： 0~255，代表该玩家的耐力值，只对比赛跑道长度有影响，比如大于100耐力就不会在100m比赛中受惩罚， 但是 200m 比赛会受惩罚，  这次模拟 最长只考虑10000m,  也就是 250以上耐力 会不受惩罚，其他 距离 你可以 通过算法来处理
智力： 0~255， 影响玩家的 起跑反应，和本身固有技能， 比如某玩家有 中途加速 技能， 则智力影响这个技能的触发概率， 比如 250以上，所有技能触发概率都是95%，200是80%，150 是60%，100 是35%， 概率不封顶，最高就是95%，  智力越高越不容易抢跑，且  起跑反应越稳定,比如该玩家的起跑反应是0.1~0.12s,  智力越高 会让每次随机值越接近0.1，而 智力越低，则越随机
身体： 0~255，代表该玩家的综合属性，直接影响模拟算法
适应性：代表玩家对跑道的适应力，也分为4档（极佳/好/不良/差）， 极佳是所有跑道都擅长，好是擅长 塑胶跑道/ 煤渣跑道/土地   不良擅长   塑胶跑道/ 煤渣跑道， 差擅长  塑胶跑道，   玩家在不擅长的跑道上时会收到惩罚
固有技能: 玩家会有多个固有技能，暂时可以先考虑几个简单的， 比如  中途加速  ，起跑智慧 ， 后程发力，    中途加速是30m到70m之间，玩家的速度提高 X m/s,   起跑智慧是 起跑反应时间-30%  ，计算方法是（原起跑反应时间-0.1）x0.7+0.1。 后程发力 是60m后，玩家力量+30

最终比赛成绩（时间）我希望是这样算出的:
起跑反应时间 +  前段（0~30m）时间+中段时间（30-60）+后端时间（60-95）+冲刺时间（95-100）
每一段的时间计算 参考 上面的玩家属性，然后使用算法得出，要求是随机的值，不可以 每次相同

时间 精确到 3位小数

环境属性对人物属性或赛段时间都会有影响

牢记 最佳时间，不得超过9.5s , 反应时间不少于 0.1s
