#define RACE_SIMULATOR_EXPORTS
#include "race_simulator.h"
#include "sqlite3.h"
#include "runner_attributes.h"
#include "skill_system.h"
#include "environment.h"
#include "random_generator.h"
#include <string>
#include <limits>
#include <cmath>
#include <cstring>
#include <stdexcept>

namespace {
    char lastError[256] = "";
}

extern "C" {

    const char* GetLastErrorMessage() {
        return lastError;
    }

    RaceResult CalculateRaceResult(const RunnerAttributes* runner, const Environment* env) {
        RaceResult result = {};

        // Parameter validation
        if (!runner || !env) {
            std::strcpy(lastError, "Invalid input parameters");
            result.totalTime = -1.0f;
            return result;
        }

        try {
            // Initialize random number generator
            InitializeRaceSimulator();

            // Calculate false start probability
            float falseStartProb = RaceSimulator::CalculateFalseStartProbability(runner);
            falseStartProb += RaceSimulator::CalculateEnvironmentalFalseStartImpact(env);

            result.falseStart = RaceSimulator::RandomBool(falseStartProb);

            if (result.falseStart) {
                // False start case, return special value
                result.totalTime = 999.999f;  // Indicates disqualification
                result.reactionTime = 0.0f;
                result.segment1Time = 0.0f;
                result.segment2Time = 0.0f;
                result.segment3Time = 0.0f;
                result.segment4Time = 0.0f;

                // Clear skill activation status
                for (int i = 0; i < 3; ++i) {
                    result.skillActivated[i] = false;
                }

                return result;
            }

            // Calculate attribute and environment effects
            float attributeMultiplier = RaceSimulator::CalculateOverallAttributeMultiplier(runner, env);
            float environmentMultiplier = RaceSimulator::CalculateEnvironmentMultiplier(env);
            float totalMultiplier = attributeMultiplier * environmentMultiplier;

            // Calculate reaction time
            float reactionTime = RaceSimulator::CalculateReactionTime(runner);

            // Apply start wisdom skill
            bool skillTriggers[3] = {false, false, false};
            RaceSimulator::CalculateSkillTriggers(runner, skillTriggers);

            // Copy skill trigger status to result
            for (int i = 0; i < 3; ++i) {
                result.skillActivated[i] = skillTriggers[i];
            }

            // Apply environmental effects on skills
            float skillMultiplier = RaceSimulator::CalculateEnvironmentalSkillImpact(env);
            for (int i = 0; i < 3; ++i) {
                if (skillTriggers[i] && RaceSimulator::RandomBool(1.0f - skillMultiplier)) {
                    result.skillActivated[i] = false;  // Environment causes skill failure
                }
            }

            // Update skill trigger status
            for (int i = 0; i < 3; ++i) {
                skillTriggers[i] = result.skillActivated[i];
            }

            // Calculate base segment times
            // Based on optimal 9.5 seconds
            const float OPTIMAL_TIME = 9.5f;
            const float SEGMENT_DISTANCES[5] = {0.0f, 30.0f, 60.0f, 95.0f, 100.0f};

            // Ideal segment time allocation
            float baseTimes[5];
            baseTimes[0] = 0.11f;  // Reaction time
            baseTimes[1] = 3.8f;   // 0-30m
            baseTimes[2] = 2.8f;   // 30-60m
            baseTimes[3] = 2.4f;   // 60-95m
            baseTimes[4] = 0.39f;  // 95-100m

            // Apply attribute, environment and random factors
            float environmentalRandomness = RaceSimulator::CalculateEnvironmentalRandomness(env);

            // Calculate each segment time
            result.reactionTime = baseTimes[0];

            // Apply attribute effects
            for (int i = 1; i < 5; ++i) {
                float segmentLength = SEGMENT_DISTANCES[i] - SEGMENT_DISTANCES[i-1];
                float baseSegmentTime = baseTimes[i];

                // Apply combined coefficient
                float adjustedTime = baseSegmentTime / totalMultiplier;

                // Add random fluctuation
                float randomFactor = 1.0f + RaceSimulator::RandomNormal(0.0f, environmentalRandomness);
                adjustedTime *= randomFactor;

                // Store adjusted time
                baseTimes[i] = adjustedTime;
            }

            // Check if below optimal time, adjust if so
            float currentTotal = baseTimes[0] + baseTimes[1] + baseTimes[2] + baseTimes[3] + baseTimes[4];
            if (currentTotal < OPTIMAL_TIME) {
                float scaleFactor = OPTIMAL_TIME / currentTotal;
                for (int i = 1; i < 5; ++i) {
                    baseTimes[i] *= scaleFactor;
                }
                currentTotal = OPTIMAL_TIME;
            }

            // Apply skill effects
            if (skillTriggers[1]) { // Start wisdom
                RaceSimulator::StartWisdomEffect wisdom = RaceSimulator::CalculateStartWisdom(runner, true);
                float finalReactionTime = RaceSimulator::ApplyStartWisdom(baseTimes[0], wisdom);

                result.reactionTime = finalReactionTime;
                result.segment1Time = baseTimes[1];
                result.segment2Time = baseTimes[2];
                result.segment3Time = baseTimes[3];
                result.segment4Time = baseTimes[4];

                // Apply skill effects to other segments
                if (skillTriggers[0] || skillTriggers[2]) {
                    float skillImpact = RaceSimulator::CalculateSkillImpact(runner, skillTriggers, baseTimes, SEGMENT_DISTANCES);
                }

                if (skillTriggers[0]) { // Mid-race acceleration
                    RaceSimulator::MidRaceAccelEffect accel = RaceSimulator::CalculateMidRaceAccel(runner, true);
                    result.segment2Time = RaceSimulator::ApplyMidRaceAccel(baseTimes[2], 30.0f, 60.0f, accel);
                    result.segment3Time = RaceSimulator::ApplyMidRaceAccel(baseTimes[3], 60.0f, 95.0f, accel);
                }

                if (skillTriggers[2]) { // Late race power
                    RaceSimulator::LateRacePowerEffect power = RaceSimulator::CalculateLateRacePower(runner, true);
                    result.segment4Time = RaceSimulator::ApplyLateRacePower(baseTimes[4], 95.0f, 100.0f, power, runner->power);
                }
            } else {
                result.reactionTime = baseTimes[0];
                result.segment1Time = baseTimes[1];
                result.segment2Time = baseTimes[2];
                result.segment3Time = baseTimes[3];
                result.segment4Time = baseTimes[4];
            }

            // Calculate total time
            result.totalTime = result.reactionTime + result.segment1Time +
                             result.segment2Time + result.segment3Time + result.segment4Time;

            // Ensure time precision to 3 decimal places
            result.totalTime = std::round(result.totalTime * 1000.0f) / 1000.0f;
            result.reactionTime = std::round(result.reactionTime * 1000.0f) / 1000.0f;
            result.segment1Time = std::round(result.segment1Time * 1000.0f) / 1000.0f;
            result.segment2Time = std::round(result.segment2Time * 1000.0f) / 1000.0f;
            result.segment3Time = std::round(result.segment3Time * 1000.0f) / 1000.0f;
            result.segment4Time = std::round(result.segment4Time * 1000.0f) / 1000.0f;

            // Final check
            if (result.totalTime < OPTIMAL_TIME) {
                // If still below 9.5 seconds, adjust to 9.5 seconds
                float scale = OPTIMAL_TIME / result.totalTime;
                result.segment1Time *= scale;
                result.segment2Time *= scale;
                result.segment3Time *= scale;
                result.segment4Time *= scale;
                result.totalTime = OPTIMAL_TIME;
            }

            // Ensure reaction time is not less than 0.1 seconds
            if (result.reactionTime < 0.1f) {
                float diff = 0.1f - result.reactionTime;
                result.reactionTime = 0.1f;
                result.totalTime += diff;
            }

            lastError[0] = '\0';  // Clear error message

        } catch (const std::exception& e) {
            std::strcpy(lastError, e.what());
            result.totalTime = -1.0f;
        } catch (...) {
            std::strcpy(lastError, "Unknown error occurred");
            result.totalTime = -1.0f;
        }

        return result;
    }

    RACE_SIMULATOR_API int RunTournament(const char* dbPath) {
        // Simplified implementation without SQLite for now
        if (!dbPath) {
            std::strcpy(lastError, "Invalid database path");
            return -1;
        }

        // TODO: Implement tournament logic
        lastError[0] = '\0';
        return 0;
    }

}
