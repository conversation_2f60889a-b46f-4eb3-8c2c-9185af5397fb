#define RACE_SIMULATOR_EXPORTS
#include "race_simulator.h"
#include "sqlite3.h"
#include "runner_attributes.h"
#include "skill_system.h"
#include "environment.h"
#include "random_generator.h"
#include <string>
#include <limits>
#include <cmath>

namespace {
    char lastError[256] = "";
}

extern "C" {

    const char* GetLastErrorMessage() {
        return lastError;
    }

    RaceResult CalculateRaceResult(const RunnerAttributes* runner, const Environment* env) {
        RaceResult result = {};
        
        // 参数验证
        if (!runner || !env) {
            std::strcpy(lastError, "Invalid input parameters");
            result.totalTime = -1.0f;
            return result;
        }

        try {
            // 初始化随机数生成器
            InitializeRaceSimulator();

            // 计算抢跑概率
            float falseStartProb = RaceSimulator::CalculateFalseStartProbability(runner);
            falseStartProb += RaceSimulator::CalculateEnvironmentalFalseStartImpact(env);
            
            result.falseStart = RaceSimulator::RandomBool(falseStartProb);
            
            if (result.falseStart) {
                // 抢跑情况，返回特殊值
                result.totalTime = 999.999f;  // 表示犯规
                result.reactionTime = 0.0f;
                result.segment1Time = 0.0f;
                result.segment2Time = 0.0f;
                result.segment3Time = 0.0f;
                result.segment4Time = 0.0f;
                
                for (int i = 0; i < 3; ++i) {
                    result.skillActivated[i] = false;
                }
                
                return result;
            }

            // 计算基础系数
            float attributeMultiplier = RaceSimulator::CalculateOverallAttributeMultiplier(runner, env);
            float environmentMultiplier = RaceSimulator::CalculateEnvironmentMultiplier(env);
            float totalMultiplier = attributeMultiplier * environmentMultiplier;
            
            // 计算起跑反应时间
            float reactionTime = RaceSimulator::CalculateReactionTime(runner);
            
            // 应用起跑智慧技能
            bool skillTriggers[3] = {false, false, false};
            RaceSimulator::CalculateSkillTriggers(runner, skillTriggers);
            
            // 复制技能触发状态到结果
            for (int i = 0; i < 3; ++i) {
                result.skillActivated[i] = skillTriggers[i];
            }
            
            // 应用环境对技能的影响
            float skillMultiplier = RaceSimulator::CalculateEnvironmentalSkillImpact(env);
            for (int i = 0; i < 3; ++i) {
                if (skillTriggers[i] && RaceSimulator::RandomBool(1.0f - skillMultiplier)) {
                    result.skillActivated[i] = false;  // 环境导致技能失效
                }
            }
            
            // 更新技能触发状态
            for (int i = 0; i < 3; ++i) {
                skillTriggers[i] = result.skillActivated[i];
            }

            // 计算各赛段基础时间
            // 基于最佳情况9.5秒倒推
            const float OPTIMAL_TIME = 9.5f;
            const float SEGMENT_DISTANCES[5] = {0.0f, 30.0f, 60.0f, 95.0f, 100.0f};
            
            // 理想情况下各赛段时间分配
            float baseTimes[5];
            baseTimes[0] = 0.11f;  // 反应时间
            baseTimes[1] = 3.8f;   // 0-30m
            baseTimes[2] = 2.8f;   // 30-60m
            baseTimes[3] = 2.4f;   // 60-95m
            baseTimes[4] = 0.39f;  // 95-100m
            
            // 应用属性、环境和随机因素
            float environmentalRandomness = RaceSimulator::CalculateEnvironmentalRandomness(env);
            
            // 计算每个赛段的时间
            result.reactionTime = baseTimes[0];
            
            // 应用属性影响
            for (int i = 1; i < 5; ++i) {
                float segmentLength = SEGMENT_DISTANCES[i] - SEGMENT_DISTANCES[i-1];
                float baseSegmentTime = baseTimes[i];
                
                // 应用综合系数
                float adjustedTime = baseSegmentTime / totalMultiplier;
                
                // 添加随机波动
                float randomFactor = 1.0f + RaceSimulator::RandomNormal(0.0f, environmentalRandomness);
                adjustedTime *= randomFactor;
                
                // 存储调整后的时间
                baseTimes[i] = adjustedTime;
            }
            
            // 应用技能影响
            float totalSkillImpact = RaceSimulator::CalculateSkillImpact(runner, skillTriggers, baseTimes, SEGMENT_DISTANCES);
            
            // 确保总时间不超过9.5秒的最佳情况
            float currentTotal = baseTimes[0] + baseTimes[1] + baseTimes[2] + baseTimes[3] + baseTimes[4] + totalSkillImpact;
            
            if (currentTotal < OPTIMAL_TIME) {
                // 按比例调整以达到9.5秒限制
                float scaleFactor = OPTIMAL_TIME / currentTotal;
                for (int i = 1; i < 5; ++i) {
                    baseTimes[i] *= scaleFactor;
                }
                currentTotal = OPTIMAL_TIME;
            }
            
            // 应用技能影响后的最终时间
            float finalReactionTime = baseTimes[0];
            float finalTimes[5];
            
            // 重新计算技能影响后的时间
            RaceSimulator::StartWisdomEffect wisdom = RaceSimulator::CalculateStartWisdom(runner, skillTriggers[1]);
            finalReactionTime = RaceSimulator::ApplyStartWisdom(baseTimes[0], wisdom);
            
            finalTimes[0] = finalReactionTime;
            finalTimes[1] = baseTimes[1];
            finalTimes[2] = baseTimes[2];
            finalTimes[3] = baseTimes[3];
            finalTimes[4] = baseTimes[4];
            
            // 应用中途加速和后程发力
            if (skillTriggers[0]) {
                RaceSimulator::MidRaceAccelEffect accel = RaceSimulator::CalculateMidRaceAccel(runner, true);
                finalTimes[2] = RaceSimulator::ApplyMidRaceAccel(finalTimes[2], 30.0f, 60.0f, accel);
                finalTimes[3] = RaceSimulator::ApplyMidRaceAccel(finalTimes[3], 60.0f, 95.0f, accel);
            }
            
            if (skillTriggers[2]) {
                RaceSimulator::LateRacePowerEffect power = RaceSimulator::CalculateLateRacePower(runner, true);
                finalTimes[3] = RaceSimulator::ApplyLateRacePower(finalTimes[3], 60.0f, 95.0f, power, runner->power);
                finalTimes[4] = RaceSimulator::ApplyLateRacePower(finalTimes[4], 95.0f, 100.0f, power, runner->power);
            }
            
            // 存储最终结果
            result.reactionTime = finalReactionTime;
            result.segment1Time = finalTimes[1];
            result.segment2Time = finalTimes[2];
            result.segment3Time = finalTimes[3];
            result.segment4Time = finalTimes[4];
            
            result.totalTime = result.reactionTime + result.segment1Time + 
                             result.segment2Time + result.segment3Time + result.segment4Time;
            
            // 确保时间精度为3位小数
            result.totalTime = std::round(result.totalTime * 1000.0f) / 1000.0f;
            result.reactionTime = std::round(result.reactionTime * 1000.0f) / 1000.0f;
            result.segment1Time = std::round(result.segment1Time * 1000.0f) / 1000.0f;
            result.segment2Time = std::round(result.segment2Time * 1000.0f) / 1000.0f;
            result.segment3Time = std::round(result.segment3Time * 1000.0f) / 1000.0f;
            result.segment4Time = std::round(result.segment4Time * 1000.0f) / 1000.0f;
            
            // 最终检查
            if (result.totalTime < OPTIMAL_TIME) {
                // 如果仍低于9.5秒，调整到9.5秒
                float scale = OPTIMAL_TIME / result.totalTime;
                result.segment1Time *= scale;
                result.segment2Time *= scale;
                result.segment3Time *= scale;
                result.segment4Time *= scale;
                result.totalTime = OPTIMAL_TIME;
            }
            
            // 确保反应时间不小于0.1秒
            if (result.reactionTime < 0.1f) {
                float diff = 0.1f - result.reactionTime;
                result.reactionTime = 0.1f;
                result.totalTime += diff;
            }
            
            lastError[0] = '\0';  // 清除错误信息
            
        } catch (const std::exception& e) {
            std::strcpy(lastError, e.what());
            result.totalTime = -1.0f;
        } catch (...) {
            std::strcpy(lastError, "Unknown error occurred");
            result.totalTime = -1.0f;
        }
        
        return result;
    }

RACE_SIMULATOR_API int RunTournament(const char* dbPath) {
    sqlite3* db;
    sqlite3_stmt* stmt;
    int rc = sqlite3_open(dbPath, &db);

    if (rc) {
        std::string err_msg = "Can't open database: ";
        err_msg += sqlite3_errmsg(db);
        std::strcpy(lastError, err_msg.c_str());
        sqlite3_close(db);
        return -1;
    }

    // For now, we assume a single match with id 1
    const char* select_sql = "SELECT p.id, p.mood, p.stamina, p.speed, p.power, p.endurance, p.intelligence, p.body, p.adaptability, p.hasMidRaceAccel, p.hasStartWisdom, p.hasLateRacePower FROM players p JOIN match_participants mp ON p.id = mp.player_id WHERE mp.match_id = 1";

    rc = sqlite3_prepare_v2(db, select_sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        std::strcpy(lastError, sqlite3_errmsg(db));
        sqlite3_close(db);
        return -2;
    }

    // Hardcoded environment for now
    Environment env = {};
    env.trackType = TrackType::PLASTIC;
    env.weather = Weather::SUNNY;
    env.windDir = WindDirection::TAILWIND;
    env.windSpeed = 1.5f;

    while ((rc = sqlite3_step(stmt)) == SQLITE_ROW) {
        RunnerAttributes runner = {};
        int player_id = sqlite3_column_int(stmt, 0);
        runner.mood = sqlite3_column_double(stmt, 1);
        runner.stamina = sqlite3_column_double(stmt, 2);
        runner.speed = sqlite3_column_double(stmt, 3);
        runner.power = sqlite3_column_double(stmt, 4);
        runner.endurance = sqlite3_column_double(stmt, 5);
        runner.intelligence = sqlite3_column_double(stmt, 6);
        runner.body = sqlite3_column_double(stmt, 7);
        runner.adaptability = static_cast<Adaptability>(sqlite3_column_int(stmt, 8));
        runner.hasMidRaceAccel = sqlite3_column_int(stmt, 9);
        runner.hasStartWisdom = sqlite3_column_int(stmt, 10);
        runner.hasLateRacePower = sqlite3_column_int(stmt, 11);

        RaceResult result = CalculateRaceResult(&runner, &env);

        // Insert result back into the database
        sqlite3_stmt* insert_stmt;
        const char* insert_sql = "INSERT INTO results (match_id, player_id, position, time) VALUES (1, ?, ?, ?)";
        rc = sqlite3_prepare_v2(db, insert_sql, -1, &insert_stmt, NULL);
        
        sqlite3_bind_int(insert_stmt, 1, player_id);
        // Position is not calculated yet, so we'll just put a placeholder.
        // A real implementation would run all sims, then sort and update positions.
        sqlite3_bind_int(insert_stmt, 2, 0);
        sqlite3_bind_double(insert_stmt, 3, result.totalTime);

        if (sqlite3_step(insert_stmt) != SQLITE_DONE) {
            // Handle insertion error
        }
        sqlite3_finalize(insert_stmt);
    }

    if (rc != SQLITE_DONE) {
        std::strcpy(lastError, sqlite3_errmsg(db));
    }

    sqlite3_finalize(stmt);

    // TODO: A second pass is needed to update the 'position' column based on the 'time'.
    
    sqlite3_close(db);
    return 0;
}

RACE_SIMULATOR_API void FreeString(const char* str) {
        // Since we are not allocating new strings for the caller,
        // this function is currently empty. It's here for API compatibility.
    }
}