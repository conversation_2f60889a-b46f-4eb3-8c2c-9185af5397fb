#pragma once

#include "race_simulator.h"

namespace RaceSimulator {
    float CalculateTrackMultiplier(TrackType trackType);
    float CalculateWeatherMultiplier(Weather weather);
    float CalculateWindMultiplier(WindDirection windDir, float windSpeed);
    float CalculateEnvironmentMultiplier(const Environment* env);
    float CalculateEnvironmentalFalseStartImpact(const Environment* env);
    float CalculateEnvironmentalSkillImpact(const Environment* env);
    float CalculateEnvironmentalRandomness(const Environment* env);
}