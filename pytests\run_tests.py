#!/usr/bin/env python3
"""
Test runner script for running race simulation visualization system.

This script provides a comprehensive test runner that executes all test suites
for the running simulation visualization system.
"""

import os
import sys
import unittest
import argparse
import subprocess
import tempfile
import json

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

def discover_tests():
    """Discover all test modules in the tests directory."""
    test_dir = os.path.join(project_root, 'tests')
    
    # Find all test files
    test_files = [
        'test_dll_integration.py',
        'test_data_loading.py',
        'test_animation_engine.py',
        'test_ui_controller.py',
        'test_build_system.py',
        'test_error_handling.py',
        'test_integration.py'
    ]
    
    test_modules = []
    for test_file in test_files:
        file_path = os.path.join(test_dir, test_file)
        if os.path.exists(file_path):
            test_modules.append(f"tests.{test_file[:-3]}")
    
    return test_modules

def run_test_suite(test_modules, verbose=False, failfast=False):
    """Run the specified test modules."""
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    for module_name in test_modules:
        try:
            module = __import__(module_name, fromlist=[''])
            tests = loader.loadTestsFromModule(module)
            suite.addTests(tests)
        except ImportError as e:
            print(f"Warning: Could not import {module_name}: {e}")
    
    runner = unittest.TextTestRunner(verbosity=2 if verbose else 1, failfast=failfast)
    return runner.run(suite)

def run_individual_test(test_name, verbose=False):
    """Run a specific test class or method."""
    loader = unittest.TestLoader()
    suite = loader.loadTestsFromName(test_name)
    runner = unittest.TextTestRunner(verbosity=2 if verbose else 1)
    return runner.run(suite)

def check_dependencies():
    """Check system dependencies and report status."""
    print("Checking system dependencies...")
    
    # Python version
    print(f"Python version: {sys.version}")
    
    # Check for pygame
    try:
        import pygame
        print(f"✓ Pygame available: {pygame.version.ver}")
        pygame_available = True
    except ImportError:
        print("✗ Pygame not available - animation tests will be skipped")
        pygame_available = False
    
    # Check for ctypes
    try:
        import ctypes
        print("✓ ctypes available")
    except ImportError:
        print("✗ ctypes not available - DLL tests may fail")
    
    # Check project structure
    required_files = [
        'src/main.py',
        'src/ui_controller.py',
        'src/data_loader.py',
        'src/animation_engine.py',
        'src/race_simulator_types.py',
        'config.json',
        'sample_runners.json'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(os.path.join(project_root, file_path)):
            missing_files.append(file_path)
    
    if missing_files:
        print("✗ Missing files:")
        for file_path in missing_files:
            print(f"  - {file_path}")
    else:
        print("✓ All required files present")
    
    return pygame_available

def create_test_data():
    """Create test data for integration tests."""
    test_dir = os.path.join(project_root, 'tests', 'test_data')
    os.makedirs(test_dir, exist_ok=True)
    
    # Create test runner files
    test_runners = [
        {"id": 1, "max_speed": 12.0, "acceleration": 3.0, "endurance": 0.85},
        {"id": 2, "max_speed": 11.5, "acceleration": 3.2, "endurance": 0.90},
        {"id": 3, "max_speed": 12.5, "acceleration": 2.8, "endurance": 0.88},
    ]
    
    with open(os.path.join(test_dir, "test_runners.json"), 'w') as f:
        json.dump(test_runners, f, indent=2)
    
    # Create test configuration
    test_config = {
        "window": {"width": 800, "height": 600, "fps": 30},
        "race": {"default_distance": 100.0, "dll_path": None},
        "ui": {"show_fps": True, "auto_play": False}
    }
    
    with open(os.path.join(test_dir, "test_config.json"), 'w') as f:
        json.dump(test_config, f, indent=2)
    
    return test_dir

def run_smoke_tests():
    """Run quick smoke tests to verify basic functionality."""
    print("\n" + "="*60)
    print("Running Smoke Tests")
    print("="*60)
    
    # Test basic imports
    try:
        from src.ui_controller import UIController
        from src.data_loader import DataLoader
        from src.race_simulator_types import create_sample_runners
        print("✓ Basic imports successful")
    except ImportError as e:
        print(f"✗ Import failed: {e}")
        return False
    
    # Test runner creation
    try:
        runners = create_sample_runners()
        assert len(runners) > 0
        assert all('id' in r and 'max_speed' in r for r in runners)
        print("✓ Sample runner creation successful")
    except Exception as e:
        print(f"✗ Runner creation failed: {e}")
        return False
    
    # Test basic functionality
    try:
        loader = DataLoader()
        race_data = loader.calculate_race(runners, 100.0)
        assert race_data is not None
        assert 'race_info' in race_data
        assert 'results' in race_data
        print("✓ Basic race calculation successful")
    except Exception as e:
        print(f"✗ Basic calculation failed: {e}")
        return False
    
    # Test UI controller initialization
    try:
        controller = UIController()
        assert controller.config is not None
        print("✓ UI controller initialization successful")
    except Exception as e:
        print(f"✗ UI controller initialization failed: {e}")
        return False
    
    print("✓ All smoke tests passed!")
    return True

def main():
    """Main test runner function."""
    parser = argparse.ArgumentParser(description='Run race simulation tests')
    parser.add_argument('--verbose', '-v', action='store_true', help='Verbose output')
    parser.add_argument('--failfast', action='store_true', help='Stop on first failure')
    parser.add_argument('--smoke', action='store_true', help='Run smoke tests only')
    parser.add_argument('--test', help='Run specific test class or method')
    parser.add_argument('--list', action='store_true', help='List available tests')
    
    args = parser.parse_args()
    
    print("Race Simulation Visualization System - Test Runner")
    print("=" * 60)
    
    # Check dependencies
    pygame_available = check_dependencies()
    print()
    
    # Create test data
    test_data_dir = create_test_data()
    print(f"Test data created in: {test_data_dir}")
    print()
    
    if args.list:
        print("Available test modules:")
        test_modules = discover_tests()
        for module in test_modules:
            print(f"  - {module}")
        return
    
    if args.smoke:
        success = run_smoke_tests()
        return 0 if success else 1
    
    if args.test:
        print(f"Running specific test: {args.test}")
        result = run_individual_test(args.test, args.verbose)
        return 0 if result.wasSuccessful() else 1
    
    # Run full test suite
    print("Running comprehensive test suite...")
    test_modules = discover_tests()
    
    if not test_modules:
        print("No test modules found!")
        return 1
    
    print(f"Found {len(test_modules)} test modules")
    
    result = run_test_suite(test_modules, args.verbose, args.failfast)
    
    print("\n" + "="*60)
    print("Test Summary")
    print("="*60)
    
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\nFailures:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback.splitlines()[-1]}")
    
    if result.errors:
        print("\nErrors:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback.splitlines()[-1]}")
    
    if pygame_available:
        print("\n✓ Animation tests included")
    else:
        print("\n⚠ Animation tests skipped (pygame not available)")
    
    return 0 if result.wasSuccessful() else 1

if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)