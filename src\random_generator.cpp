#include "race_simulator.h"
#include "random_generator.h"
#include <random>
#include <cmath>
#include <ctime>
#include <algorithm>

namespace {
    std::mt19937 rng;
    bool initialized = false;
}

extern "C" void InitializeRaceSimulator() {
    if (!initialized) {
        rng.seed(std::random_device{}());
        initialized = true;
    }
}

extern "C" void SetRandomSeed(uint32_t seed) {
    rng.seed(seed);
    initialized = true;
}

namespace RaceSimulator {
    // 生成指定范围内的均匀分布随机数
    float RandomFloat(float min, float max) {
        if (!initialized) {
            InitializeRaceSimulator();
        }
        std::uniform_real_distribution<float> dist(min, max);
        return dist(rng);
    }

    // 生成指定范围内的整数随机数
    int RandomInt(int min, int max) {
        if (!initialized) {
            InitializeRaceSimulator();
        }
        std::uniform_int_distribution<int> dist(min, max);
        return dist(rng);
    }

    // 正态分布随机数
    float RandomNormal(float mean, float stddev) {
        if (!initialized) {
            InitializeRaceSimulator();
        }
        std::normal_distribution<float> dist(mean, stddev);
        return dist(rng);
    }

    // 伯努利试验（概率p返回true）
    bool RandomBool(float probability) {
        if (!initialized) {
            InitializeRaceSimulator();
        }
        std::bernoulli_distribution dist(probability);
        return dist(rng);
    }

    // 根据属性值生成偏向高值的随机数（属性越高越稳定）
    float AttributeBiasedRandom(float base, float range, uint8_t attribute) {
        // 属性值映射到0-1的权重
        float weight = static_cast<float>(attribute) / 255.0f;
        
        // 高属性值时方差更小，结果更稳定
        float variance = range * (1.0f - weight * 0.8f);
        
        // 使用正态分布，但限制在合理范围内
        float result = RandomNormal(base, variance);
        return std::max(base - range, std::min(base + range, result));
    }
}