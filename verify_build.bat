@echo off
setlocal enabledelayedexpansion

REM Build Verification Script for ASCII Running Simulation
echo ASCII Running Simulation - Build Verification
echo =============================================
echo.

set ALL_PASSED=1

REM 1. Check Environment
echo 1. Checking Environment...

REM Check Visual Studio
where cl >nul 2>&1
if %errorlevel% neq 0 (
    echo    ERROR: Visual Studio compiler not found!
    set ALL_PASSED=0
    goto :failed
)

REM Check CMake
where cmake >nul 2>&1
if %errorlevel% neq 0 (
    echo    ERROR: CMake not found!
    set ALL_PASSED=0
    goto :failed
)

REM Check MSBuild
where msbuild >nul 2>&1
if %errorlevel% neq 0 (
    echo    ERROR: MSBuild not found!
    set ALL_PASSED=0
    goto :failed
)

echo    Environment: PASS

REM 2. Test Build Process
echo.
echo 2. Testing Build Process...

REM Clean previous build
if exist build rmdir /s /q build

REM Run build
build.bat release
if %errorlevel% neq 0 (
    echo    ERROR: Build failed!
    set ALL_PASSED=0
    goto :failed
)

REM Check if files were created
if exist "bin\AsciiRaceSimulator.exe" (
    echo    Build: PASS - Executable created
) else (
    echo    ERROR: Executable not created
    set ALL_PASSED=0
)

if exist "lib\RaceSimulation.dll" (
    echo    Build: PASS - DLL created
) else (
    echo    ERROR: DLL not created
    set ALL_PASSED=0
)

REM 3. Test DLL Integration
echo.
echo 3. Testing DLL Integration...

REM Test DLL loading
if exist "build\x64\Release\bin\Release\test_skill_system.exe" (
    build\x64\Release\bin\Release\test_skill_system.exe >nul 2>&1
    if %errorlevel% neq 0 (
        echo    DLL Integration: FAIL - Tests failed
        set ALL_PASSED=0
    ) else (
        echo    DLL Integration: PASS
    )
) else (
    echo    DLL Integration: WARNING - Test executable not found
)

REM 4. Test Basic Functionality
echo.
echo 4. Testing Basic Functionality...

if exist "bin\AsciiRaceSimulator.exe" (
    echo    Functionality: PASS - Executable available
) else (
    echo    Functionality: FAIL - Executable not available
    set ALL_PASSED=0
)

REM 5. Test Performance
echo.
echo 5. Testing Performance...

if exist "bin\AsciiRaceSimulator.exe" (
    for %%i in ("bin\AsciiRaceSimulator.exe") do set EXE_SIZE=%%~zi
    for %%i in ("lib\RaceSimulation.dll") do set DLL_SIZE=%%~zi
    
    set /a TOTAL_SIZE=%EXE_SIZE%+%DLL_SIZE%
    set /a TOTAL_MB=%TOTAL_SIZE%/1024/1024
    
    echo    Performance: PASS - Total size: !TOTAL_MB! MB
)

REM Summary
echo.
echo ============================================
echo VERIFICATION SUMMARY
echo ============================================

if %ALL_PASSED%==1 (
    echo 🎉 ALL VERIFICATIONS PASSED!
    echo The build system is ready for development.
    echo.
    echo You can now run:
    echo   build.bat release   - Build release version
    echo   build.bat debug     - Build debug version
    echo   build.bat run       - Run the simulation
    echo.
    exit /b 0
) else (
    echo ❌ SOME VERIFICATIONS FAILED!
    echo Please check the issues above.
    exit /b 1
)

:failed
echo.
echo ============================================
echo VERIFICATION FAILED
echo ============================================
echo Please fix the issues above and run verify_build.bat again
exit /b 1