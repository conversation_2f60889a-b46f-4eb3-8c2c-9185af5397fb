#pragma once

#include <windows.h>
#include <string>
#include <vector>
#include <memory>

// Forward declarations for DLL functions
typedef void* (*CreateSimulatorFunc)();
typedef void (*DestroySimulatorFunc)(void*);
typedef bool (*InitializeRaceFunc)(void*, const char**, int);
typedef bool (*StartRaceFunc)(void*);
typedef bool (*GetRaceProgressFunc)(void*, float*, float*, int*);
typedef bool (*GetRaceResultsFunc)(void*, char**, float*, int);
typedef bool (*IsRaceFinishedFunc)(void*);
typedef const char* (*GetLastErrorFunc)(void*);

struct RunnerInfo {
    std::string name;
    float distance;
    int position;
    bool finished;
    float finishTime;
    
    RunnerInfo() : distance(0.0f), position(0), finished(false), finishTime(0.0f) {}
};

class RaceEngine {
public:
    RaceEngine();
    ~RaceEngine();

    bool initialize();
    void shutdown();

    bool loadRaceDLL(const std::string& dllPath);
    bool setupRace(const std::vector<std::string>& runnerNames);
    bool startRace();
    
    bool updateRace();
    bool isRaceFinished() const;
    
    std::vector<RunnerInfo> getRunnerInfo() const;
    std::vector<RunnerInfo> getRaceResults() const;
    
    std::string getLastError() const;

private:
    HMODULE dllHandle;
    void* simulatorInstance;
    
    // DLL function pointers
    CreateSimulatorFunc createSimulator;
    DestroySimulatorFunc destroySimulator;
    InitializeRaceFunc initializeRace;
    StartRaceFunc startRace;
    GetRaceProgressFunc getRaceProgress;
    GetRaceResultsFunc getRaceResults;
    IsRaceFinishedFunc isRaceFinished;
    GetLastErrorFunc getLastError;
    
    std::vector<std::string> runnerNames;
    bool dllLoaded;
    bool raceInitialized;
    
    void unloadDLL();
    bool loadDLLFunctions();
};