# Decision Log

This file records architectural and implementation decisions using a list format.
2025-08-04 01:18:32 - Log of updates made.

*
      
## Decision

*
      
## Rationale 

*

## Implementation Details

*

---
### Decision
[2025-08-05 01:20:20] - **架构变更：使用 SQLite 代替 JSON 进行数据交换**

**Rationale:**
在与用户讨论后，为了避免在 C++ 和 Python 之间引入 JSON 解析的复杂性，并利用数据库的结构化查询能力，决定采用 SQLite 作为新的数据交换媒介。这种方法将复杂的锦标赛数据存储在临时的数据库文件中，C++ 端通过文件路径接收并直接查询所需数据。

**Implications/Details:**
- **C++ API 变更**: `RunTournament` 函数的签名从接受 JSON 字符串变更为接受数据库文件的路径。
  ```cpp
  // 旧签名: RACE_SIMULATOR_API const char* RunTournament(const char* tournamentJson);
  // 新签名:
  RACE_SIMULATOR_API int RunTournament(const char* dbPath);
  ```
- **依赖项**: 项目现在依赖于 `sqlite3` 库。`sqlite3.h` 和 `sqlite3.c` 已被添加到项目中。
- **工作流**: 调用者（如 Python）将负责：
  1.  创建一个临时的 SQLite 数据库文件。
  2.  根据在 `productContext.md` 中定义的数据模型，将锦标赛的所有数据（选手、赛程、环境等）写入该数据库。
  3.  调用 `RunTournament(dbPath)`。
  4.  C++ DLL 执行模拟，并将比赛结果直接写回数据库中的相应表。
  5.  调用者从数据库中读取结果。

---
### Decision
[2025-08-05 01:12:45] - **设计 DLL 集成适配器层**：定义一个新的、基于 JSON 的通信协议，以解决 C++ 核心逻辑和 Python UI 层之间的数据模型不匹配问题。

**Rationale:**
当前 `include/race_simulator.h` (C++) 和 `src/race_simulator_types.py` (Python) 之间存在严重的不匹配。C++ 模型包含详细的选手属性和环境因素，而 Python 模型则使用一套完全不同的、简化的性能指标。直接修改任一端都可能破坏现有功能（如 `basic_test.exe`）。

采用基于 JSON 的适配器层具有以下优势：
1.  **解耦**: 允许 C++ 核心和 Python 前端独立演进。
2.  **灵活性**: JSON 格式可以轻松地表示复杂的嵌套数据结构（如团队、锦标赛），而无需在 `ctypes` 中创建复杂的、易出错的映射。
3.  **可读性**: JSON 易于人类阅读和调试。
4.  **向后兼容**: 保留现有的 C++ 函数，新的 Python 接口将调用新的、专门为复杂场景设计的 C++ 函数。

**Implementation Details:**
-   **C++ API**: 在 `race_simulator.h` 中定义一个新的 C-style 函数 `RunTournament`，它接受一个 JSON 字符串作为输入，并返回一个包含结果的 JSON 字符串。
    ```cpp
    RACE_SIMULATOR_API const char* RunTournament(const char* tournamentJson);
    RACE_SIMULATOR_API void FreeString(const char* str); // 用于释放返回的字符串
    ```
-   **Python 接口**: 重写 `src/race_simulator_types.py` 中的 `RaceSimulatorAPI` 类。它将负责将 Python 字典序列化为 JSON 字符串，调用 `RunTournament`，然后将返回的 JSON 结果反序列化为 Python 字典。
-   **数据序列化**: C++ 端将集成一个 JSON 库（如 `nlohmann/json`）来解析输入并序列化输出。
- [2025-08-04T01:59:30Z] - **决策**: 移除源代码中的中文注释。
- [2025-08-04T01:59:30Z] - **原因**: MSVC 编译器在处理非 UTF-8 编码的文件时，即使设置了 `/utf-8` 编译选项，仍然会因中文字符导致编译错误。移除注释是解决此问题的最直接方法。
- [2025-08-04T01:59:30Z] - **结果**: 成功解决了编译问题。

---
### Decision
[2025-08-04 03:09:55] - 在架构文档中记录 `basic_test.exe` 工具及其新功能。

**Rationale:**
`basic_test.exe` 是项目重要的测试和演示工具，其 `runCompetition` 功能为算法的平衡性提供了有效的验证手段。为了让所有项目成员了解其价值和用法，并固化这种测试模式，有必要在 `running_race_simulator/design.md` 和 `memory-bank/systemPatterns.md` 中进行记录。

**Implications/Details:**
- 更新了 `running_race_simulator/design.md` 的“测试策略”部分，增加了对 `basic_test.exe` 三个主要功能（单次模拟、多次模拟、模拟比赛）的描述。
- 更新了 `memory-bank/systemPatterns.md`，在“测试模式”下增加了“命令行测试工具”条目，并阐述了其优点和适用场景。

---
### Decision
[2025-08-04 03:15:12] - 将 `tests/basic_test.cpp` 中的编译错误修复工作委托给 `debug` 模式。

**Rationale:**
编译失败符合预期，原因是 `tests/basic_test.cpp` 中存在代码错误。`devops` 模式的职责是构建和部署，而不是修复代码级错误。`debug` 模式专门用于处理此类任务。

**Implications/Details:**
- 将为 `debug` 模式创建一个新任务，其中包含编译错误日志作为上下文。

---
### Decision (Debug)
[2025-08-04 03:21:22] - [Bug Fix Strategy: Address compilation and linking errors in `tests/basic_test.cpp`]

**Rationale:**
The compilation failed due to several issues:
1.  Missing standard C++ headers (`<vector>`, `<algorithm>`) for used data structures and functions.
2.  Functions from the `race_simulator` library (`RandomInt`, `RandomBool`) were not accessible due to being in a namespace and not being exported correctly from the DLL.
3.  The combination of these issues led to a cascade of compilation and, subsequently, linking errors.

**Details:**
- **File:** `tests/basic_test.cpp`
  - Added `#include <vector>`, `#include <algorithm>`.
  - Added `#include "random_generator.h"`.
  - Added `using namespace RaceSimulator;` to resolve namespaced functions.
- **File:** `include/random_generator.h`
  - Added `RACE_SIMULATOR_API` macro to all function declarations to ensure they are properly exported from the `race_simulator.dll`. This required including `race_simulator.h` where the macro is defined.