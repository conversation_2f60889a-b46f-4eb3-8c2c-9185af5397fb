#pragma once

#include "race_simulator.h"

namespace RaceSimulator {
    RACE_SIMULATOR_API float CalculateMoodMultiplier(Mood mood);
    RACE_SIMULATOR_API Mood GetMoodFromValue(uint8_t moodValue);
    RACE_SIMULATOR_API float CalculateAdaptabilityMultiplier(Adaptability adapt, TrackType trackType);
    RACE_SIMULATOR_API float CalculateStaminaMultiplier(uint8_t stamina);
    RACE_SIMULATOR_API float CalculateEnduranceMultiplier(uint8_t endurance);
    RACE_SIMULATOR_API float CalculateReactionTime(const RunnerAttributes* runner);
    RACE_SIMULATOR_API float CalculateFalseStartProbability(const RunnerAttributes* runner);
    RACE_SIMULATOR_API float CalculateOverallAttributeMultiplier(const RunnerAttributes* runner, const Environment* env);
    RACE_SIMULATOR_API float CalculateSkillTriggerProbability(uint8_t intelligence);
}