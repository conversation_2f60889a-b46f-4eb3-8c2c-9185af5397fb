"""
Build System Tests

Tests for executable packaging and build verification including:
- Build script functionality
- Executable creation
- Resource bundling
- Cross-platform compatibility
- Distribution package integrity
"""

import os
import sys
import unittest
import tempfile
import shutil
import subprocess
import json
from unittest.mock import patch, MagicMock

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)


class TestBuildSystem(unittest.TestCase):
    """Test suite for build system and packaging."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.project_root = os.path.dirname(os.path.abspath(__file__))
        
    def tearDown(self):
        """Clean up test fixtures."""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_build_script_exists(self):
        """Test that build script exists and is executable."""
        build_script = os.path.join(self.project_root, 'build.py')
        self.assertTrue(os.path.exists(build_script))
        self.assertTrue(os.path.isfile(build_script))
    
    def test_build_script_import(self):
        """Test that build script can be imported."""
        try:
            import importlib.util
            spec = importlib.util.spec_from_file_location("build", os.path.join(self.project_root, 'build.py'))
            build_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(build_module)
            self.assertTrue(hasattr(build_module, 'main'))
        except Exception as e:
            self.skipTest(f"Build script import failed: {e}")
    
    def test_source_files_exist(self):
        """Test that all source files exist."""
        required_files = [
            'src/main.py',
            'src/ui_controller.py',
            'src/data_loader.py',
            'src/animation_engine.py',
            'src/race_simulator_types.py',
            'src/data_types.py',
        ]
        
        for file_path in required_files:
            full_path = os.path.join(self.project_root, file_path)
            self.assertTrue(os.path.exists(full_path), f"Missing: {file_path}")
    
    def test_config_file_exists(self):
        """Test that configuration file exists."""
        config_path = os.path.join(self.project_root, 'config.json')
        self.assertTrue(os.path.exists(config_path))
        
        # Verify it's valid JSON
        with open(config_path, 'r') as f:
            config = json.load(f)
        self.assertIsInstance(config, dict)
    
    def test_sample_data_exists(self):
        """Test that sample data files exist."""
        sample_files = [
            'sample_runners.json',
        ]
        
        for file_name in sample_files:
            file_path = os.path.join(self.project_root, file_name)
            if os.path.exists(file_path):
                with open(file_path, 'r') as f:
                    data = json.load(f)
                self.assertIsInstance(data, (dict, list))
    
    def test_entry_point_executable(self):
        """Test main entry point execution."""
        main_path = os.path.join(self.project_root, 'src', 'main.py')
        self.assertTrue(os.path.exists(main_path))
        
        # Test that main.py can be executed with --help
        try:
            result = subprocess.run([
                sys.executable, main_path, '--help'
            ], capture_output=True, text=True, timeout=10)
            
            # Should exit with code 0 or 2 (help exits with 0, invalid args with 2)
            self.assertIn(result.returncode, [0, 2])
            self.assertIn('Running Race Simulation', result.stdout + result.stderr)
        except subprocess.TimeoutExpired:
            self.skipTest("Main script execution timed out")
        except Exception as e:
            self.skipTest(f"Cannot test main execution: {e}")
    
    def test_module_import_structure(self):
        """Test that all modules can be imported."""
        src_path = os.path.join(self.project_root, 'src')
        
        # Add src to path temporarily
        original_path = sys.path[:]
        sys.path.insert(0, src_path)
        
        try:
            import main
            import ui_controller
            import data_loader
            import race_simulator_types
            
            # Verify key classes/functions exist
            self.assertTrue(hasattr(main, 'main'))
            self.assertTrue(hasattr(ui_controller, 'UIController'))
            self.assertTrue(hasattr(data_loader, 'DataLoader'))
            self.assertTrue(hasattr(race_simulator_types, 'create_sample_runners'))
            
        except ImportError as e:
            self.fail(f"Module import failed: {e}")
        finally:
            sys.path[:] = original_path
    
    def test_dependency_availability(self):
        """Test that required dependencies are available."""
        # Test Python version
        self.assertGreaterEqual(sys.version_info, (3, 6))
        
        # Test standard library modules
        required_stdlib = [
            'json', 'argparse', 'tempfile', 'subprocess', 'os', 'sys'
        ]
        
        for module_name in required_stdlib:
            try:
                __import__(module_name)
            except ImportError:
                self.fail(f"Required standard library module missing: {module_name}")
    
    def test_optional_dependencies(self):
        """Test optional dependencies (pygame, ctypes)."""
        # Test ctypes (should always be available)
        try:
            import ctypes
            self.assertTrue(hasattr(ctypes, 'CDLL'))
        except ImportError:
            self.fail("ctypes module not available")
        
        # Test pygame (optional)
        try:
            import pygame
            self.assertTrue(hasattr(pygame, 'init'))
            pygame_available = True
        except ImportError:
            pygame_available = False
            
        # Test should pass whether pygame is available or not
        self.assertIsInstance(pygame_available, bool)
    
    def test_directory_structure(self):
        """Test project directory structure."""
        expected_dirs = [
            'src',
            'tests',
            'examples',
            'build'
        ]
        
        for dir_name in expected_dirs:
            dir_path = os.path.join(self.project_root, dir_name)
            self.assertTrue(os.path.isdir(dir_path), f"Missing directory: {dir_name}")
    
    def test_build_config_compatibility(self):
        """Test build configuration compatibility."""
        # Test CMakeLists.txt exists
        cmake_path = os.path.join(self.project_root, 'CMakeLists.txt')
        self.assertTrue(os.path.exists(cmake_path))
        
        # Test build.py exists and is executable
        build_script = os.path.join(self.project_root, 'build.py')
        self.assertTrue(os.path.exists(build_script))
        
        # Test that build.py can be analyzed for syntax errors
        try:
            with open(build_script, 'r') as f:
                build_code = f.read()
            compile(build_code, build_script, 'exec')
        except SyntaxError as e:
            self.fail(f"Build script has syntax error: {e}")
    
    def test_resource_files_structure(self):
        """Test resource files structure and content."""
        # Test configuration file structure
        config_path = os.path.join(self.project_root, 'config.json')
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        required_sections = ['window', 'race', 'ui']
        for section in required_sections:
            self.assertIn(section, config)
        
        # Test sample data structure
        sample_path = os.path.join(self.project_root, 'sample_runners.json')
        if os.path.exists(sample_path):
            with open(sample_path, 'r') as f:
                sample_data = json.load(f)
            
            # Should contain runners array
            if isinstance(sample_data, dict):
                self.assertIn('runners', sample_data)
                runners = sample_data['runners']
            else:
                runners = sample_data
            
            # Each runner should have required fields
            for runner in runners:
                required_fields = ['id', 'max_speed', 'acceleration', 'endurance']
                for field in required_fields:
                    self.assertIn(field, runner)
    
    def test_cross_platform_path_handling(self):
        """Test cross-platform path handling."""
        # Test that paths use os.path for cross-platform compatibility
        src_path = os.path.join(self.project_root, 'src')
        
        # Test path construction
        main_path = os.path.join(src_path, 'main.py')
        self.assertTrue(os.path.exists(main_path))
        
        # Test relative path handling
        relative_path = os.path.relpath(main_path, self.project_root)
        self.assertTrue(os.path.exists(os.path.join(self.project_root, relative_path)))
    
    def test_executable_permissions(self):
        """Test executable permissions on relevant files."""
        # Test main.py is readable
        main_path = os.path.join(self.project_root, 'src', 'main.py')
        self.assertTrue(os.access(main_path, os.R_OK))
        
        # Test build.py is readable
        build_path = os.path.join(self.project_root, 'build.py')
        self.assertTrue(os.access(build_path, os.R_OK))


class TestPackagingIntegrity(unittest.TestCase):
    """Test suite for packaging integrity verification."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.project_root = os.path.dirname(os.path.abspath(__file__))
    
    def test_package_metadata(self):
        """Test package metadata and versioning."""
        # Test README exists and contains relevant info
        readme_path = os.path.join(self.project_root, 'README.md')
        self.assertTrue(os.path.exists(readme_path))
        
        with open(readme_path, 'r', encoding='utf-8') as f:
            readme_content = f.read()
        
        # Should contain project name and basic info
        self.assertIn('race', readme_content.lower())
        self.assertIn('simulation', readme_content.lower())
    
    def test_gitignore_exists(self):
        """Test .gitignore file exists and contains relevant patterns."""
        gitignore_path = os.path.join(self.project_root, '.gitignore')
        self.assertTrue(os.path.exists(gitignore_path))
        
        with open(gitignore_path, 'r') as f:
            gitignore_content = f.read()
        
        # Should contain Python and build-related patterns
        expected_patterns = ['*.pyc', '__pycache__', '*.exe', 'build/']
        for pattern in expected_patterns:
            self.assertIn(pattern, gitignore_content)
    
    def test_build_artifacts_cleanup(self):
        """Test that build artifacts can be cleaned up."""
        # Test that we can create and clean temporary build artifacts
        temp_build_dir = os.path.join(self.project_root, 'temp_build_test')
        
        try:
            os.makedirs(temp_build_dir, exist_ok=True)
            
            # Create some mock build artifacts
            artifact_files = [
                'race_simulator.exe',
                'main.pyc',
                '__pycache__/',
                'build/temp/'
            ]
            
            for artifact in artifact_files:
                path = os.path.join(temp_build_dir, artifact)
                if artifact.endswith('/'):
                    os.makedirs(path, exist_ok=True)
                else:
                    os.makedirs(os.path.dirname(path), exist_ok=True)
                    with open(path, 'w') as f:
                        f.write('test')
            
            # Test cleanup
            shutil.rmtree(temp_build_dir)
            self.assertFalse(os.path.exists(temp_build_dir))
            
        except Exception as e:
            # Clean up on any error
            if os.path.exists(temp_build_dir):
                shutil.rmtree(temp_build_dir, ignore_errors=True)
            self.skipTest(f"Build cleanup test failed: {e}")


class TestIntegrationVerification(unittest.TestCase):
    """Test suite for integration verification."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.project_root = os.path.dirname(os.path.abspath(__file__))
    
    def test_end_to_end_import_chain(self):
        """Test complete import chain from main to all components."""
        src_path = os.path.join(self.project_root, 'src')
        original_path = sys.path[:]
        
        try:
            sys.path.insert(0, src_path)
            
            # Test complete import chain
            import main
            from ui_controller import UIController
            from data_loader import DataLoader
            from race_simulator_types import RaceSimulatorAPI, create_sample_runners
            
            # Test basic instantiation
            controller = UIController()
            self.assertIsNotNone(controller)
            
            # Test runner creation
            runners = create_sample_runners()
            self.assertIsInstance(runners, list)
            self.assertGreater(len(runners), 0)
            
        except Exception as e:
            self.fail(f"Import chain test failed: {e}")
        finally:
            sys.path[:] = original_path
    
    def test_configuration_loading_chain(self):
        """Test configuration loading through the complete chain."""
        src_path = os.path.join(self.project_root, 'src')
        original_path = sys.path[:]
        
        try:
            sys.path.insert(0, src_path)
            
            from ui_controller import UIController
            
            controller = UIController()
            config = controller.config
            
            # Verify configuration is loaded and accessible
            self.assertIsInstance(config, dict)
            self.assertIn('window', config)
            self.assertIn('race', config)
            self.assertIn('ui', config)
            
        except Exception as e:
            self.fail(f"Configuration loading test failed: {e}")
        finally:
            sys.path[:] = original_path


if __name__ == '__main__':
    unittest.main()