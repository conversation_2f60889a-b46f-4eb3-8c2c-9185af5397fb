#pragma once

#include <windows.h>
#include <string>

class ConsoleUtils {
public:
    ConsoleUtils();
    ~ConsoleUtils();

    void setupUtf8Console();
    void setConsoleSize(int width, int height);
    void setConsoleTitle(const std::string& title);
    void setCursorPosition(int x, int y);
    void setCursorVisibility(bool visible);
    void clearScreen();
    void setTextColor(WORD color);
    void resetTextColor();

    void setConsoleBufferSize(int width, int height);
    
    int getConsoleWidth() const;
    int getConsoleHeight() const;
    
    void flushInputBuffer();
    bool keyPressed() const;
    int getKey() const;

    void sleep(int milliseconds);

    // Display refresh rate functions
    int getDisplayRefreshRate() const;
    void enableVSync();

    // ANSI escape sequence functions (cross-platform, flicker-free)
    void clearScreenANSI();
    void setCursorPositionANSI(int x, int y);
    void hideCursorANSI();
    void showCursorANSI();
    void clearLineANSI();
    void clearFromCursorANSI();
    void clearToEndOfLineANSI();
    void saveCursorPositionANSI();
    void restoreCursorPositionANSI();

    // Color constants
    static const WORD COLOR_BLACK = 0;
    static const WORD COLOR_BLUE = FOREGROUND_BLUE;
    static const WORD COLOR_GREEN = FOREGROUND_GREEN;
    static const WORD COLOR_RED = FOREGROUND_RED;
    static const WORD COLOR_CYAN = FOREGROUND_BLUE | FOREGROUND_GREEN;
    static const WORD COLOR_MAGENTA = FOREGROUND_BLUE | FOREGROUND_RED;
    static const WORD COLOR_YELLOW = FOREGROUND_GREEN | FOREGROUND_RED;
    static const WORD COLOR_WHITE = FOREGROUND_BLUE | FOREGROUND_GREEN | FOREGROUND_RED;
    static const WORD COLOR_BRIGHT = FOREGROUND_INTENSITY;

private:
    HANDLE consoleHandle;
    HANDLE inputHandle;
    int consoleWidth;
    int consoleHeight;
    WORD defaultAttributes;
    
    bool getConsoleSize(int& width, int& height);
    void getDefaultAttributes();
    void enableVirtualTerminalProcessing();
    void disableQuickEditMode();
};