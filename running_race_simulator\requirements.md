# 跑步比赛模拟游戏C++ DLL需求规格

## 简介

本需求规格定义了一个C++动态链接库(DLL)，用于模拟100米跑步比赛的计算引擎。该DLL接收比赛人物属性数据，根据特定规则计算每个玩家的最终成绩。所有计算规则严格基于smic_algo.txt文档定义，不考虑现实物理规律。

## 功能需求

### 1. 核心计算引擎

#### 1.1 成绩计算
- **需求1.1.1**：作为系统开发者，我需要DLL能够计算100米跑步比赛的最终成绩，以便为游戏提供准确的比赛结果。
  - 1.1.1.1 成绩计算必须包含五个阶段：起跑反应时间 + 前段(0-30m) + 中段(30-60m) + 后端(60-95m) + 冲刺(95-100m)
  - 1.1.1.2 最终成绩时间必须精确到3位小数
  - 1.1.1.3 最佳成绩为9.5秒（在最佳条件下）
  - 1.1.1.4 起跑反应时间必须≥0.1秒

#### 1.2 C++ DLL接口
- **需求1.2.1**：作为系统集成者，我需要DLL提供标准C接口，以便能够被不同语言的游戏客户端调用。
  - 1.2.1.1 DLL必须导出计算函数，接收选手属性结构体
  - 1.2.1.2 函数必须返回计算后的成绩（浮点数，3位小数）
  - 1.2.1.3 必须提供清晰的API文档和头文件

### 2. 人物属性系统

#### 2.1 基础属性范围
- **需求2.1.1**：作为游戏设计师，我需要系统支持8个人物属性，每个属性范围为0-255整数。
  - 2.1.1.1 心情：影响成绩百分比的4档位系统
  - 2.1.1.2 体力：影响赛程惩罚，>70%无惩罚
  - 2.1.1.3 速度：最快速度，直接影响算法
  - 2.1.1.4 力量：加速度，影响起跑反应时间
  - 2.1.1.5 耐力：影响跑道长度惩罚，250+不受10000m惩罚
  - 2.1.1.6 智力：影响起跑反应稳定性和技能触发概率
  - 2.1.1.7 身体：综合属性，直接影响算法
  - 2.1.1.8 适应性：4档位跑道适应性（极佳/好/不良/差）

#### 2.2 属性效果计算
- **需求2.2.1**：作为算法工程师，我需要系统根据属性值准确计算各项效果。
  - 2.2.1.1 心情效果：极佳+2%，好无影响，不良-3%，差-5%
  - 2.2.1.2 力量与反应时间：250+力量=0.1-0.12s反应时间
  - 2.2.1.3 智力稳定性：250+智力=95%技能触发概率

### 3. 固有技能系统

#### 3.1 技能定义
- **需求3.1.1**：作为游戏平衡师，我需要系统支持三种固有技能，每种技能有明确的触发条件和效果。
  - 3.1.1.1 中途加速：30-70m区间速度提高X m/s
  - 3.1.1.2 起跑智慧：起跑反应时间减少30%，公式：(原时间-0.1)×0.7+0.1
  - 3.1.1.3 后程发力：60m后力量属性+30

#### 3.2 技能触发逻辑
- **需求3.2.1**：作为算法实现者，我需要技能触发基于智力属性概率计算。
  - 3.2.1.1 智力250+时，技能触发概率为95%
  - 3.2.1.2 技能效果必须正确应用到对应的赛程阶段

### 4. 环境影响系统

#### 4.1 环境属性
- **需求4.1.1**：作为游戏设计师，我需要系统支持多种环境因素对比赛结果的影响。
  - 4.1.1.1 赛道类型：塑胶跑道、草地、煤渣跑道、土地
  - 4.1.1.2 天气类型：晴、大晴、乌、微雨、小雨、中雨、大雨
  - 4.1.1.3 风速：逆风/顺风影响速度

#### 4.2 环境效果计算
- **需求4.2.1**：作为算法工程师，我需要环境因素按照文档规则影响最终成绩。
  - 4.2.1.1 跑道适应性根据选手适应性属性和赛道类型计算
  - 4.2.1.2 天气效果根据天气类型影响成绩
  - 4.2.1.3 风速效果根据风向和风力影响速度

### 5. 数据验证和边界处理

#### 5.1 输入验证
- **需求5.1.1**：作为系统开发者，我需要DLL验证所有输入数据的有效性。
  - 5.1.1.1 所有属性值必须在0-255范围内
  - 5.1.1.2 无效输入必须返回错误码或默认值
  - 5.1.1.3 必须处理极端边界情况

#### 5.2 计算精度
- **需求5.2.1**：作为算法工程师，我需要确保计算精度符合要求。
  - 5.2.1.1 所有中间计算必须保持足够精度
  - 5.2.1.2 最终结果四舍五入到3位小数
  - 5.2.1.3 避免浮点数精度误差累积

## 非功能需求

### 6. 性能要求
- **需求6.1**：DLL计算必须在1毫秒内完成单次成绩计算
- **需求6.2**：内存使用不得超过1MB
- **需求6.3**：必须支持并发调用（线程安全）

### 7. 兼容性要求
- **需求7.1**：必须兼容Windows平台标准DLL格式
- **需求7.2**：提供C风格接口，支持C/C++调用
- **需求7.3**：必须提供清晰的API头文件和文档

## 成功标准

1. 计算结果与文档规则完全一致
2. 在最佳条件下可达到9.5秒最佳成绩
3. 所有边界情况正确处理
4. 性能满足实时游戏需求
5. API接口清晰易用