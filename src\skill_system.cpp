#include "race_simulator.h"
#include "skill_system.h"
#include "runner_attributes.h"
#include "random_generator.h"
#include <algorithm>

namespace RaceSimulator {

    // Forward declaration
    float CalculateSkillTriggerProbability(uint8_t intelligence);
    MidRaceAccelEffect CalculateMidRaceAccel(const RunnerAttributes* runner, bool triggered) {
        MidRaceAccelEffect effect;
        effect.triggerStart = 30.0f;
        effect.triggerEnd = 70.0f;
        effect.triggered = triggered;
        
        if (triggered) {
            // 根据智力和速度计算加速效果
            float intelligenceFactor = static_cast<float>(runner->intelligence) / 255.0f;
            float speedFactor = static_cast<float>(runner->speed) / 255.0f;
            
            effect.speedBoost = 0.5f + 1.0f * intelligenceFactor * speedFactor;
        } else {
            effect.speedBoost = 0.0f;
        }
        
        return effect;
    }

    // 计算起跑智慧效果
    StartWisdomEffect CalculateStartWisdom(const RunnerAttributes* runner, bool triggered) {
        StartWisdomEffect effect;
        effect.triggered = triggered;
        
        if (triggered) {
            effect.reactionTimeReduction = 0.3f;  // 30%减少
        } else {
            effect.reactionTimeReduction = 0.0f;
        }
        
        return effect;
    }

    // 计算后程发力效果
    LateRacePowerEffect CalculateLateRacePower(const RunnerAttributes* runner, bool triggered) {
        LateRacePowerEffect effect;
        effect.triggerDistance = 60.0f;
        effect.triggered = triggered;
        
        if (triggered) {
            effect.powerBoost = 30.0f;  // 力量+30
        } else {
            effect.powerBoost = 0.0f;
        }
        
        return effect;
    }

    // 应用起跑智慧到反应时间
    float ApplyStartWisdom(float reactionTime, const StartWisdomEffect& wisdom) {
        if (wisdom.triggered) {
            // 公式: (原反应时间 - 0.1) * 0.7 + 0.1
            return (reactionTime - 0.1f) * 0.7f + 0.1f;
        }
        return reactionTime;
    }

    // 应用中途加速到赛段时间
    float ApplyMidRaceAccel(float segmentTime, float distanceStart, float distanceEnd, 
                           const MidRaceAccelEffect& accel) {
        if (!accel.triggered) return segmentTime;
        
        // 计算该赛段与加速区间的重叠部分
        float overlapStart = std::max(distanceStart, accel.triggerStart);
        float overlapEnd = std::min(distanceEnd, accel.triggerEnd);
        
        if (overlapStart >= overlapEnd) return segmentTime;  // 无重叠
        
        // 计算重叠比例
        float segmentLength = distanceEnd - distanceStart;
        float overlapLength = overlapEnd - overlapStart;
        float overlapRatio = overlapLength / segmentLength;
        
        // 计算时间减少量
        float timeReduction = (accel.speedBoost / 10.0f) * overlapRatio;
        
        return std::max(segmentTime - timeReduction, segmentTime * 0.7f);
    }

    // 应用后程发力到赛段时间
    float ApplyLateRacePower(float segmentTime, float distanceStart, float distanceEnd,
                            const LateRacePowerEffect& power, uint8_t originalPower) {
        if (!power.triggered) return segmentTime;
        
        // 计算该赛段是否在后程（60米后）
        if (distanceEnd <= power.triggerDistance) return segmentTime;
        
        // 计算实际生效的距离比例
        float effectiveDistance = std::max(0.0f, distanceEnd - power.triggerDistance);
        float segmentLength = distanceEnd - distanceStart;
        float ratio = std::min(1.0f, effectiveDistance / segmentLength);
        
        // 计算新的力量值影响
        float newPower = static_cast<float>(originalPower) + power.powerBoost;
        float powerIncrease = newPower / static_cast<float>(originalPower);
        
        // 力量增加会减少时间
        float powerFactor = 1.0f / (1.0f + (powerIncrease - 1.0f) * 0.3f);
        
        return segmentTime * (1.0f - ratio * (1.0f - powerFactor));
    }

    // 计算技能触发状态
    void CalculateSkillTriggers(const RunnerAttributes* runner, bool triggers[3]) {
        float triggerProb = CalculateSkillTriggerProbability(runner->intelligence);
        
        // 检查每个技能是否存在并触发
        triggers[0] = runner->hasMidRaceAccel && RandomBool(triggerProb);
        triggers[1] = runner->hasStartWisdom && RandomBool(triggerProb);
        triggers[2] = runner->hasLateRacePower && RandomBool(triggerProb);
    }

    // 计算技能对总时间的影响
    float CalculateSkillImpact(const RunnerAttributes* runner, const bool triggers[3],
                              float baseTimes[5], const float segmentDistances[5]) {
        float totalImpact = 0.0f;
        
        // 计算各技能效果
        StartWisdomEffect wisdom = CalculateStartWisdom(runner, triggers[1]);
        MidRaceAccelEffect accel = CalculateMidRaceAccel(runner, triggers[0]);
        LateRacePowerEffect power = CalculateLateRacePower(runner, triggers[2]);
        
        // 应用起跑智慧
        if (wisdom.triggered) {
            float originalReaction = baseTimes[0];
            float newReaction = ApplyStartWisdom(originalReaction, wisdom);
            totalImpact += (newReaction - originalReaction);
        }
        
        // 应用中途加速
        if (accel.triggered) {
            for (int i = 1; i < 5; ++i) {  // 跳过反应时间
                float newTime = ApplyMidRaceAccel(
                    baseTimes[i], 
                    segmentDistances[i-1], 
                    segmentDistances[i], 
                    accel
                );
                totalImpact += (newTime - baseTimes[i]);
            }
        }
        
        // 应用后程发力
        if (power.triggered) {
            for (int i = 1; i < 5; ++i) {  // 跳过反应时间
                if (segmentDistances[i] > 60.0f) {  // 只影响60米后的赛段
                    float newTime = ApplyLateRacePower(
                        baseTimes[i],
                        segmentDistances[i-1],
                        segmentDistances[i],
                        power,
                        runner->power
                    );
                    totalImpact += (newTime - baseTimes[i]);
                }
            }
        }
        
        return totalImpact;
    }
}

// 计算技能触发的基础概率
float CalculateSkillTriggerProbability(uint8_t intelligence) {
    // 基础概率为30%，智力每增加1点，概率增加0.05%，最高可达60%
    float baseProb = 0.30f;
    float intelligenceBonus = (static_cast<float>(intelligence) / 255.0f) * 0.30f;
    return std::min(baseProb + intelligenceBonus, 0.60f);
}