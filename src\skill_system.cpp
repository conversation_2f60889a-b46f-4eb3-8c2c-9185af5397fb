#include "race_simulator.h"
#include "skill_system.h"
#include "runner_attributes.h"
#include "random_generator.h"
#include <algorithm>
#include <cmath>

namespace RaceSimulator {

    float CalculateSkillTriggerProbability(const RunnerAttributes* runner, int skillIndex) {
        if (!runner) return 0.0f;

        float baseProbability = 0.0f;
        float intelligenceFactor = static_cast<float>(runner->intelligence) / 255.0f;

        switch (skillIndex) {
            case 0: // Mid-race acceleration
                if (runner->hasMidRaceAccel) {
                    baseProbability = 0.3f + intelligenceFactor * 0.4f;
                }
                break;
            case 1: // Start wisdom
                if (runner->hasStartWisdom) {
                    baseProbability = 0.4f + intelligenceFactor * 0.3f;
                }
                break;
            case 2: // Late race power
                if (runner->hasLateRacePower) {
                    baseProbability = 0.35f + intelligenceFactor * 0.35f;
                }
                break;
        }

        return std::min(baseProbability, 0.8f);
    }

    StartWisdomEffect CalculateStartWisdom(const RunnerAttributes* runner, bool activated) {
        StartWisdomEffect effect = {};

        if (!activated || !runner || !runner->hasStartWisdom) {
            return effect;
        }

        float intelligenceFactor = static_cast<float>(runner->intelligence) / 255.0f;

        effect.reactionTimeReduction = 0.01f + intelligenceFactor * 0.02f;
        effect.triggered = true;

        return effect;
    }

    float ApplyStartWisdom(float baseReactionTime, const StartWisdomEffect& effect) {
        if (!effect.triggered) {
            return baseReactionTime;
        }

        float improvedTime = baseReactionTime - effect.reactionTimeReduction;
        return std::max(0.08f, improvedTime);
    }

    MidRaceAccelEffect CalculateMidRaceAccel(const RunnerAttributes* runner, bool activated) {
        MidRaceAccelEffect effect = {};

        if (!activated || !runner || !runner->hasMidRaceAccel) {
            return effect;
        }

        float speedFactor = static_cast<float>(runner->speed) / 255.0f;
        float staminaFactor = static_cast<float>(runner->stamina) / 255.0f;

        effect.speedBoost = 0.02f + speedFactor * 0.05f;
        effect.triggerStart = 30.0f;
        effect.triggerEnd = 95.0f;
        effect.triggered = true;

        return effect;
    }

    float ApplyMidRaceAccel(float baseTime, float startDistance, float endDistance, const MidRaceAccelEffect& effect) {
        if (!effect.triggered) {
            return baseTime;
        }

        float segmentLength = endDistance - startDistance;
        float timeReduction = baseTime * effect.speedBoost * (segmentLength / 100.0f);

        return std::max(baseTime * 0.8f, baseTime - timeReduction);
    }

    LateRacePowerEffect CalculateLateRacePower(const RunnerAttributes* runner, bool activated) {
        LateRacePowerEffect effect = {};

        if (!activated || !runner || !runner->hasLateRacePower) {
            return effect;
        }

        float powerFactor = static_cast<float>(runner->power) / 255.0f;
        float enduranceFactor = static_cast<float>(runner->endurance) / 255.0f;

        effect.powerBoost = 0.03f + powerFactor * 0.07f;
        effect.triggerDistance = 95.0f;
        effect.triggered = true;

        return effect;
    }

    float ApplyLateRacePower(float baseTime, float startDistance, float endDistance, const LateRacePowerEffect& effect, uint8_t originalPower) {
        if (!effect.triggered) {
            return baseTime;
        }

        float powerFactor = static_cast<float>(originalPower) / 255.0f;
        float timeReduction = baseTime * effect.powerBoost * powerFactor;

        return std::max(baseTime * 0.75f, baseTime - timeReduction);
    }

    void CalculateSkillTriggers(const RunnerAttributes* runner, bool triggers[3]) {
        if (!runner) {
            triggers[0] = triggers[1] = triggers[2] = false;
            return;
        }

        float prob0 = CalculateSkillTriggerProbability(runner, 0);
        float prob1 = CalculateSkillTriggerProbability(runner, 1);
        float prob2 = CalculateSkillTriggerProbability(runner, 2);

        triggers[0] = RandomBool(prob0);
        triggers[1] = RandomBool(prob1);
        triggers[2] = RandomBool(prob2);
    }

    float CalculateSkillImpact(const RunnerAttributes* runner, const bool triggers[3], float baseTimes[5], const float segmentDistances[5]) {
        if (!runner) return 0.0f;

        float totalImpact = 0.0f;

        // Apply skill effects to segment times
        if (triggers[0]) { // Mid-race acceleration
            MidRaceAccelEffect accel = CalculateMidRaceAccel(runner, true);
            float oldTime2 = baseTimes[2];
            float oldTime3 = baseTimes[3];
            baseTimes[2] = ApplyMidRaceAccel(baseTimes[2], 30.0f, 60.0f, accel);
            baseTimes[3] = ApplyMidRaceAccel(baseTimes[3], 60.0f, 95.0f, accel);
            totalImpact += (oldTime2 - baseTimes[2]) + (oldTime3 - baseTimes[3]);
        }

        if (triggers[2]) { // Late race power
            LateRacePowerEffect power = CalculateLateRacePower(runner, true);
            float oldTime4 = baseTimes[4];
            baseTimes[4] = ApplyLateRacePower(baseTimes[4], 95.0f, 100.0f, power, runner->power);
            totalImpact += (oldTime4 - baseTimes[4]);
        }

        return totalImpact;
    }

    float CalculateFalseStartProbability(const RunnerAttributes* runner) {
        if (!runner) return 0.0f;

        float moodFactor = 1.0f - (static_cast<float>(runner->mood) / 255.0f);
        float intelligenceFactor = static_cast<float>(runner->intelligence) / 255.0f;

        float baseProbability = 0.02f + moodFactor * 0.03f - intelligenceFactor * 0.01f;

        return std::max(0.0f, std::min(baseProbability, 0.1f));
    }

    float CalculateReactionTime(const RunnerAttributes* runner) {
        if (!runner) return 0.15f;

        float speedFactor = static_cast<float>(runner->speed) / 255.0f;
        float intelligenceFactor = static_cast<float>(runner->intelligence) / 255.0f;

        float baseTime = 0.15f - speedFactor * 0.04f - intelligenceFactor * 0.02f;

        return std::max(0.08f, std::min(baseTime, 0.25f));
    }

}
