# PowerShell Test Runner for ASCII Running Simulation

param(
    [switch]$Quick = $false,
    [switch]$Verbose = $false
)

$ErrorActionPreference = "Stop"

Write-Host "ASCII Running Simulation - Test Suite" -ForegroundColor Cyan
Write-Host "=====================================" -ForegroundColor Cyan
Write-Host ""

$testResults = @()
$allPassed = $true

function Invoke-Test {
    param(
        [string]$TestName,
        [string]$ExecutablePath,
        [string[]]$Arguments = @()
    )
    
    Write-Host "Running $TestName..." -ForegroundColor Yellow
    
    if (-not (Test-Path $ExecutablePath)) {
        Write-Host "  ERROR: $ExecutablePath not found" -ForegroundColor Red
        return $false
    }
    
    try {
        $startTime = Get-Date
        
        if ($Verbose) {
            $output = & $ExecutablePath $Arguments 2>&1
            Write-Host $output -ForegroundColor Gray
        } else {
            & $ExecutablePath $Arguments > $null 2>&1
        }
        
        $endTime = Get-Date
        $duration = ($endTime - $startTime).TotalSeconds
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  ✅ PASS ($([math]::Round($duration, 2))s)" -ForegroundColor Green
            return $true
        } else {
            Write-Host "  ❌ FAIL (exit code: $LASTEXITCODE)" -ForegroundColor Red
            return $false
        }
        
    } catch {
        Write-Host "  ❌ ERROR: $_" -ForegroundColor Red
        return $false
    }
}

# Run unit tests
$unitTests = @(
    @{ Name = "Skill System Tests"; Path = "build\x64\Release\bin\Release\test_skill_system.exe" }
    @{ Name = "Race Engine Tests"; Path = "build\x64\Release\bin\Release\test_race_engine.exe" }
)

foreach ($test in $unitTests) {
    $result = Invoke-Test -TestName $test.Name -ExecutablePath $test.Path
    $testResults += @{ Test = $test.Name; Passed = $result }
    if (-not $result) { $allPassed = $false }
}

# Run integration tests
if (-not $Quick) {
    $integrationTests = @(
        @{ Name = "Integration Tests"; Path = "build\x64\Release\bin\Release\integration_test.exe" }
    )

    foreach ($test in $integrationTests) {
        $result = Invoke-Test -TestName $test.Name -ExecutablePath $test.Path -Arguments @("--quick")
        $testResults += @{ Test = $test.Name; Passed = $result }
        if (-not $result) { $allPassed = $false }
    }
} else {
    Write-Host "Skipping integration tests (quick mode)" -ForegroundColor Gray
}

# Run CTest if available
if (Test-Path "build\x64\Release\CTestTestfile.cmake") {
    Write-Host "Running CTest..." -ForegroundColor Yellow
    try {
        Push-Location "build\x64\Release"
        $ctestOutput = ctest --output-on-failure 2>&1
        $ctestPassed = $LASTEXITCODE -eq 0
        Pop-Location
        
        if ($ctestPassed) {
            Write-Host "  ✅ CTest PASS" -ForegroundColor Green
        } else {
            Write-Host "  ❌ CTest FAIL" -ForegroundColor Red
            if ($Verbose) { Write-Host $ctestOutput -ForegroundColor Gray }
            $allPassed = $false
        }
    } catch {
        Write-Host "  ⚠️  CTest not available" -ForegroundColor Yellow
    }
}

# Summary
Write-Host ""
Write-Host "=====================================" -ForegroundColor Cyan
Write-Host "TEST SUMMARY" -ForegroundColor Cyan
Write-Host "=====================================" -ForegroundColor Cyan

foreach ($result in $testResults) {
    $status = if ($result.Passed) { "PASS" } else { "FAIL" }
    $color = if ($result.Passed) { "Green" } else { "Red" }
    Write-Host "$("{0,-25}" -f $result.Test): $status" -ForegroundColor $color
}

Write-Host ""
if ($allPassed) {
    Write-Host "🎉 ALL TESTS PASSED!" -ForegroundColor Green
} else {
    Write-Host "❌ SOME TESTS FAILED!" -ForegroundColor Red
}

exit $allPassed ? 0 : 1