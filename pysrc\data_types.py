from dataclasses import dataclass
from typing import List, Dict
from enum import Enum

class TrackType(Enum):
    PLASTIC = 0
    GRASS = 1
    CINDER = 2
    DIRT = 3

class Weather(Enum):
    SUNNY = 0
    VERY_SUNNY = 1
    CLOUDY = 2
    LIGHT_RAIN = 3
    SMALL_RAIN = 4
    MEDIUM_RAIN = 5
    HEAVY_RAIN = 6

class WindDirection(Enum):
    HEADWIND = 0
    TAILWIND = 1

class Adaptability(Enum):
    EXCELLENT = 0
    GOOD = 1
    POOR = 2
    BAD = 3

@dataclass
class RunnerAttributes:
    mood: int  # 0-255
    stamina: int  # 0-255
    speed: int  # 0-255
    power: int  # 0-255
    endurance: int  # 0-255
    intelligence: int  # 0-255
    body: int  # 0-255
    adaptability: Adaptability
    has_mid_race_accel: bool
    has_start_wisdom: bool
    has_late_race_power: bool

@dataclass
class Environment:
    track_type: TrackType
    weather: Weather
    wind_dir: WindDirection
    wind_speed: float

@dataclass
class RaceResult:
    total_time: float
    reaction_time: float
    false_start: bool
    segment1_time: float  # 0-30m
    segment2_time: float  # 30-60m
    segment3_time: float  # 60-95m
    segment4_time: float  # 95-100m
    skill_activated: List[bool]  # [mid_accel, start_wisdom, late_power]

@dataclass
class RaceSnapshot:
    runner_id: int
    position: float  # 0-100 meters
    time_elapsed: float
    current_segment: int  # 1-4
    speed: float  # m/s

@dataclass
class SimulationData:
    runners: List[RunnerAttributes]
    environment: Environment
    race_results: List[RaceResult]
    snapshots: List[List[RaceSnapshot]]  # [time_step][runner]