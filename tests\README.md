# Running Race Simulation - Test Suite

This directory contains comprehensive functional tests for the running race simulation visualization system.

## Test Suite Overview

The test suite is organized into six main categories:

### 1. **DLL Integration Tests** (`test_dll_integration.py`)
- DLL loading and initialization
- ctypes interface verification
- Race calculation accuracy through DLL
- Error handling for DLL failures
- Graceful fallback when DLL unavailable

### 2. **Data Loading Tests** (`test_data_loading.py`)
- JSON file loading and parsing
- Runner data validation
- Race calculation accuracy
- Results formatting and export
- Error handling for malformed data

### 3. **Animation Engine Tests** (`test_animation_engine.py`)
- Frame interpolation accuracy
- Runner positioning calculations
- Animation timing and synchronization
- Visual rendering accuracy
- Performance validation

### 4. **UI Controller Tests** (`test_ui_controller.py`)
- Play/pause controls functionality
- User input handling
- Configuration loading
- Command line argument parsing
- Export functionality

### 5. **Build System Tests** (`test_build_system.py`)
- Build script functionality
- Executable creation verification
- Resource bundling
- Cross-platform compatibility
- Distribution package integrity

### 6. **Error Handling Tests** (`test_error_handling.py`)
- DLL unavailable scenarios
- Invalid input data handling
- Network/IO errors
- Resource unavailability
- User-friendly error messages

### 7. **Integration Tests** (`test_integration.py`)
- Complete end-to-end workflows
- Real-world usage scenarios
- DLL integration to visualization
- Configuration to execution
- User interaction to results

## Quick Start

### Run All Tests
```bash
# Navigate to project root
cd running-race-simulation

# Run comprehensive test suite
python tests/run_tests.py

# Run with verbose output
python tests/run_tests.py --verbose
```

### Run Specific Test Categories
```bash
# Run only DLL integration tests
python -m pytest tests/test_dll_integration.py

# Run only UI tests
python -m pytest tests/test_ui_controller.py

# Run only integration tests
python -m pytest tests/test_integration.py
```

### Run Smoke Tests
```bash
# Quick verification of basic functionality
python tests/run_tests.py --smoke
```

### Run Individual Tests
```bash
# Run specific test class
python tests/run_tests.py --test test_dll_integration.TestDLLIntegration

# Run specific test method
python tests/run_tests.py --test test_data_loading.TestDataLoading.test_load_valid_json_file
```

## Test Execution Options

### Test Runner Script (`run_tests.py`)
The main test runner provides several options:

```bash
# List all available tests
python tests/run_tests.py --list

# Run with fail-fast mode
python tests/run_tests.py --failfast

# Run specific test
python tests/run_tests.py --test test_name

# Run smoke tests only
python tests/run_tests.py --smoke
```

### Direct unittest Execution
```bash
# Run all tests with unittest
python -m unittest discover tests -v

# Run specific test file
python -m unittest tests.test_dll_integration -v

# Run specific test class
python -m unittest tests.test_ui_controller.TestUIController -v
```

## Test Data

The test suite creates temporary test data including:
- Sample runner files with various configurations
- Test configuration files
- Malformed JSON files for error testing
- Large datasets for performance testing

Test data is automatically cleaned up after test execution.

## Coverage Reports

To generate test coverage reports:

```bash
# Install coverage tool
pip install coverage

# Run tests with coverage
coverage run -m pytest tests/

# Generate coverage report
coverage report

# Generate HTML coverage report
coverage html
```

## Dependencies

### Required Dependencies
- Python 3.6+
- Standard library modules (json, argparse, tempfile, etc.)
- ctypes (for DLL integration)

### Optional Dependencies
- **Pygame**: For animation engine tests
  - Install with: `pip install pygame`
  - Tests will gracefully skip if pygame is not available

### Test Dependencies
- unittest (built-in)
- pytest (optional, for enhanced test discovery)
- coverage (optional, for coverage reports)

## Test Categories and Focus Areas

### Critical Path Tests
These tests cover the most important user workflows:

1. **Basic Race Simulation**: DLL → Calculation → Results
2. **Data Loading**: File → JSON → Validation → Processing
3. **Animation**: Data → Frames → Display → Controls
4. **Export**: Results → JSON → File System
5. **Error Recovery**: Failure → Fallback → User Feedback

### Edge Case Tests
- Empty runner lists
- Invalid JSON formats
- Missing DLL files
- Network path handling
- Unicode file paths
- Very large datasets
- Zero/negative values

### Performance Tests
- Frame rate consistency
- Memory usage scaling
- Large dataset handling
- Concurrent file access

### Real-World Scenario Tests
- First-time user workflow
- Research data analysis
- Coaching/training scenarios
- Configuration customization
- Export for external analysis

## Writing New Tests

### Test Structure
```python
import unittest
from src.your_module import YourClass

class TestYourFeature(unittest.TestCase):
    def setUp(self):
        """Set up test fixtures."""
        self.test_data = ...
    
    def test_specific_functionality(self):
        """Test specific functionality with clear description."""
        # Arrange
        test_input = ...
        
        # Act
        result = your_function(test_input)
        
        # Assert
        self.assertEqual(result, expected_result)
    
    def tearDown(self):
        """Clean up test fixtures."""
        # Cleanup code here
```

### Test Guidelines
1. **Use descriptive test names**: `test_function_behavior_expected_result`
2. **Follow AAA pattern**: Arrange, Act, Assert
3. **Test both success and failure paths**
4. **Use temporary files for I/O operations**
5. **Mock external dependencies when appropriate**
6. **Test real-world scenarios, not just edge cases**

## Continuous Integration

The test suite is designed for CI/CD integration:

```yaml
# Example GitHub Actions workflow
- name: Run Tests
  run: |
    python tests/run_tests.py --smoke
    python -m unittest discover tests -v
```

## Troubleshooting

### Common Issues

1. **Pygame Import Errors**
   ```bash
   # Install pygame
   pip install pygame
   # Or skip animation tests
   python tests/run_tests.py --test test_dll_integration
   ```

2. **Path Issues**
   ```bash
   # Ensure tests are run from project root
   cd running-race-simulation
   python tests/run_tests.py
   ```

3. **Permission Errors**
   ```bash
   # Ensure test directory is writable
   chmod +w tests/
   ```

### Debug Mode
```bash
# Run with debug output
python -m unittest tests.test_ui_controller.TestUIController.test_workflow_no_dll_fallback -v
```

## Test Results

### Success Criteria
- All critical path tests pass
- Fallback mechanisms work correctly
- Error handling provides user-friendly messages
- Performance meets minimum requirements
- Cross-platform compatibility verified

### Reporting
Test results include:
- Test execution summary
- Failure details with stack traces
- Performance metrics
- Coverage reports (when available)
- Dependency status