"""
DLL interface and ctypes mappings for race simulation.
Defines the data structures and function signatures for interacting with the DLL.
"""

import ctypes
from ctypes import Structure, c_int, c_double, c_char_p, c_bool, POINTER, CDLL
from typing import List, Dict, Any
import os


class RunnerPerformance(Structure):
    """Structure representing a runner's performance characteristics."""
    _fields_ = [
        ("id", c_int),
        ("max_speed", c_double),
        ("acceleration", c_double),
        ("endurance", c_double)
    ]


class RaceResult(Structure):
    """Structure representing a single runner's race result."""
    _fields_ = [
        ("runner_id", c_int),
        ("finish_time", c_double),
        ("max_speed_achieved", c_double),
        ("average_speed", c_double),
        ("final_position", c_int),
        ("did_finish", c_bool)
    ]


class RaceSnapshot(Structure):
    """Structure representing a single frame of race data for animation."""
    _fields_ = [
        ("time", c_double),
        ("runner_id", c_int),
        ("position", c_double),
        ("current_speed", c_double),
        ("current_acceleration", c_double)
    ]


class RaceData(Structure):
    """Complete race data including results and animation frames."""
    _fields_ = [
        ("total_runners", c_int),
        ("race_distance", c_double),
        ("race_results", POINTER(RaceResult)),
        ("snapshots", POINTER(RaceSnapshot)),
        ("total_snapshots", c_int)
    ]


class RaceSimulatorAPI:
    """High-level interface to the race simulation DLL."""
    
    def __init__(self, dll_path: str = None):
        """Initialize the race simulator API with DLL loading."""
        if dll_path is None:
            # Look for DLL in current directory and common locations
            possible_paths = [
                "race_simulator.dll",
                "./race_simulator.dll",
                "../race_simulator.dll",
                "lib/race_simulator.dll"
            ]
            
            for path in possible_paths:
                if os.path.exists(path):
                    dll_path = path
                    break
            else:
                raise FileNotFoundError("race_simulator.dll not found. Please ensure the DLL is in the application directory.")
        
        try:
            self.dll = CDLL(dll_path)
            self._setup_function_signatures()
        except Exception as e:
            raise RuntimeError(f"Failed to load race_simulator.dll: {e}")
    
    def _setup_function_signatures(self):
        """Setup ctypes function signatures for DLL functions."""
        # int calculate_race(RunnerPerformance* runners, int num_runners, double distance, RaceData* out_data)
        self.dll.calculate_race.argtypes = [
            POINTER(RunnerPerformance),
            c_int,
            c_double,
            POINTER(RaceData)
        ]
        self.dll.calculate_race.restype = c_int
        
        # void free_race_data(RaceData* data)
        self.dll.free_race_data.argtypes = [POINTER(RaceData)]
        self.dll.free_race_data.restype = None
        
        # const char* get_last_error()
        self.dll.get_last_error.argtypes = []
        self.dll.get_last_error.restype = c_char_p
    
    def calculate_race(self, runners: List[Dict[str, float]], distance: float) -> Dict[str, Any]:
        """
        Calculate a race simulation.
        
        Args:
            runners: List of runner dictionaries with keys: id, max_speed, acceleration, endurance
            distance: Race distance in meters
            
        Returns:
            Dictionary containing race results and animation data
        """
        num_runners = len(runners)
        
        # Create ctypes array of RunnerPerformance structures
        runner_array = (RunnerPerformance * num_runners)()
        
        for i, runner in enumerate(runners):
            runner_array[i].id = int(runner['id'])
            runner_array[i].max_speed = float(runner['max_speed'])
            runner_array[i].acceleration = float(runner['acceleration'])
            runner_array[i].endurance = float(runner['endurance'])
        
        # Create output RaceData structure
        race_data = RaceData()
        
        # Call the DLL function
        result = self.dll.calculate_race(
            runner_array,
            num_runners,
            float(distance),
            ctypes.byref(race_data)
        )
        
        if result != 0:
            error_msg = self.dll.get_last_error()
            raise RuntimeError(f"Race calculation failed: {error_msg.decode('utf-8') if error_msg else 'Unknown error'}")
        
        try:
            # Convert results to Python data structures
            results = []
            for i in range(race_data.total_runners):
                result_struct = race_data.race_results[i]
                results.append({
                    'runner_id': result_struct.runner_id,
                    'finish_time': result_struct.finish_time,
                    'max_speed_achieved': result_struct.max_speed_achieved,
                    'average_speed': result_struct.average_speed,
                    'final_position': result_struct.final_position,
                    'did_finish': bool(result_struct.did_finish)
                })
            
            # Convert animation snapshots
            snapshots = []
            for i in range(race_data.total_snapshots):
                snapshot_struct = race_data.snapshots[i]
                snapshots.append({
                    'time': snapshot_struct.time,
                    'runner_id': snapshot_struct.runner_id,
                    'position': snapshot_struct.position,
                    'current_speed': snapshot_struct.current_speed,
                    'current_acceleration': snapshot_struct.current_acceleration
                })
            
            return {
                'total_runners': race_data.total_runners,
                'race_distance': race_data.race_distance,
                'results': results,
                'snapshots': snapshots
            }
            
        finally:
            # Always free the allocated data
            self.dll.free_race_data(ctypes.byref(race_data))


def create_sample_runners() -> List[Dict[str, float]]:
    """Create sample runner data for testing."""
    return [
        {"id": 1, "max_speed": 12.5, "acceleration": 2.8, "endurance": 0.85},
        {"id": 2, "max_speed": 11.8, "acceleration": 3.2, "endurance": 0.90},
        {"id": 3, "max_speed": 13.1, "acceleration": 2.5, "endurance": 0.80},
        {"id": 4, "max_speed": 12.0, "acceleration": 3.0, "endurance": 0.88},
        {"id": 5, "max_speed": 11.5, "acceleration": 3.5, "endurance": 0.92}
    ]


if __name__ == "__main__":
    # Test the API
    try:
        api = RaceSimulatorAPI()
        runners = create_sample_runners()
        results = api.calculate_race(runners, 100.0)
        
        print("Race Results:")
        for result in results['results']:
            print(f"Runner {result['runner_id']}: {result['finish_time']:.2f}s, Position: {result['final_position']}")
            
        print(f"\nTotal Snapshots: {len(results['snapshots'])}")
        
    except Exception as e:
        print(f"Error: {e}")