{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["Bash(git init:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git remote:*)", "Bash(git branch:*)", "Bash(git push:*)", "Bash(cp:*)", "Bash(xcopy:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(ls:*)", "Bash(find:*)", "<PERSON><PERSON>(python:*)", "Bash(pip install:*)", "<PERSON><PERSON>(touch:*)", "Bash(g++:*)", "<PERSON><PERSON>(dir:*)", "<PERSON><PERSON>(build.bat:*)", "Bash(./build.bat:*)", "Ba<PERSON>(cmd:*)", "<PERSON><PERSON>(powershell:*)", "Bash(.binReleaseAsciiRaceSimulator.exe:*)"], "deny": []}}