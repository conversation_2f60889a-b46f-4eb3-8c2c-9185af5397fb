# src/db_manager.py
import sqlite3
import os

DB_FILE = "tournament.db"

def create_connection(db_file):
    """ create a database connection to the SQLite database
        specified by db_file
    :param db_file: database file
    :return: Connection object or None
    """
    conn = None
    try:
        conn = sqlite3.connect(db_file)
        return conn
    except sqlite3.Error as e:
        print(e)

    return conn

def create_tables(conn):
    """ create tables in the SQLite database
    :param conn: Connection object
    """
    try:
        sql_create_tournaments_table = """
        CREATE TABLE IF NOT EXISTS tournaments (
            id INTEGER PRIMARY KEY,
            name TEXT NOT NULL
        );
        """

        sql_create_teams_table = """
        CREATE TABLE IF NOT EXISTS teams (
            id INTEGER PRIMARY KEY,
            name TEXT NOT NULL
        );
        """

        sql_create_players_table = """
        CREATE TABLE IF NOT EXISTS players (
            id INTEGER PRIMARY KEY,
            name TEXT NOT NULL,
            team_id INTEGER NOT NULL,
            mood REAL,
            stamina REAL,
            speed REAL,
            power REAL,
            endurance REAL,
            intelligence REAL,
            body REAL,
            adaptability REAL,
            hasMidRaceAccel INTEGER,
            hasStartWisdom INTEGER,
            hasLateRacePower INTEGER,
            stamina_current REAL,
            FOREIGN KEY (team_id) REFERENCES teams (id)
        );
        """

        sql_create_matches_table = """
        CREATE TABLE IF NOT EXISTS matches (
            id INTEGER PRIMARY KEY,
            tournament_id INTEGER NOT NULL,
            name TEXT NOT NULL,
            track_length REAL,
            FOREIGN KEY (tournament_id) REFERENCES tournaments (id)
        );
        """
        
        sql_create_match_participants_table = """
        CREATE TABLE IF NOT EXISTS match_participants (
            match_id INTEGER NOT NULL,
            player_id INTEGER NOT NULL,
            PRIMARY KEY (match_id, player_id),
            FOREIGN KEY (match_id) REFERENCES matches (id),
            FOREIGN KEY (player_id) REFERENCES players (id)
        );
        """

        sql_create_results_table = """
        CREATE TABLE IF NOT EXISTS results (
            match_id INTEGER NOT NULL,
            player_id INTEGER NOT NULL,
            position INTEGER,
            time REAL,
            PRIMARY KEY (match_id, player_id),
            FOREIGN KEY (match_id) REFERENCES matches (id),
            FOREIGN KEY (player_id) REFERENCES players (id)
        );
        """

        c = conn.cursor()
        c.execute(sql_create_tournaments_table)
        c.execute(sql_create_teams_table)
        c.execute(sql_create_players_table)
        c.execute(sql_create_matches_table)
        c.execute(sql_create_match_participants_table)
        c.execute(sql_create_results_table)
    except sqlite3.Error as e:
        print(e)

def populate_data(conn):
    """
    Populate the database with sample data.
    :param conn: Connection object
    """
    try:
        c = conn.cursor()

        # Teams
        c.execute("INSERT INTO teams (id, name) VALUES (1, 'Team Alpha')")
        c.execute("INSERT INTO teams (id, name) VALUES (2, 'Team Beta')")

        # Players for Team Alpha
        c.execute("INSERT INTO players (id, name, team_id, mood, stamina, speed, power, endurance, intelligence, body, adaptability, hasMidRaceAccel, hasStartWisdom, hasLateRacePower, stamina_current) VALUES (1, 'Player 1', 1, 80, 85, 90, 75, 88, 92, 78, 85, 1, 0, 1, 85)")
        c.execute("INSERT INTO players (id, name, team_id, mood, stamina, speed, power, endurance, intelligence, body, adaptability, hasMidRaceAccel, hasStartWisdom, hasLateRacePower, stamina_current) VALUES (2, 'Player 2', 1, 75, 90, 85, 80, 82, 88, 80, 80, 0, 1, 0, 90)")

        # Players for Team Beta
        c.execute("INSERT INTO players (id, name, team_id, mood, stamina, speed, power, endurance, intelligence, body, adaptability, hasMidRaceAccel, hasStartWisdom, hasLateRacePower, stamina_current) VALUES (3, 'Player 3', 2, 85, 80, 88, 82, 90, 85, 82, 90, 1, 1, 0, 80)")
        c.execute("INSERT INTO players (id, name, team_id, mood, stamina, speed, power, endurance, intelligence, body, adaptability, hasMidRaceAccel, hasStartWisdom, hasLateRacePower, stamina_current) VALUES (4, 'Player 4', 2, 90, 78, 82, 88, 85, 80, 88, 75, 0, 0, 1, 78)")

        # Tournament
        c.execute("INSERT INTO tournaments (id, name) VALUES (1, 'Grand Championship')")

        # Matches
        c.execute("INSERT INTO matches (id, tournament_id, name, track_length) VALUES (1, 1, 'Qualifier', 1200.0)")
        c.execute("INSERT INTO matches (id, tournament_id, name, track_length) VALUES (2, 1, 'Final', 1600.0)")

        # Match Participants
        c.execute("INSERT INTO match_participants (match_id, player_id) VALUES (1, 1)")
        c.execute("INSERT INTO match_participants (match_id, player_id) VALUES (1, 2)")
        c.execute("INSERT INTO match_participants (match_id, player_id) VALUES (1, 3)")
        c.execute("INSERT INTO match_participants (match_id, player_id) VALUES (1, 4)")

        conn.commit()
    except sqlite3.Error as e:
        print(f"Error populating data: {e}")


def get_results(conn, match_id):
    """
    Query results for a specific match.
    :param conn: the Connection object
    :param match_id: id of the match
    :return:
    """
    cur = conn.cursor()
    cur.execute("SELECT p.name, r.position, r.time FROM results r JOIN players p ON r.player_id = p.id WHERE r.match_id=? ORDER BY r.position", (match_id,))

    rows = cur.fetchall()
    return rows

def setup_database():
    """
    Main function to set up the database.
    """
    if os.path.exists(DB_FILE):
        os.remove(DB_FILE)
        
    conn = create_connection(DB_FILE)
    if conn is not None:
        create_tables(conn)
        populate_data(conn)
        conn.close()
        print(f"Database '{DB_FILE}' created and populated successfully.")
    else:
        print("Error! cannot create the database connection.")

if __name__ == '__main__':
    setup_database()
