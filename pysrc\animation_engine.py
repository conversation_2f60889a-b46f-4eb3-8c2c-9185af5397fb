"""
Pygame-based animation engine for race visualization.
Provides smooth 30 FPS animation playback with play/pause controls.
"""

import pygame
import math
from typing import Dict, List, Any, Optional, Tuple
from data_loader import DataLoader


class AnimationEngine:
    """Main animation engine using Pygame for race visualization."""
    
    def __init__(self, width: int = 1200, height: int = 800, fps: int = 30):
        """
        Initialize the animation engine.
        
        Args:
            width: Screen width in pixels
            height: Screen height in pixels
            fps: Target frames per second
        """
        pygame.init()
        self.width = width
        self.height = height
        self.fps = fps
        
        # Colors
        self.colors = {
            'background': (240, 240, 240),
            'track': (100, 100, 100),
            'track_lines': (255, 255, 255),
            'text': (0, 0, 0),
            'grid': (200, 200, 200),
            'progress_bg': (220, 220, 220),
            'progress_fill': (0, 150, 0)
        }
        
        # Initialize display
        self.screen = pygame.display.set_mode((width, height))
        pygame.display.set_caption("Running Race Simulation")
        
        # Fonts
        self.font_large = pygame.font.Font(None, 36)
        self.font_medium = pygame.font.Font(None, 24)
        self.font_small = pygame.font.Font(None, 18)
        
        # Animation state
        self.clock = pygame.time.Clock()
        self.is_playing = False
        self.current_time = 0.0
        self.total_duration = 0.0
        self.animation_data = None
        self.runner_colors = {}
        
        # Track dimensions
        self.track_start_x = 100
        self.track_end_x = width - 100
        self.track_width = self.track_end_x - self.track_start_x
        self.track_y = height // 2
        self.lane_height = 50
        
        # Control buttons
        self.button_width = 100
        self.button_height = 40
        self.play_button = pygame.Rect(width // 2 - 110, height - 80, self.button_width, self.button_height)
        self.reset_button = pygame.Rect(width // 2 + 10, height - 80, self.button_width, self.button_height)
        
        # Progress bar
        self.progress_bar = pygame.Rect(50, height - 150, width - 100, 20)
        
    def load_race_data(self, race_data: Dict[str, Any]) -> None:
        """
        Load race data for animation.
        
        Args:
            race_data: Complete race data from DataLoader
        """
        self.animation_data = race_data
        self.total_duration = race_data['race_info']['total_frames'] / 30.0 if race_data['frames'] else 0.0
        self.current_time = 0.0
        self.is_playing = False
        
        # Generate runner colors based on results order
        if 'results' in race_data:
            loader = DataLoader()
            loader.race_data = {'results': race_data['results']}
            self.runner_colors = loader.get_runner_colors()
        else:
            # Fallback colors
            self.runner_colors = {
                i: (255 * (i % 3 == 0), 255 * ((i + 1) % 3 == 0), 255 * ((i + 2) % 3 == 0))
                for i in range(1, race_data['race_info']['total_runners'] + 1)
            }
    
    def handle_events(self) -> bool:
        """
        Handle pygame events.
        
        Returns:
            True if the application should continue running
        """
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                return False
                
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_SPACE:
                    self.toggle_play_pause()
                elif event.key == pygame.K_r:
                    self.reset()
                elif event.key == pygame.K_ESCAPE:
                    return False
                    
            elif event.type == pygame.MOUSEBUTTONDOWN:
                if event.button == 1:  # Left click
                    if self.play_button.collidepoint(event.pos):
                        self.toggle_play_pause()
                    elif self.reset_button.collidepoint(event.pos):
                        self.reset()
                    elif self.progress_bar.collidepoint(event.pos):
                        # Seek to clicked position
                        x = event.pos[0] - self.progress_bar.left
                        progress = max(0, min(1, x / self.progress_bar.width))
                        self.current_time = progress * self.total_duration
        
        return True
    
    def toggle_play_pause(self) -> None:
        """Toggle between play and pause states."""
        self.is_playing = not self.is_playing
    
    def reset(self) -> None:
        """Reset animation to the beginning."""
        self.current_time = 0.0
        self.is_playing = False
    
    def update(self, delta_time: float) -> None:
        """
        Update animation state.
        
        Args:
            delta_time: Time elapsed since last frame in seconds
        """
        if self.is_playing and self.total_duration > 0:
            self.current_time += delta_time
            if self.current_time >= self.total_duration:
                self.current_time = self.total_duration
                self.is_playing = False
    
    def get_current_frame(self) -> Optional[Dict[str, Any]]:
        """
        Get the current animation frame based on current time.
        
        Returns:
            Current frame data or None if no data
        """
        if not self.animation_data or not self.animation_data['frames']:
            return None
        
        # Find the closest frame
        target_frame_time = self.current_time * 30  # Convert to frame index (30 FPS)
        frame_index = int(target_frame_time)
        
        if frame_index >= len(self.animation_data['frames']):
            return self.animation_data['frames'][-1]
        elif frame_index < 0:
            return self.animation_data['frames'][0]
        else:
            return self.animation_data['frames'][frame_index]
    
    def render(self) -> None:
        """Render the current frame."""
        self.screen.fill(self.colors['background'])
        
        if self.animation_data:
            self._render_track()
            self._render_runners()
            self._render_ui()
            self._render_results()
        else:
            self._render_no_data()
        
        pygame.display.flip()
    
    def _render_track(self) -> None:
        """Render the race track."""
        # Track background
        track_height = self.lane_height * max(1, self.animation_data['race_info']['total_runners'])
        track_rect = pygame.Rect(self.track_start_x, self.track_y - track_height // 2,
                               self.track_width, track_height)
        pygame.draw.rect(self.screen, self.colors['track'], track_rect)
        
        # Lane dividers
        for i in range(1, self.animation_data['race_info']['total_runners']):
            y = self.track_y - track_height // 2 + i * self.lane_height
            pygame.draw.line(self.screen, self.colors['track_lines'],
                           (self.track_start_x, y), (self.track_end_x, y), 2)
        
        # Start and finish lines
        pygame.draw.line(self.screen, (0, 0, 0),
                        (self.track_start_x, self.track_y - track_height // 2),
                        (self.track_start_x, self.track_y + track_height // 2), 4)
        pygame.draw.line(self.screen, (255, 0, 0),
                        (self.track_end_x, self.track_y - track_height // 2),
                        (self.track_end_x, self.track_y + track_height // 2), 4)
        
        # Distance markers
        for dist in [0, 25, 50, 75, 100]:
            x = self.track_start_x + (dist / 100.0) * self.track_width
            pygame.draw.line(self.screen, self.colors['grid'],
                           (x, self.track_y - track_height // 2 - 10),
                           (x, self.track_y + track_height // 2 + 10), 1)
            
            text = self.font_small.render(f"{dist}m", True, self.colors['text'])
            text_rect = text.get_rect(center=(x, self.track_y - track_height // 2 - 25))
            self.screen.blit(text, text_rect)
    
    def _render_runners(self) -> None:
        """Render runners on the track."""
        current_frame = self.get_current_frame()
        if not current_frame:
            return
        
        track_height = self.lane_height * self.animation_data['race_info']['total_runners']
        
        for runner_id, data in current_frame['runners'].items():
            # Calculate position on track
            progress = data['position'] / self.animation_data['race_info']['distance']
            x = self.track_start_x + progress * self.track_width
            
            # Calculate lane position
            runner_index = list(current_frame['runners'].keys()).index(runner_id)
            y = self.track_y - track_height // 2 + runner_index * self.lane_height + self.lane_height // 2
            
            # Draw runner dot
            color = self.runner_colors.get(runner_id, (128, 128, 128))
            pygame.draw.circle(self.screen, color, (int(x), int(y)), 8)
            
            # Draw runner ID
            text = self.font_small.render(str(runner_id), True, (255, 255, 255))
            text_rect = text.get_rect(center=(int(x), int(y)))
            self.screen.blit(text, text_rect)
    
    def _render_ui(self) -> None:
        """Render UI controls."""
        # Play/Pause button
        button_color = (0, 150, 0) if not self.is_playing else (150, 0, 0)
        pygame.draw.rect(self.screen, button_color, self.play_button)
        pygame.draw.rect(self.screen, (0, 0, 0), self.play_button, 2)
        
        button_text = "Play" if not self.is_playing else "Pause"
        text = self.font_medium.render(button_text, True, (255, 255, 255))
        text_rect = text.get_rect(center=self.play_button.center)
        self.screen.blit(text, text_rect)
        
        # Reset button
        pygame.draw.rect(self.screen, (100, 100, 100), self.reset_button)
        pygame.draw.rect(self.screen, (0, 0, 0), self.reset_button, 2)
        
        text = self.font_medium.render("Reset", True, (255, 255, 255))
        text_rect = text.get_rect(center=self.reset_button.center)
        self.screen.blit(text, text_rect)
        
        # Progress bar
        pygame.draw.rect(self.screen, self.colors['progress_bg'], self.progress_bar)
        
        if self.total_duration > 0:
            progress = min(1, self.current_time / self.total_duration)
            progress_width = int(self.progress_bar.width * progress)
            progress_rect = pygame.Rect(self.progress_bar.left, self.progress_bar.top,
                                      progress_width, self.progress_bar.height)
            pygame.draw.rect(self.screen, self.colors['progress_fill'], progress_rect)
        
        pygame.draw.rect(self.screen, (0, 0, 0), self.progress_bar, 2)
        
        # Time display
        time_text = f"Time: {self.current_time:.2f}s / {self.total_duration:.2f}s"
        text = self.font_medium.render(time_text, True, self.colors['text'])
        text_rect = text.get_rect(center=(self.width // 2, self.progress_bar.top - 15))
        self.screen.blit(text, text_rect)
    
    def _render_results(self) -> None:
        """Render race results sidebar."""
        if not self.animation_data or 'results' not in self.animation_data:
            return
        
        # Calculate sidebar position
        sidebar_x = self.width - 250
        sidebar_y = 50
        sidebar_width = 220
        sidebar_height = 400
        
        # Background
        sidebar_rect = pygame.Rect(sidebar_x, sidebar_y, sidebar_width, sidebar_height)
        pygame.draw.rect(self.screen, (240, 240, 240), sidebar_rect)
        pygame.draw.rect(self.screen, (0, 0, 0), sidebar_rect, 2)
        
        # Title
        title = self.font_large.render("Results", True, self.colors['text'])
        self.screen.blit(title, (sidebar_x + 10, sidebar_y + 10))
        
        # Results
        results = sorted(self.animation_data['results'], key=lambda x: x['final_position'])
        y_offset = 50
        
        for result in results[:8]:  # Show top 8
            color = self.runner_colors.get(result['runner_id'], (128, 128, 128))
            
            # Position indicator
            pos_rect = pygame.Rect(sidebar_x + 10, sidebar_y + y_offset, 30, 20)
            pygame.draw.rect(self.screen, color, pos_rect)
            
            # Position text
            pos_text = self.font_small.render(str(result['final_position']), True, (255, 255, 255))
            self.screen.blit(pos_text, (pos_rect.centerx - pos_text.get_width() // 2,
                                      pos_rect.centery - pos_text.get_height() // 2))
            
            # Runner info
            if result['did_finish']:
                info_text = f"Runner {result['runner_id']}: {result['finish_time']:.2f}s"
            else:
                info_text = f"Runner {result['runner_id']}: DNF"
            
            text = self.font_small.render(info_text, True, self.colors['text'])
            self.screen.blit(text, (sidebar_x + 50, sidebar_y + y_offset))
            
            y_offset += 25
    
    def _render_no_data(self) -> None:
        """Render when no race data is loaded."""
        text = self.font_large.render("No race data loaded", True, self.colors['text'])
        text_rect = text.get_rect(center=(self.width // 2, self.height // 2))
        self.screen.blit(text, text_rect)
        
        text2 = self.font_medium.render("Please load race data to begin", True, self.colors['text'])
        text2_rect = text2.get_rect(center=(self.width // 2, self.height // 2 + 50))
        self.screen.blit(text2, text2_rect)
    
    def run(self) -> None:
        """Main animation loop."""
        running = True
        
        while running:
            delta_time = self.clock.tick(self.fps) / 1000.0  # Convert to seconds
            
            running = self.handle_events()
            self.update(delta_time)
            self.render()
        
        pygame.quit()
    
    def __del__(self):
        """Cleanup pygame resources."""
        try:
            pygame.quit()
        except:
            pass


def main():
    """Test the animation engine."""
    from data_loader import DataLoader
    
    # Create test data
    loader = DataLoader()
    race_data = loader.calculate_race(create_sample_runners(), 100.0)
    
    # Initialize and run animation
    engine = AnimationEngine()
    engine.load_race_data(race_data)
    engine.run()


if __name__ == "__main__":
    main()