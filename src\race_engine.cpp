#include "race_engine.h"
#include <iostream>
#include <vector>
#include <algorithm>

RaceEngine::RaceEngine() : dll<PERSON><PERSON><PERSON>(nullptr), simulatorInstance(nullptr),
    createSimulator(nullptr), destroySimulator(nullptr), initializeRace(nullptr),
    startRace(nullptr), getRaceProgress(nullptr), getRaceResults(nullptr),
    isRaceFinished(nullptr), getLastError(nullptr), dllLoaded(false), raceInitialized(false) {
}

RaceEngine::~RaceEngine() {
    shutdown();
}

bool RaceEngine::initialize() {
    return true;
}

void RaceEngine::shutdown() {
    if (simulatorInstance) {
        if (destroySimulator) {
            destroySimulator(simulatorInstance);
        }
        simulatorInstance = nullptr;
    }
    
    unloadDLL();
}

bool RaceEngine::loadRaceDLL(const std::string& dllPath) {
    if (dllLoaded) {
        unloadDLL();
    }
    
    dllHandle = LoadLibraryA(dllPath.c_str());
    if (!dllHandle) {
        std::cerr << "Failed to load DLL: " << dllPath << std::endl;
        return false;
    }
    
    if (!loadDLLFunctions()) {
        unloadDLL();
        return false;
    }
    
    // Create simulator instance
    simulatorInstance = createSimulator();
    if (!simulatorInstance) {
        std::cerr << "Failed to create simulator instance" << std::endl;
        unloadDLL();
        return false;
    }
    
    dllLoaded = true;
    return true;
}

bool RaceEngine::loadDLLFunctions() {
    createSimulator = (CreateSimulatorFunc)GetProcAddress(dllHandle, "CreateRaceSimulator");
    destroySimulator = (DestroySimulatorFunc)GetProcAddress(dllHandle, "DestroyRaceSimulator");
    initializeRace = (InitializeRaceFunc)GetProcAddress(dllHandle, "InitializeRace");
    startRace = (StartRaceFunc)GetProcAddress(dllHandle, "StartRace");
    getRaceProgress = (GetRaceProgressFunc)GetProcAddress(dllHandle, "GetRaceProgress");
    getRaceResults = (GetRaceResultsFunc)GetProcAddress(dllHandle, "GetRaceResults");
    isRaceFinished = (IsRaceFinishedFunc)GetProcAddress(dllHandle, "IsRaceFinished");
    getLastError = (GetLastErrorFunc)GetProcAddress(dllHandle, "GetLastError");
    
    // Check if all required functions are loaded
    if (!createSimulator || !destroySimulator || !initializeRace || 
        !startRace || !getRaceProgress || !getRaceResults || 
        !isRaceFinished || !getLastError) {
        std::cerr << "Failed to load one or more DLL functions" << std::endl;
        return false;
    }
    
    return true;
}

bool RaceEngine::setupRace(const std::vector<std::string>& runnerNames) {
    if (!dllLoaded || !simulatorInstance) {
        return false;
    }
    
    this->runners = runnerNames;
    
    // Convert runner names to C-style strings for DLL
    std::vector<const char*> names;
    for (const auto& name : runnerNames) {
        names.push_back(name.c_str());
    }
    
    if (!initializeRace(simulatorInstance, names.data(), static_cast<int>(names.size()))) {
        std::cerr << "Failed to initialize race: " << getLastError() << std::endl;
        return false;
    }
    
    raceInitialized = true;
    return true;
}

bool RaceEngine::startRace() {
    if (!raceInitialized || !simulatorInstance) {
        return false;
    }
    
    return startRace(simulatorInstance);
}

bool RaceEngine::updateRace() {
    if (!raceInitialized || !simulatorInstance) {
        return false;
    }
    
    return true; // Race updates happen asynchronously in DLL
}

bool RaceEngine::isRaceFinished() const {
    if (!raceInitialized || !simulatorInstance || !isRaceFinished) {
        return false;
    }
    
    return isRaceFinished(simulatorInstance);
}

std::vector<RunnerInfo> RaceEngine::getRunnerInfo() const {
    std::vector<RunnerInfo> runnerInfo;
    
    if (!raceInitialized || !simulatorInstance || !getRaceProgress) {
        return runnerInfo;
    }
    
    int numRunners = static_cast<int>(runners.size());
    if (numRunners == 0) {
        return runnerInfo;
    }
    
    std::vector<float> distances(numRunners);
    std::vector<float> finishTimes(numRunners);
    std::vector<int> positions(numRunners);
    
    if (getRaceProgress(simulatorInstance, distances.data(), finishTimes.data(), positions.data())) {
        for (int i = 0; i < numRunners; ++i) {
            RunnerInfo info;
            info.name = runners[i];
            info.distance = distances[i];
            info.position = positions[i];
            info.finished = (finishTimes[i] > 0.0f);
            info.finishTime = finishTimes[i];
            runnerInfo.push_back(info);
        }
    }
    
    return runnerInfo;
}

std::vector<RunnerInfo> RaceEngine::getRaceResults() const {
    std::vector<RunnerInfo> results;
    
    if (!raceInitialized || !simulatorInstance || !getRaceResults) {
        return results;
    }
    
    int numRunners = static_cast<int>(runners.size());
    if (numRunners == 0) {
        return results;
    }
    
    std::vector<char*> names(numRunners);
    std::vector<float> finishTimes(numRunners);
    
    if (getRaceResults(simulatorInstance, names.data(), finishTimes.data(), numRunners)) {
        for (int i = 0; i < numRunners; ++i) {
            RunnerInfo info;
            info.name = names[i] ? names[i] : "Unknown";
            info.finishTime = finishTimes[i];
            info.finished = true;
            info.position = i + 1;
            results.push_back(info);
        }
    }
    
    return results;
}

std::string RaceEngine::getLastError() const {
    if (!getLastError || !simulatorInstance) {
        return "Race engine not initialized";
    }
    
    const char* error = getLastError(simulatorInstance);
    return error ? std::string(error) : "No error";
}

void RaceEngine::unloadDLL() {
    if (dllHandle) {
        FreeLibrary(dllHandle);
        dllHandle = nullptr;
        dllLoaded = false;
        
        // Reset function pointers
        createSimulator = nullptr;
        destroySimulator = nullptr;
        initializeRace = nullptr;
        startRace = nullptr;
        getRaceProgress = nullptr;
        getRaceResults = nullptr;
        isRaceFinished = nullptr;
        getLastError = nullptr;
    }
}