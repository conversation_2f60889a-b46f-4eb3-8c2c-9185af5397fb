#include <iostream>
#include <iomanip>
#include <string>
#include "race_simulator.h"
#include "random_generator.h"
#include <vector>
#include <algorithm>
#include "sqlite3.h"
#include <filesystem>

using namespace RaceSimulator;

// =================================================================
// Database Functions
// =================================================================

const char* DB_FILE = "tournament.db";

// A simple callback function for sqlite3_exec
static int callback(void* NotUsed, int argc, char** argv, char** azColName) {
    for (int i = 0; i < argc; i++) {
        std::cout << azColName[i] << ": " << (argv[i] ? argv[i] : "NULL") << std::endl;
    }
    std::cout << std::endl;
    return 0;
}

void execute_sql(sqlite3* db, const char* sql) {
    char* zErrMsg = 0;
    int rc = sqlite3_exec(db, sql, callback, 0, &zErrMsg);
    if (rc != SQLITE_OK) {
        std::cerr << "SQL error: " << zErrMsg << std::endl;
        sqlite3_free(zErrMsg);
    }
}

void setup_tournament_database() {
    sqlite3* db;
    
    // Delete old database file if it exists
    std::filesystem::remove(DB_FILE);

    int rc = sqlite3_open(DB_FILE, &db);
    if (rc) {
        std::cerr << "Can't open database: " << sqlite3_errmsg(db) << std::endl;
        return;
    } else {
        std::cout << "Opened database successfully" << std::endl;
    }

    const char* create_tables_sql[] = {
        "CREATE TABLE tournaments (id INTEGER PRIMARY KEY, name TEXT NOT NULL);",
        "CREATE TABLE teams (id INTEGER PRIMARY KEY, name TEXT NOT NULL);",
        "CREATE TABLE players (id INTEGER PRIMARY KEY, name TEXT NOT NULL, team_id INTEGER NOT NULL, mood REAL, stamina REAL, speed REAL, power REAL, endurance REAL, intelligence REAL, body REAL, adaptability REAL, hasMidRaceAccel INTEGER, hasStartWisdom INTEGER, hasLateRacePower INTEGER, stamina_current REAL, FOREIGN KEY (team_id) REFERENCES teams (id));",
        "CREATE TABLE matches (id INTEGER PRIMARY KEY, tournament_id INTEGER NOT NULL, name TEXT NOT NULL, track_length REAL, FOREIGN KEY (tournament_id) REFERENCES tournaments (id));",
        "CREATE TABLE match_participants (match_id INTEGER NOT NULL, player_id INTEGER NOT NULL, PRIMARY KEY (match_id, player_id), FOREIGN KEY (match_id) REFERENCES matches (id), FOREIGN KEY (player_id) REFERENCES players (id));",
        "CREATE TABLE results (match_id INTEGER NOT NULL, player_id INTEGER NOT NULL, position INTEGER, time REAL, PRIMARY KEY (match_id, player_id), FOREIGN KEY (match_id) REFERENCES matches (id), FOREIGN KEY (player_id) REFERENCES players (id));"
    };

    for(const auto& sql : create_tables_sql) {
        execute_sql(db, sql);
    }

    const char* populate_data_sql[] = {
        "INSERT INTO teams (id, name) VALUES (1, 'Team Alpha');",
        "INSERT INTO teams (id, name) VALUES (2, 'Team Beta');",
        "INSERT INTO players (id, name, team_id, mood, stamina, speed, power, endurance, intelligence, body, adaptability, hasMidRaceAccel, hasStartWisdom, hasLateRacePower, stamina_current) VALUES (1, 'Player 1', 1, 80, 85, 90, 75, 88, 92, 78, 85, 1, 0, 1, 85);",
        "INSERT INTO players (id, name, team_id, mood, stamina, speed, power, endurance, intelligence, body, adaptability, hasMidRaceAccel, hasStartWisdom, hasLateRacePower, stamina_current) VALUES (2, 'Player 2', 1, 75, 90, 85, 80, 82, 88, 80, 80, 0, 1, 0, 90);",
        "INSERT INTO players (id, name, team_id, mood, stamina, speed, power, endurance, intelligence, body, adaptability, hasMidRaceAccel, hasStartWisdom, hasLateRacePower, stamina_current) VALUES (3, 'Player 3', 2, 85, 80, 88, 82, 90, 85, 82, 90, 1, 1, 0, 80);",
        "INSERT INTO players (id, name, team_id, mood, stamina, speed, power, endurance, intelligence, body, adaptability, hasMidRaceAccel, hasStartWisdom, hasLateRacePower, stamina_current) VALUES (4, 'Player 4', 2, 90, 78, 82, 88, 85, 80, 88, 75, 0, 0, 1, 78);",
        "INSERT INTO tournaments (id, name) VALUES (1, 'Grand Championship');",
        "INSERT INTO matches (id, tournament_id, name, track_length) VALUES (1, 1, 'Qualifier', 1200.0);",
        "INSERT INTO match_participants (match_id, player_id) VALUES (1, 1);",
        "INSERT INTO match_participants (match_id, player_id) VALUES (1, 2);",
        "INSERT INTO match_participants (match_id, player_id) VALUES (1, 3);",
        "INSERT INTO match_participants (match_id, player_id) VALUES (1, 4);"
    };

    for(const auto& sql : populate_data_sql) {
        execute_sql(db, sql);
    }
    
    std::cout << "Database created and populated." << std::endl;
    sqlite3_close(db);
}

void display_tournament_results() {
    sqlite3* db;
    sqlite3_stmt* stmt;

    int rc = sqlite3_open_v2(DB_FILE, &db, SQLITE_OPEN_READONLY, NULL);
    if (rc) {
        std::cerr << "Can't open database: " << sqlite3_errmsg(db) << std::endl;
        return;
    }

    const char* sql = "SELECT p.name, r.position, r.time FROM results r JOIN players p ON r.player_id = p.id WHERE r.match_id=1 ORDER BY r.position";
    
    rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        std::cerr << "Failed to prepare statement: " << sqlite3_errmsg(db) << std::endl;
        sqlite3_close(db);
        return;
    }
    
    std::cout << "\n========== Tournament Results (Match 1) ==========\n";
    std::cout << std::left << std::setw(10) << "Position"
              << std::setw(20) << "Player"
              << std::setw(10) << "Time" << "\n";
    std::cout << "----------------------------------------\n";

    while ((rc = sqlite3_step(stmt)) == SQLITE_ROW) {
        int position = sqlite3_column_int(stmt, 1);
        const unsigned char* name = sqlite3_column_text(stmt, 0);
        double time = sqlite3_column_double(stmt, 2);

        std::cout << std::left << std::setw(10) << position
                  << std::setw(20) << name
                  << std::fixed << std::setprecision(3) << std::setw(10) << time << "\n";
    }

    if (rc != SQLITE_DONE) {
        std::cerr << "Error stepping through results: " << sqlite3_errmsg(db) << std::endl;
    }

    sqlite3_finalize(stmt);
    sqlite3_close(db);
}


void runTournamentFromDb() {
    std::cout << "\n========== Running Tournament from DB ==========\n";
    
    // 1. Setup database
    std::cout << "Setting up database...\n";
    setup_tournament_database();

    // 2. Call DLL
    std::cout << "\nCalling RunTournament DLL function...\n";
    int result_code = RunTournament(DB_FILE);

    if (result_code == 0) {
        std::cout << "DLL function executed successfully.\n";
        // 3. Display results
        display_tournament_results();
    } else {
        std::cout << "DLL function returned an error code: " << result_code << std::endl;
    }
}


void printHeader() {
    std::cout << "========================================\n";
    std::cout << "    Race Simulator - Basic Test Tool\n";
    std::cout << "========================================\n\n";
}

void printRunnerAttributes(const RunnerAttributes& runner) {
    std::cout << "Runner Attributes:\n";
    std::cout << "  Mood: " << static_cast<int>(runner.mood) << "/255\n";
    std::cout << "  Stamina: " << static_cast<int>(runner.stamina) << "/255\n";
    std::cout << "  Speed: " << static_cast<int>(runner.speed) << "/255\n";
    std::cout << "  Power: " << static_cast<int>(runner.power) << "/255\n";
    std::cout << "  Endurance: " << static_cast<int>(runner.endurance) << "/255\n";
    std::cout << "  Intelligence: " << static_cast<int>(runner.intelligence) << "/255\n";
    std::cout << "  Body: " << static_cast<int>(runner.body) << "/255\n";
    std::cout << "  Adaptability: " << static_cast<int>(runner.adaptability) << "\n";
    std::cout << "  Skills: ";
    if (runner.hasMidRaceAccel) std::cout << "[Mid Race Accel] ";
    if (runner.hasStartWisdom) std::cout << "[Start Wisdom] ";
    if (runner.hasLateRacePower) std::cout << "[Late Race Power] ";
    std::cout << "\n\n";
}

void printEnvironment(const Environment& env) {
    std::cout << "Environment:\n";
    std::cout << "  Track Type: ";
    switch (env.trackType) {
        case TrackType::PLASTIC: std::cout << "Plastic"; break;
        case TrackType::GRASS: std::cout << "Grass"; break;
        case TrackType::CINDER: std::cout << "Cinder"; break;
        case TrackType::DIRT: std::cout << "Dirt"; break;
    }
    std::cout << "\n  Weather: ";
    switch (env.weather) {
        case Weather::SUNNY: std::cout << "Sunny"; break;
        case Weather::VERY_SUNNY: std::cout << "Very Sunny"; break;
        case Weather::CLOUDY: std::cout << "Cloudy"; break;
        case Weather::LIGHT_RAIN: std::cout << "Light Rain"; break;
        case Weather::SMALL_RAIN: std::cout << "Small Rain"; break;
        case Weather::MEDIUM_RAIN: std::cout << "Medium Rain"; break;
        case Weather::HEAVY_RAIN: std::cout << "Heavy Rain"; break;
    }
    std::cout << "\n  Wind Direction: " << (env.windDir == WindDirection::HEADWIND ? "Headwind" : "Tailwind");
    std::cout << "\n  Wind Speed: " << env.windSpeed << " m/s\n\n";
}

void printRaceResult(const RaceResult& result) {
    std::cout << "========== Race Result ==========\n";
    std::cout << std::fixed << std::setprecision(3);
    std::cout << "Total Time: " << result.totalTime << " seconds\n";
    std::cout << "Reaction Time: " << result.reactionTime << " seconds\n";
    std::cout << "False Start: " << (result.falseStart ? "Yes" : "No") << "\n\n";
    
    std::cout << "Segment Times:\n";
    std::cout << "  0-30m:  " << result.segment1Time << " seconds\n";
    std::cout << "  30-60m: " << result.segment2Time << " seconds\n";
    std::cout << "  60-95m: " << result.segment3Time << " seconds\n";
    std::cout << "  95-100m:" << result.segment4Time << " seconds\n\n";
    
    std::cout << "Skill Activation Status:\n";
    std::cout << "  Mid Race Accel: " << (result.skillActivated[0] ? "Activated" : "Not Activated") << "\n";
    std::cout << "  Start Wisdom: " << (result.skillActivated[1] ? "Activated" : "Not Activated") << "\n";
    std::cout << "  Late Race Power: " << (result.skillActivated[2] ? "Activated" : "Not Activated") << "\n\n";
    
    // Calculate average speed
    float avgSpeed = 100.0f / result.totalTime;
    std::cout << "Average Speed: " << avgSpeed << " m/s (" << (avgSpeed * 3.6f) << " km/h)\n\n";
    
    // Calculate segment speeds
    std::cout << "Segment Speeds:\n";
    std::cout << "  0-30m:  " << (30.0f / result.segment1Time) << " m/s\n";
    std::cout << "  30-60m: " << (30.0f / result.segment2Time) << " m/s\n";
    std::cout << "  60-95m: " << (35.0f / result.segment3Time) << " m/s\n";
    std::cout << "  95-100m:" << (5.0f / result.segment4Time) << " m/s\n\n";
}

RunnerAttributes inputRunnerAttributes() {
    RunnerAttributes runner = {};
    int temp;
    
    std::cout << "Enter runner attributes (0-255):\n";
    
    std::cout << "Mood (0-255): ";
    std::cin >> temp;
    runner.mood = static_cast<uint8_t>(std::max(0, std::min(255, temp)));
    
    std::cout << "Stamina (0-255): ";
    std::cin >> temp;
    runner.stamina = static_cast<uint8_t>(std::max(0, std::min(255, temp)));
    
    std::cout << "Speed (0-255): ";
    std::cin >> temp;
    runner.speed = static_cast<uint8_t>(std::max(0, std::min(255, temp)));
    
    std::cout << "Power (0-255): ";
    std::cin >> temp;
    runner.power = static_cast<uint8_t>(std::max(0, std::min(255, temp)));
    
    std::cout << "Endurance (0-255): ";
    std::cin >> temp;
    runner.endurance = static_cast<uint8_t>(std::max(0, std::min(255, temp)));
    
    std::cout << "Intelligence (0-255): ";
    std::cin >> temp;
    runner.intelligence = static_cast<uint8_t>(std::max(0, std::min(255, temp)));
    
    std::cout << "Body (0-255): ";
    std::cin >> temp;
    runner.body = static_cast<uint8_t>(std::max(0, std::min(255, temp)));
    
    std::cout << "Adaptability (0=Excellent, 1=Good, 2=Poor, 3=Bad): ";
    std::cin >> temp;
    runner.adaptability = static_cast<Adaptability>(std::max(0, std::min(3, temp)));
    
    std::cout << "Has Mid Race Accel skill? (1=Yes, 0=No): ";
    std::cin >> temp;
    runner.hasMidRaceAccel = (temp == 1);
    
    std::cout << "Has Start Wisdom skill? (1=Yes, 0=No): ";
    std::cin >> temp;
    runner.hasStartWisdom = (temp == 1);
    
    std::cout << "Has Late Race Power skill? (1=Yes, 0=No): ";
    std::cin >> temp;
    runner.hasLateRacePower = (temp == 1);
    
    return runner;
}

Environment inputEnvironment() {
    Environment env = {};
    int temp;
    
    std::cout << "\nEnter environment conditions:\n";
    
    std::cout << "Track Type (0=Plastic, 1=Grass, 2=Cinder, 3=Dirt): ";
    std::cin >> temp;
    env.trackType = static_cast<TrackType>(std::max(0, std::min(3, temp)));
    
    std::cout << "Weather (0=Sunny, 1=Very Sunny, 2=Cloudy, 3=Light Rain, 4=Small Rain, 5=Medium Rain, 6=Heavy Rain): ";
    std::cin >> temp;
    env.weather = static_cast<Weather>(std::max(0, std::min(6, temp)));
    
    std::cout << "Wind Direction (0=Headwind, 1=Tailwind): ";
    std::cin >> temp;
    env.windDir = static_cast<WindDirection>(std::max(0, std::min(1, temp)));
    
    std::cout << "Wind Speed (m/s): ";
    std::cin >> env.windSpeed;
    
    return env;
}

void showPresetRunners() {
    std::cout << "\nPreset Runners:\n";
    std::cout << "1. All-rounder (All attributes 200, All skills)\n";
    std::cout << "2. Speed Type (Speed 255, Intelligence 200, Others 150, Mid Accel + Start Wisdom)\n";
    std::cout << "3. Endurance Type (Endurance 255, Stamina 200, Others 150, Late Power)\n";
    std::cout << "4. Balanced Type (All attributes 180, No skills)\n";
    std::cout << "5. Custom Input\n";
}

RunnerAttributes getPresetRunner(int choice) {
    RunnerAttributes runner = {};
    
    switch (choice) {
        case 1: // All-rounder
            runner.mood = 200;
            runner.stamina = 200;
            runner.speed = 200;
            runner.power = 200;
            runner.endurance = 200;
            runner.intelligence = 200;
            runner.body = 200;
            runner.adaptability = Adaptability::EXCELLENT;
            runner.hasMidRaceAccel = true;
            runner.hasStartWisdom = true;
            runner.hasLateRacePower = true;
            break;
            
        case 2: // Speed Type
            runner.mood = 150;
            runner.stamina = 150;
            runner.speed = 255;
            runner.power = 150;
            runner.endurance = 150;
            runner.intelligence = 200;
            runner.body = 150;
            runner.adaptability = Adaptability::GOOD;
            runner.hasMidRaceAccel = true;
            runner.hasStartWisdom = true;
            runner.hasLateRacePower = false;
            break;
            
        case 3: // Endurance Type
            runner.mood = 150;
            runner.stamina = 200;
            runner.speed = 150;
            runner.power = 180;
            runner.endurance = 255;
            runner.intelligence = 150;
            runner.body = 180;
            runner.adaptability = Adaptability::GOOD;
            runner.hasMidRaceAccel = false;
            runner.hasStartWisdom = false;
            runner.hasLateRacePower = true;
            break;
            
        case 4: // Balanced Type
            runner.mood = 180;
            runner.stamina = 180;
            runner.speed = 180;
            runner.power = 180;
            runner.endurance = 180;
            runner.intelligence = 180;
            runner.body = 180;
            runner.adaptability = Adaptability::GOOD;
            runner.hasMidRaceAccel = false;
            runner.hasStartWisdom = false;
            runner.hasLateRacePower = false;
            break;
            
        default:
            return inputRunnerAttributes();
    }
    
    return runner;
}

Environment getPresetEnvironment(int choice) {
    Environment env = {};
    
    switch (choice) {
        case 1: // Ideal conditions
            env.trackType = TrackType::PLASTIC;
            env.weather = Weather::SUNNY;
            env.windDir = WindDirection::TAILWIND;
            env.windSpeed = 2.0f;
            break;
            
        case 2: // Poor conditions
            env.trackType = TrackType::DIRT;
            env.weather = Weather::HEAVY_RAIN;
            env.windDir = WindDirection::HEADWIND;
            env.windSpeed = 5.0f;
            break;
            
        case 3: // Normal conditions
            env.trackType = TrackType::PLASTIC;
            env.weather = Weather::CLOUDY;
            env.windDir = WindDirection::HEADWIND;
            env.windSpeed = 1.0f;
            break;
            
        default:
            return inputEnvironment();
    }
    
    return env;
}

void runSingleSimulation() {
    std::cout << "\n========== Single Simulation ==========\n";

    // Choose runner
    showPresetRunners();
    std::cout << "Choose runner: ";
    int runnerChoice;
    std::cin >> runnerChoice;

    RunnerAttributes runner = getPresetRunner(runnerChoice);

    // Choose environment
    std::cout << "\nEnvironment Presets:\n";
    std::cout << "1. Ideal conditions (Plastic track, Sunny, Tailwind)\n";
    std::cout << "2. Poor conditions (Dirt track, Heavy rain, Headwind)\n";
    std::cout << "3. Normal conditions (Plastic track, Cloudy, Headwind)\n";
    std::cout << "4. Custom Input\n";
    std::cout << "Choose environment: ";
    int envChoice;
    std::cin >> envChoice;

    Environment env = getPresetEnvironment(envChoice);

    // Display input information
    std::cout << "\n";
    printRunnerAttributes(runner);
    printEnvironment(env);

    // Run simulation
    std::cout << "Running simulation...\n\n";
    RaceResult result = CalculateRaceResult(&runner, &env);

    // Display result
    printRaceResult(result);
}

void runMultipleSimulations() {
    std::cout << "\n========== Multiple Simulations ==========\n";

    // Choose runner and environment
    showPresetRunners();
    std::cout << "Choose runner: ";
    int runnerChoice;
    std::cin >> runnerChoice;
    RunnerAttributes runner = getPresetRunner(runnerChoice);

    std::cout << "\nEnvironment Presets:\n";
    std::cout << "1. Ideal conditions\n2. Poor conditions\n3. Normal conditions\n4. Custom Input\n";
    std::cout << "Choose environment: ";
    int envChoice;
    std::cin >> envChoice;
    Environment env = getPresetEnvironment(envChoice);

    std::cout << "Number of simulations: ";
    int simCount;
    std::cin >> simCount;

    std::cout << "\nRunning " << simCount << " simulations...\n";

    // Statistics variables
    float totalTime = 0.0f;
    float minTime = 999.0f;
    float maxTime = 0.0f;
    int falseStarts = 0;
    int skillTriggers[3] = {0, 0, 0};

    for (int i = 0; i < simCount; i++) {
        RaceResult result = CalculateRaceResult(&runner, &env);

        totalTime += result.totalTime;
        minTime = std::min(minTime, result.totalTime);
        maxTime = std::max(maxTime, result.totalTime);

        if (result.falseStart) falseStarts++;

        for (int j = 0; j < 3; j++) {
            if (result.skillActivated[j]) skillTriggers[j]++;
        }

        if ((i + 1) % (simCount / 10) == 0 || i == simCount - 1) {
            std::cout << "Progress: " << (i + 1) << "/" << simCount << "\n";
        }
    }

    // Display statistics
    std::cout << "\n========== Statistics ==========\n";
    std::cout << std::fixed << std::setprecision(3);
    std::cout << "Average Time: " << (totalTime / simCount) << " seconds\n";
    std::cout << "Best Time: " << minTime << " seconds\n";
    std::cout << "Worst Time: " << maxTime << " seconds\n";
    std::cout << "Time Range: " << (maxTime - minTime) << " seconds\n";
    std::cout << "False Start Rate: " << std::setprecision(1) << (falseStarts * 100.0f / simCount) << "%\n\n";

    std::cout << "Skill Trigger Rates:\n";
    std::cout << "  Mid Race Accel: " << (skillTriggers[0] * 100.0f / simCount) << "%\n";
    std::cout << "  Start Wisdom: " << (skillTriggers[1] * 100.0f / simCount) << "%\n";
    std::cout << "  Late Race Power: " << (skillTriggers[2] * 100.0f / simCount) << "%\n\n";
}

// Structure to hold competition results for each runner
struct CompetitionEntry {
    int runner_id;
    RunnerAttributes attributes;
    RaceResult result;
};

// Comparison function for sorting competition entries by total time
bool compareEntries(const CompetitionEntry& a, const CompetitionEntry& b) {
    return a.result.totalTime < b.result.totalTime;
}

void runCompetition() {
    std::cout << "\n========== Competition Simulation ==========\n";

    // 1. Select Environment
    std::cout << "Environment Presets:\n";
    std::cout << "1. Ideal conditions\n";
    std::cout << "2. Poor conditions\n";
    std::cout << "3. Normal conditions\n";
    std::cout << "4. Custom Input\n";
    std::cout << "Choose environment: ";
    int envChoice;
    std::cin >> envChoice;

    Environment env = getPresetEnvironment(envChoice);
    std::cout << "\nSelected Environment:\n";
    printEnvironment(env);

    // 2. Create and randomize 8 runners
    std::vector<CompetitionEntry> competition_results;
    std::cout << "Generating 8 random runners...\n";

    for (int i = 0; i < 8; ++i) {
        RunnerAttributes runner = {};
        runner.mood = RandomInt(100, 255);
        runner.stamina = RandomInt(100, 255);
        runner.speed = RandomInt(100, 255);
        runner.power = RandomInt(100, 255);
        runner.endurance = RandomInt(100, 255);
        runner.intelligence = RandomInt(100, 255);
        runner.body = RandomInt(100, 255);
        runner.adaptability = static_cast<Adaptability>(RandomInt(0, 3));
        runner.hasMidRaceAccel = RandomBool(0.5f);
        runner.hasStartWisdom = RandomBool(0.5f);
        runner.hasLateRacePower = RandomBool(0.5f);

        // 3. Run simulation
        RaceResult result = CalculateRaceResult(&runner, &env);

        // 4. Store results
        competition_results.push_back({i + 1, runner, result});
    }

    std::cout << "Simulation complete. Calculating rankings...\n";

    // 5. Sort results
    std::sort(competition_results.begin(), competition_results.end(), compareEntries);

    // 6. Print rankings
    std::cout << "\n========== Competition Ranking ==========\n";
    std::cout << std::left << std::setw(6) << "Rank"
              << std::setw(5) << "ID"
              << std::setw(14) << "Total Time"
              << std::setw(8) << "Speed"
              << std::setw(10) << "Stamina"
              << std::setw(8) << "Power"
              << std::setw(12) << "Endurance"
              << std::setw(8) << "Mood"
              << std::setw(15) << "Intelligence"
              << std::setw(8) << "Body" << "\n";
    std::cout << "--------------------------------------------------------------------------------------------------\n";

    int rank = 1;
    for (const auto& entry : competition_results) {
        std::cout << std::left << std::setw(6) << rank++
                  << std::setw(5) << entry.runner_id
                  << std::fixed << std::setprecision(3) << std::setw(14) << entry.result.totalTime
                  << std::setw(8) << static_cast<int>(entry.attributes.speed)
                  << std::setw(10) << static_cast<int>(entry.attributes.stamina)
                  << std::setw(8) << static_cast<int>(entry.attributes.power)
                  << std::setw(12) << static_cast<int>(entry.attributes.endurance)
                  << std::setw(8) << static_cast<int>(entry.attributes.mood)
                  << std::setw(15) << static_cast<int>(entry.attributes.intelligence)
                  << std::setw(8) << static_cast<int>(entry.attributes.body) << "\n";
    }
}

int main() {
    // Initialize random number generator
    InitializeRaceSimulator();

    printHeader();

    int choice;
    do {
        std::cout << "Choose operation:\n";
        std::cout << "1. Run single simulation\n";
        std::cout << "2. Run multiple simulations\n";
        std::cout << "3. Run Competition\n";
        std::cout << "4. Run Tournament from DB\n";
        std::cout << "5. Exit\n";
        std::cout << "Please choose: ";
        std::cin >> choice;

        switch (choice) {
            case 1:
                runSingleSimulation();
                break;
            case 2:
                runMultipleSimulations();
                break;
            case 3:
                runCompetition();
                break;
            case 4:
                runTournamentFromDb();
                break;
            case 5:
                std::cout << "Thank you for using!\n";
                break;
            default:
                std::cout << "Invalid choice, please try again.\n";
                break;
        }

        if (choice != 5) {
            std::cout << "\nPress Enter to continue...";
            std::cin.ignore();
            std::cin.get();
            std::cout << "\n";
        }

    } while (choice != 5);

    return 0;
}
