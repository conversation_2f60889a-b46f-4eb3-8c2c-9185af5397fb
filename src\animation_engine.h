#pragma once

#include "console_utils.h"
#include "race_engine.h"
#include <vector>
#include <string>

class AnimationEngine {
public:
    static constexpr int FPS = 30;
    static constexpr double FRAME_TIME = 1000.0 / FPS; // milliseconds
    
    AnimationEngine();
    ~AnimationEngine();
    
    bool Initialize();
    void RunRaceAnimation(RaceEngine& raceEngine);
    void DrawRaceScene(const RaceResult& result);
    void DrawResults(const RaceResult& result);
    void DrawStartScreen();
    void DrawLoadingScreen();
    
private:
    ConsoleUtils console_;
    
    // Track dimensions
    static constexpr int TRACK_Y = 5;
    static constexpr int TRACK_HEIGHT = 20;
    static constexpr int TRACK_WIDTH = 80;
    static constexpr int TRACK_START_X = 5;
    static constexpr int TRACK_END_X = TRACK_START_X + TRACK_WIDTH;
    
    // Runner symbols
    static constexpr char RUNNER_SYMBOLS[] = {'R', 'F', 'B', 'S', 'T', 'L'};
    static constexpr WORD RUNNER_COLORS[] = {
        FOREGROUND_RED | FOREGROUND_INTENSITY,
        FOREGROUND_GREEN | FOREGROUND_INTENSITY,
        FOREGROUND_BLUE | FOREGROUND_INTENSITY,
        FOREGROUND_RED | FOREGROUND_GREEN | FOREGROUND_INTENSITY,
        FOREGROUND_RED | FOREGROUND_BLUE | FOREGROUND_INTENSITY,
        FOREGROUND_GREEN | FOREGROUND_BLUE | FOREGROUND_INTENSITY
    };
    
    void DrawTrack();
    void DrawRunners(const std::vector<Runner>& runners);
    void DrawHUD(const RaceResult& result);
    void DrawProgressBar(int x, int y, double progress, int width, WORD color);
    void DrawRunnerInfo(int runnerIndex, const Runner& runner, int y);
    void ClearArea(int x, int y, int width, int height);
    void DrawTextCentered(const std::string& text, int y, WORD color = FOREGROUND_RED | FOREGROUND_GREEN | FOREGROUND_BLUE);
    void DrawTime(double time, int x, int y);
    
    // Animation effects
    void DrawStartCountdown();
    void DrawFinishEffect(int runnerIndex);
    int GetRunnerSymbolIndex(const std::string& runnerName);
    
    // Input handling
    bool IsKeyPressed(int virtualKeyCode);
    void SleepMilliseconds(int milliseconds);
};