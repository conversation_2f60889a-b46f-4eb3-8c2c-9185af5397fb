cmake_minimum_required(VERSION 3.15)

project(AsciiRaceSimulator VERSION 1.0.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Output directories
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)

# Include directories
include_directories(${CMAKE_SOURCE_DIR}/include)

# Windows-specific settings
if(WIN32)
    set(CMAKE_WIN32_EXECUTABLE OFF)  # Console application
    link_directories(${CMAKE_SOURCE_DIR}/lib)
endif()

# ASCII Race Simulator executable
add_executable(AsciiRaceSimulator
    src/main.cpp
    src/console_utils.cpp
)

# Test refresh rate executable
add_executable(TestRefreshRate
    test_refresh_rate.cpp
)

# Test console utils executable
add_executable(TestConsoleUtils
    test_console_utils.cpp
    src/console_utils.cpp
)

# ANSI demo executable
add_executable(TestANSIDemo
    test_ansi_demo.cpp
    src/console_utils.cpp
)

# race_simulator (DLL)
add_library(race_simulator SHARED
    src/race_simulator.cpp
    src/random_generator.cpp
    src/skill_system.cpp
    src/environment.cpp
    src/runner_attributes.cpp
    src/console_utils.cpp
    src/sqlite3.c
)

# Set preprocessor definitions for exporting symbols
target_compile_definitions(race_simulator PRIVATE RACE_SIMULATOR_EXPORTS)

# basic_test executable
add_executable(basic_test
    tests/basic_test.cpp
    src/sqlite3.c
)

# Link basic_test to the race_simulator library
target_link_libraries(basic_test PRIVATE race_simulator)


# Link libraries
if(WIN32)
    target_link_libraries(AsciiRaceSimulator winmm.lib)
    target_link_libraries(TestRefreshRate user32.lib gdi32.lib)
    target_link_libraries(TestConsoleUtils winmm.lib)
    target_link_libraries(TestANSIDemo winmm.lib)
endif()

# Set subsystem to console
if(WIN32)
    set_target_properties(AsciiRaceSimulator PROPERTIES
        LINK_FLAGS "/SUBSYSTEM:CONSOLE"
    )
endif()

# Compiler settings
if(MSVC)
    target_compile_options(AsciiRaceSimulator PRIVATE /W4)
endif()