#include <iostream>
#include <iomanip>
#include <string>
#include <sstream>
#include "race_simulator.h"
#include "skill_system.h"
#include "runner_attributes.h"
#include "environment.h"

void printHeader() {
    std::cout << "========================================\n";
    std::cout << "    赛马娘跑步模拟器 - 手动测试工具\n";
    std::cout << "========================================\n\n";
}

void printRunnerAttributes(const RunnerAttributes& runner) {
    std::cout << "选手属性:\n";
    std::cout << "  心情: " << static_cast<int>(runner.mood) << "/255\n";
    std::cout << "  体力: " << static_cast<int>(runner.stamina) << "/255\n";
    std::cout << "  速度: " << static_cast<int>(runner.speed) << "/255\n";
    std::cout << "  力量: " << static_cast<int>(runner.power) << "/255\n";
    std::cout << "  耐力: " << static_cast<int>(runner.endurance) << "/255\n";
    std::cout << "  智力: " << static_cast<int>(runner.intelligence) << "/255\n";
    std::cout << "  身体: " << static_cast<int>(runner.body) << "/255\n";
    std::cout << "  适应性: " << static_cast<int>(runner.adaptability) << "\n";
    std::cout << "  技能: ";
    if (runner.hasMidRaceAccel) std::cout << "[中途加速] ";
    if (runner.hasStartWisdom) std::cout << "[起跑智慧] ";
    if (runner.hasLateRacePower) std::cout << "[后程发力] ";
    std::cout << "\n\n";
}

void printEnvironment(const Environment& env) {
    std::cout << "环境条件:\n";
    std::cout << "  赛道类型: ";
    switch (env.trackType) {
        case TrackType::PLASTIC: std::cout << "塑胶跑道"; break;
        case TrackType::GRASS: std::cout << "草地"; break;
        case TrackType::CINDER: std::cout << "煤渣跑道"; break;
        case TrackType::DIRT: std::cout << "土地"; break;
    }
    std::cout << "\n  天气: ";
    switch (env.weather) {
        case Weather::SUNNY: std::cout << "晴"; break;
        case Weather::VERY_SUNNY: std::cout << "大晴"; break;
        case Weather::CLOUDY: std::cout << "乌云"; break;
        case Weather::LIGHT_RAIN: std::cout << "微雨"; break;
        case Weather::SMALL_RAIN: std::cout << "小雨"; break;
        case Weather::MEDIUM_RAIN: std::cout << "中雨"; break;
        case Weather::HEAVY_RAIN: std::cout << "大雨"; break;
    }
    std::cout << "\n  风向: " << (env.windDir == WindDirection::HEADWIND ? "逆风" : "顺风");
    std::cout << "\n  风速: " << env.windSpeed << " m/s\n\n";
}

void printDetailedCalculations(const RunnerAttributes& runner, const Environment& env) {
    std::cout << "========== 详细计算过程 ==========\n\n";
    
    // 计算基础属性影响
    std::cout << "1. 基础属性计算:\n";
    Mood mood = RaceSimulator::GetMoodFromValue(runner.mood);
    float moodMultiplier = RaceSimulator::CalculateMoodMultiplier(mood);
    std::cout << "   心情倍率: " << std::fixed << std::setprecision(3) << moodMultiplier << "\n";
    
    float adaptMultiplier = RaceSimulator::CalculateAdaptabilityMultiplier(runner.adaptability, env.trackType);
    std::cout << "   适应性倍率: " << adaptMultiplier << "\n";
    
    float staminaMultiplier = RaceSimulator::CalculateStaminaMultiplier(runner.stamina);
    std::cout << "   体力倍率: " << staminaMultiplier << "\n";
    
    float enduranceMultiplier = RaceSimulator::CalculateEnduranceMultiplier(runner.endurance);
    std::cout << "   耐力倍率: " << enduranceMultiplier << "\n";
    
    float overallMultiplier = RaceSimulator::CalculateOverallAttributeMultiplier(&runner, &env);
    std::cout << "   综合属性倍率: " << overallMultiplier << "\n\n";
    
    // 计算反应时间
    std::cout << "2. 起跑反应时间:\n";
    float baseReactionTime = RaceSimulator::CalculateReactionTime(&runner);
    std::cout << "   基础反应时间: " << baseReactionTime << " 秒\n";
    
    float falseStartProb = RaceSimulator::CalculateFalseStartProbability(&runner);
    std::cout << "   抢跑概率: " << (falseStartProb * 100) << "%\n\n";
    
    // 计算技能触发概率
    std::cout << "3. 技能系统:\n";
    float skillTriggerProb = RaceSimulator::CalculateSkillTriggerProbability(runner.intelligence);
    std::cout << "   技能触发概率: " << (skillTriggerProb * 100) << "%\n";
    
    if (runner.hasStartWisdom) {
        auto wisdom = RaceSimulator::CalculateStartWisdom(&runner, true);
        std::cout << "   起跑智慧效果: 反应时间减少 " << (wisdom.reactionTimeReduction * 100) << "%\n";
    }
    
    if (runner.hasMidRaceAccel) {
        auto accel = RaceSimulator::CalculateMidRaceAccel(&runner, true);
        std::cout << "   中途加速效果: 速度提升 " << accel.speedBoost << " m/s (距离 " 
                  << accel.triggerStart << "-" << accel.triggerEnd << "m)\n";
    }
    
    if (runner.hasLateRacePower) {
        auto power = RaceSimulator::CalculateLateRacePower(&runner, true);
        std::cout << "   后程发力效果: 力量提升 " << power.powerBoost 
                  << " (触发距离 " << power.triggerDistance << "m)\n";
    }
    std::cout << "\n";
}

void printRaceResult(const RaceResult& result) {
    std::cout << "========== 比赛结果 ==========\n";
    std::cout << std::fixed << std::setprecision(3);
    std::cout << "总时间: " << result.totalTime << " 秒\n";
    std::cout << "起跑反应时间: " << result.reactionTime << " 秒\n";
    std::cout << "抢跑: " << (result.falseStart ? "是" : "否") << "\n\n";
    
    std::cout << "分段时间:\n";
    std::cout << "  0-30m:  " << result.segment1Time << " 秒\n";
    std::cout << "  30-60m: " << result.segment2Time << " 秒\n";
    std::cout << "  60-95m: " << result.segment3Time << " 秒\n";
    std::cout << "  95-100m:" << result.segment4Time << " 秒\n\n";
    
    std::cout << "Skill Activation Status:\n";
    std::cout << "  Mid Race Accel: " << (result.skillActivated[0] ? "Activated" : "Not Activated") << "\n";
    std::cout << "  Start Wisdom: " << (result.skillActivated[1] ? "Activated" : "Not Activated") << "\n";
    std::cout << "  Late Race Power: " << (result.skillActivated[2] ? "Activated" : "Not Activated") << "\n\n";
    
    // 计算平均速度
    float avgSpeed = 100.0f / result.totalTime;
    std::cout << "平均速度: " << avgSpeed << " m/s (" << (avgSpeed * 3.6f) << " km/h)\n\n";
}

RunnerAttributes inputRunnerAttributes() {
    RunnerAttributes runner = {};
    int temp;
    
    std::cout << "请输入选手属性 (0-255):\n";
    
    std::cout << "心情 (0-255): ";
    std::cin >> temp;
    runner.mood = static_cast<uint8_t>(std::max(0, std::min(255, temp)));
    
    std::cout << "体力 (0-255): ";
    std::cin >> temp;
    runner.stamina = static_cast<uint8_t>(std::max(0, std::min(255, temp)));
    
    std::cout << "速度 (0-255): ";
    std::cin >> temp;
    runner.speed = static_cast<uint8_t>(std::max(0, std::min(255, temp)));
    
    std::cout << "力量 (0-255): ";
    std::cin >> temp;
    runner.power = static_cast<uint8_t>(std::max(0, std::min(255, temp)));
    
    std::cout << "耐力 (0-255): ";
    std::cin >> temp;
    runner.endurance = static_cast<uint8_t>(std::max(0, std::min(255, temp)));
    
    std::cout << "智力 (0-255): ";
    std::cin >> temp;
    runner.intelligence = static_cast<uint8_t>(std::max(0, std::min(255, temp)));
    
    std::cout << "身体 (0-255): ";
    std::cin >> temp;
    runner.body = static_cast<uint8_t>(std::max(0, std::min(255, temp)));
    
    std::cout << "适应性 (0=极佳, 1=好, 2=不良, 3=差): ";
    std::cin >> temp;
    runner.adaptability = static_cast<Adaptability>(std::max(0, std::min(3, temp)));
    
    std::cout << "是否有中途加速技能? (1=是, 0=否): ";
    std::cin >> temp;
    runner.hasMidRaceAccel = (temp == 1);
    
    std::cout << "是否有起跑智慧技能? (1=是, 0=否): ";
    std::cin >> temp;
    runner.hasStartWisdom = (temp == 1);
    
    std::cout << "是否有后程发力技能? (1=是, 0=否): ";
    std::cin >> temp;
    runner.hasLateRacePower = (temp == 1);
    
    return runner;
}

Environment inputEnvironment() {
    Environment env = {};
    int temp;
    
    std::cout << "\n请输入环境条件:\n";
    
    std::cout << "赛道类型 (0=塑胶, 1=草地, 2=煤渣, 3=土地): ";
    std::cin >> temp;
    env.trackType = static_cast<TrackType>(std::max(0, std::min(3, temp)));
    
    std::cout << "天气 (0=晴, 1=大晴, 2=乌云, 3=微雨, 4=小雨, 5=中雨, 6=大雨): ";
    std::cin >> temp;
    env.weather = static_cast<Weather>(std::max(0, std::min(6, temp)));
    
    std::cout << "风向 (0=逆风, 1=顺风): ";
    std::cin >> temp;
    env.windDir = static_cast<WindDirection>(std::max(0, std::min(1, temp)));
    
    std::cout << "风速 (m/s): ";
    std::cin >> env.windSpeed;
    
    return env;
}

void showPresetRunners() {
    std::cout << "\n预设选手:\n";
    std::cout << "1. 全能型 (所有属性200, 全技能)\n";
    std::cout << "2. 速度型 (速度255, 智力200, 其他150, 中途加速+起跑智慧)\n";
    std::cout << "3. 耐力型 (耐力255, 体力200, 其他150, 后程发力)\n";
    std::cout << "4. 平衡型 (所有属性180, 无技能)\n";
    std::cout << "5. 自定义输入\n";
}

RunnerAttributes getPresetRunner(int choice) {
    RunnerAttributes runner = {};

    switch (choice) {
        case 1: // 全能型
            runner.mood = 200;
            runner.stamina = 200;
            runner.speed = 200;
            runner.power = 200;
            runner.endurance = 200;
            runner.intelligence = 200;
            runner.body = 200;
            runner.adaptability = Adaptability::EXCELLENT;
            runner.hasMidRaceAccel = true;
            runner.hasStartWisdom = true;
            runner.hasLateRacePower = true;
            break;

        case 2: // 速度型
            runner.mood = 150;
            runner.stamina = 150;
            runner.speed = 255;
            runner.power = 150;
            runner.endurance = 150;
            runner.intelligence = 200;
            runner.body = 150;
            runner.adaptability = Adaptability::GOOD;
            runner.hasMidRaceAccel = true;
            runner.hasStartWisdom = true;
            runner.hasLateRacePower = false;
            break;

        case 3: // 耐力型
            runner.mood = 150;
            runner.stamina = 200;
            runner.speed = 150;
            runner.power = 180;
            runner.endurance = 255;
            runner.intelligence = 150;
            runner.body = 180;
            runner.adaptability = Adaptability::GOOD;
            runner.hasMidRaceAccel = false;
            runner.hasStartWisdom = false;
            runner.hasLateRacePower = true;
            break;

        case 4: // 平衡型
            runner.mood = 180;
            runner.stamina = 180;
            runner.speed = 180;
            runner.power = 180;
            runner.endurance = 180;
            runner.intelligence = 180;
            runner.body = 180;
            runner.adaptability = Adaptability::GOOD;
            runner.hasMidRaceAccel = false;
            runner.hasStartWisdom = false;
            runner.hasLateRacePower = false;
            break;

        default:
            return inputRunnerAttributes();
    }

    return runner;
}

Environment getPresetEnvironment(int choice) {
    Environment env = {};

    switch (choice) {
        case 1: // 理想条件
            env.trackType = TrackType::PLASTIC;
            env.weather = Weather::SUNNY;
            env.windDir = WindDirection::TAILWIND;
            env.windSpeed = 2.0f;
            break;

        case 2: // 恶劣条件
            env.trackType = TrackType::DIRT;
            env.weather = Weather::HEAVY_RAIN;
            env.windDir = WindDirection::HEADWIND;
            env.windSpeed = 5.0f;
            break;

        case 3: // 普通条件
            env.trackType = TrackType::PLASTIC;
            env.weather = Weather::CLOUDY;
            env.windDir = WindDirection::HEADWIND;
            env.windSpeed = 1.0f;
            break;

        default:
            return inputEnvironment();
    }

    return env;
}

void showMenu() {
    std::cout << "选择操作:\n";
    std::cout << "1. 运行单次模拟\n";
    std::cout << "2. 运行多次模拟 (统计分析)\n";
    std::cout << "3. 比较不同选手\n";
    std::cout << "4. 退出\n";
    std::cout << "请选择: ";
}

void runSingleSimulation() {
    std::cout << "\n========== 单次模拟 ==========\n";

    // 选择选手
    showPresetRunners();
    std::cout << "选择选手: ";
    int runnerChoice;
    std::cin >> runnerChoice;

    RunnerAttributes runner = getPresetRunner(runnerChoice);

    // 选择环境
    std::cout << "\n环境预设:\n";
    std::cout << "1. 理想条件 (塑胶跑道, 晴天, 顺风)\n";
    std::cout << "2. 恶劣条件 (土地, 大雨, 逆风)\n";
    std::cout << "3. 普通条件 (塑胶跑道, 乌云, 逆风)\n";
    std::cout << "4. 自定义输入\n";
    std::cout << "选择环境: ";
    int envChoice;
    std::cin >> envChoice;

    Environment env = getPresetEnvironment(envChoice);

    // 显示输入信息
    std::cout << "\n";
    printRunnerAttributes(runner);
    printEnvironment(env);

    // 询问是否显示详细计算
    std::cout << "是否显示详细计算过程? (1=是, 0=否): ";
    int showDetails;
    std::cin >> showDetails;

    if (showDetails) {
        printDetailedCalculations(runner, env);
    }

    // 运行模拟
    std::cout << "正在运行模拟...\n\n";
    RaceResult result = CalculateRaceResult(&runner, &env);

    // 显示结果
    printRaceResult(result);
}

void runMultipleSimulations() {
    std::cout << "\n========== 多次模拟统计 ==========\n";

    // 选择选手和环境
    showPresetRunners();
    std::cout << "选择选手: ";
    int runnerChoice;
    std::cin >> runnerChoice;
    RunnerAttributes runner = getPresetRunner(runnerChoice);

    std::cout << "\n环境预设:\n";
    std::cout << "1. 理想条件\n2. 恶劣条件\n3. 普通条件\n4. 自定义输入\n";
    std::cout << "选择环境: ";
    int envChoice;
    std::cin >> envChoice;
    Environment env = getPresetEnvironment(envChoice);

    std::cout << "模拟次数: ";
    int simCount;
    std::cin >> simCount;

    std::cout << "\n正在运行 " << simCount << " 次模拟...\n";

    // 统计变量
    float totalTime = 0.0f;
    float minTime = 999.0f;
    float maxTime = 0.0f;
    int falseStarts = 0;
    int skillTriggers[3] = {0, 0, 0};

    for (int i = 0; i < simCount; i++) {
        RaceResult result = CalculateRaceResult(&runner, &env);

        totalTime += result.totalTime;
        minTime = std::min(minTime, result.totalTime);
        maxTime = std::max(maxTime, result.totalTime);

        if (result.falseStart) falseStarts++;

        for (int j = 0; j < 3; j++) {
            if (result.skillActivated[j]) skillTriggers[j]++;
        }

        if ((i + 1) % (simCount / 10) == 0 || i == simCount - 1) {
            std::cout << "进度: " << (i + 1) << "/" << simCount << "\n";
        }
    }

    // 显示统计结果
    std::cout << "\n========== 统计结果 ==========\n";
    std::cout << std::fixed << std::setprecision(3);
    std::cout << "平均时间: " << (totalTime / simCount) << " 秒\n";
    std::cout << "最佳时间: " << minTime << " 秒\n";
    std::cout << "最差时间: " << maxTime << " 秒\n";
    std::cout << "时间差: " << (maxTime - minTime) << " 秒\n";
    std::cout << "抢跑率: " << std::setprecision(1) << (falseStarts * 100.0f / simCount) << "%\n\n";

    std::cout << "技能触发率:\n";
    std::cout << "  中途加速: " << (skillTriggers[0] * 100.0f / simCount) << "%\n";
    std::cout << "  起跑智慧: " << (skillTriggers[1] * 100.0f / simCount) << "%\n";
    std::cout << "  后程发力: " << (skillTriggers[2] * 100.0f / simCount) << "%\n\n";
}

void compareRunners() {
    std::cout << "\n========== 选手比较 ==========\n";

    std::cout << "选择第一个选手:\n";
    showPresetRunners();
    std::cout << "选择: ";
    int choice1;
    std::cin >> choice1;
    RunnerAttributes runner1 = getPresetRunner(choice1);

    std::cout << "\n选择第二个选手:\n";
    showPresetRunners();
    std::cout << "选择: ";
    int choice2;
    std::cin >> choice2;
    RunnerAttributes runner2 = getPresetRunner(choice2);

    std::cout << "\n选择环境:\n";
    std::cout << "1. 理想条件\n2. 恶劣条件\n3. 普通条件\n4. 自定义输入\n";
    std::cout << "选择: ";
    int envChoice;
    std::cin >> envChoice;
    Environment env = getPresetEnvironment(envChoice);

    std::cout << "比较次数: ";
    int compareCount;
    std::cin >> compareCount;

    std::cout << "\n正在进行比较...\n";

    float totalTime1 = 0.0f, totalTime2 = 0.0f;
    int wins1 = 0, wins2 = 0, ties = 0;

    for (int i = 0; i < compareCount; i++) {
        RaceResult result1 = CalculateRaceResult(&runner1, &env);
        RaceResult result2 = CalculateRaceResult(&runner2, &env);

        totalTime1 += result1.totalTime;
        totalTime2 += result2.totalTime;

        if (result1.totalTime < result2.totalTime) wins1++;
        else if (result1.totalTime > result2.totalTime) wins2++;
        else ties++;
    }

    std::cout << "\n========== 比较结果 ==========\n";
    std::cout << std::fixed << std::setprecision(3);
    std::cout << "选手1 平均时间: " << (totalTime1 / compareCount) << " 秒\n";
    std::cout << "选手2 平均时间: " << (totalTime2 / compareCount) << " 秒\n";
    std::cout << "胜负记录: " << wins1 << " - " << wins2 << " - " << ties << "\n";
    std::cout << "选手1 胜率: " << std::setprecision(1) << (wins1 * 100.0f / compareCount) << "%\n\n";
}

int main() {
    // 初始化随机数生成器
    InitializeRaceSimulator();

    printHeader();

    int choice;
    do {
        showMenu();
        std::cin >> choice;

        switch (choice) {
            case 1:
                runSingleSimulation();
                break;
            case 2:
                runMultipleSimulations();
                break;
            case 3:
                compareRunners();
                break;
            case 4:
                std::cout << "感谢使用！\n";
                break;
            default:
                std::cout << "无效选择，请重试。\n";
                break;
        }

        if (choice != 4) {
            std::cout << "\n按回车键继续...";
            std::cin.ignore();
            std::cin.get();
            std::cout << "\n";
        }

    } while (choice != 4);

    return 0;
}
