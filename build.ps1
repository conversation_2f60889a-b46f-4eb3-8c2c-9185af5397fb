# PowerShell build script for ASCII Running Simulation
# Optimized for Windows with MSBuild and Visual Studio

param(
    [string]$Configuration = "Release",
    [string]$Platform = "x64",
    [switch]$Clean = $false,
    [switch]$Test = $false,
    [switch]$Run = $false,
    [switch]$Setup = $false
)

# Set error handling
$ErrorActionPreference = "Stop"

Write-Host "ASCII Running Simulation Build System" -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green

# Check for Visual Studio/MSBuild
function Find-MSBuild {
    $vswhere = "${env:ProgramFiles(x86)}\Microsoft Visual Studio\Installer\vswhere.exe"
    if (Test-Path $vswhere) {
        $vsPath = & $vswhere -latest -products * -requires Microsoft.Component.MSBuild -property installationPath
        if ($vsPath) {
            $msbuild = Join-Path $vsPath "MSBuild\Current\Bin\MSBuild.exe"
            if (Test-Path $msbuild) {
                return $msbuild
            }
        }
    }
    
    # Fallback to PATH
    $msbuild = Get-Command "MSBuild.exe" -ErrorAction SilentlyContinue
    if ($msbuild) {
        return $msbuild.Source
    }
    
    return $null
}

function Find-CMake {
    $cmake = Get-Command "cmake.exe" -ErrorAction SilentlyContinue
    if ($cmake) {
        return $cmake.Source
    }
    return $null
}

function Test-BuildEnvironment {
    Write-Host "Checking build environment..." -ForegroundColor Yellow
    
    $msbuild = Find-MSBuild
    $cmake = Find-CMake
    
    if (-not $msbuild) {
        Write-Host "ERROR: MSBuild not found. Please install Visual Studio or Build Tools" -ForegroundColor Red
        return $false
    }
    
    if (-not $cmake) {
        Write-Host "ERROR: CMake not found. Please install CMake from https://cmake.org" -ForegroundColor Red
        return $false
    }
    
    Write-Host "MSBuild: $msbuild" -ForegroundColor Green
    Write-Host "CMake: $cmake" -ForegroundColor Green
    
    return $true
}

function Invoke-CMakeBuild {
    param([string]$Config, [string]$Platform)
    
    Write-Host "Configuring CMake for $Config|$Platform..." -ForegroundColor Yellow
    
    $buildDir = "build\$Platform\$Config"
    if (-not (Test-Path $buildDir)) {
        New-Item -ItemType Directory -Path $buildDir -Force | Out-Null
    }
    
    Push-Location $buildDir
    
    try {
        # Configure with CMake
        & cmake ..\..\.. -G "Visual Studio 17 2022" -A $Platform -DCMAKE_BUILD_TYPE=$Config
        if ($LASTEXITCODE -ne 0) {
            throw "CMake configuration failed"
        }
        
        # Build with MSBuild
        Write-Host "Building with MSBuild..." -ForegroundColor Yellow
        & cmake --build . --config $Config --parallel
        if ($LASTEXITCODE -ne 0) {
            throw "Build failed"
        }
        
        Write-Host "Build completed successfully!" -ForegroundColor Green
        Write-Host "Output: $((Get-Location).Path)\bin\$Config\" -ForegroundColor Green
        
    } finally {
        Pop-Location
    }
}

function Invoke-Tests {
    Write-Host "Running tests..." -ForegroundColor Yellow
    
    $testExe = "build\x64\Release\bin\Release\test_skill_system.exe"
    if (Test-Path $testExe) {
        Write-Host "Running unit tests..." -ForegroundColor Cyan
        & $testExe
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Tests failed!" -ForegroundColor Red
            return $false
        }
    }
    
    # Run basic functionality test
    $simulatorExe = "build\x64\Release\bin\Release\AsciiRaceSimulator.exe"
    if (Test-Path $simulatorExe) {
        Write-Host "Testing basic functionality..." -ForegroundColor Cyan
        
        # Test with timeout
        $process = Start-Process -FilePath $simulatorExe -ArgumentList "--test-mode" -PassThru -NoNewWindow
        if ($process.WaitForExit(5000)) {
            Write-Host "Basic functionality test passed" -ForegroundColor Green
        } else {
            $process.Kill()
            Write-Host "Basic functionality test timed out" -ForegroundColor Yellow
        }
    }
    
    return $true
}

function Invoke-Clean {
    Write-Host "Cleaning build directories..." -ForegroundColor Yellow
    
    $dirsToClean = @("build", "bin", "obj", "lib")
    foreach ($dir in $dirsToClean) {
        if (Test-Path $dir) {
            Remove-Item -Recurse -Force $dir
            Write-Host "Cleaned $dir" -ForegroundColor Green
        }
    }
    
    # Also clean CMake cache files
    Get-ChildItem -Recurse -Force -ErrorAction SilentlyContinue | 
        Where-Object { $_.Name -match "CMakeCache|cmake_install|CMakeFiles" } | 
        Remove-Item -Recurse -Force
}

function Copy-Dependencies {
    Write-Host "Copying dependencies..." -ForegroundColor Yellow
    
    # Ensure lib directory exists
    if (-not (Test-Path "lib")) {
        New-Item -ItemType Directory -Path "lib" -Force | Out-Null
    }
    
    # Copy built DLLs to lib directory
    $dllPath = "build\x64\Release\bin\Release\race_simulator.dll"
    if (Test-Path $dllPath) {
        Copy-Item $dllPath "lib\RaceSimulation.dll" -Force
        Write-Host "Copied race_simulator.dll to lib\RaceSimulation.dll" -ForegroundColor Green
    }
    
    # Copy to bin directory for immediate testing
    $binPath = "bin"
    if (-not (Test-Path $binPath)) {
        New-Item -ItemType Directory -Path $binPath -Force | Out-Null
    }
    
    $exePath = "build\x64\Release\bin\Release\AsciiRaceSimulator.exe"
    if (Test-Path $exePath) {
        Copy-Item $exePath $binPath -Force
        Copy-Item "lib\RaceSimulation.dll" "$binPath\RaceSimulation.dll" -Force
        Write-Host "Copied executable and DLL to bin directory" -ForegroundColor Green
    }
}

# Main execution
if ($Setup) {
    Write-Host "Setting up build environment..." -ForegroundColor Green
    if (-not (Test-BuildEnvironment)) {
        exit 1
    }
    Write-Host "Build environment is ready!" -ForegroundColor Green
    exit 0
}

if ($Clean) {
    Invoke-Clean
    exit 0
}

# Check environment
if (-not (Test-BuildEnvironment)) {
    exit 1
}

# Build configurations
$configs = @("Debug", "Release")
$platforms = @("x64")

foreach ($platform in $platforms) {
    foreach ($config in $configs) {
        if ($Configuration -eq "All" -or $Configuration -eq $config) {
            Invoke-CMakeBuild -Config $config -Platform $platform
        }
    }
}

# Copy dependencies
Copy-Dependencies

# Run tests if requested
if ($Test) {
    Invoke-Tests
}

# Run simulator if requested
if ($Run) {
    $exePath = "bin\AsciiRaceSimulator.exe"
    if (Test-Path $exePath) {
        Write-Host "Starting ASCII Running Simulation..." -ForegroundColor Green
        & $exePath
    } else {
        Write-Host "ERROR: Executable not found at $exePath" -ForegroundColor Red
        exit 1
    }
}

Write-Host "Build process completed!" -ForegroundColor Green