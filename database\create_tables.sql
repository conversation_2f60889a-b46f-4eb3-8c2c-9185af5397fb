-- Race Simulator Database Schema
-- Create tables for teams, players, roles, tournaments, and matches

-- Enable foreign key constraints
PRAGMA foreign_keys = ON;

-- Roles table - defines player archetypes
CREATE TABLE IF NOT EXISTS roles (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    -- Base attribute ranges for this role type
    base_mood_min INTEGER DEFAULT 100,
    base_mood_max INTEGER DEFAULT 200,
    base_stamina_min INTEGER DEFAULT 100,
    base_stamina_max INTEGER DEFAULT 200,
    base_speed_min INTEGER DEFAULT 100,
    base_speed_max INTEGER DEFAULT 200,
    base_power_min INTEGER DEFAULT 100,
    base_power_max INTEGER DEFAULT 200,
    base_endurance_min INTEGER DEFAULT 100,
    base_endurance_max INTEGER DEFAULT 200,
    base_intelligence_min INTEGER DEFAULT 100,
    base_intelligence_max INTEGER DEFAULT 200,
    base_body_min INTEGER DEFAULT 100,
    base_body_max INTEGER DEFAULT 200,
    base_adaptability INTEGER DEFAULT 1, -- 0=BAD, 1=POOR, 2=GOOD, 3=EXCELLENT
    -- Skill probabilities for this role
    mid_race_accel_chance REAL DEFAULT 0.3,
    start_wisdom_chance REAL DEFAULT 0.3,
    late_race_power_chance REAL DEFAULT 0.3,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Teams table
CREATE TABLE IF NOT EXISTS teams (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    owner_name TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Players table - represents individual runners
CREATE TABLE IF NOT EXISTS players (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    team_id INTEGER NOT NULL,
    role_id INTEGER NOT NULL,
    -- Core attributes (0-255 range to match C++ RunnerAttributes)
    mood INTEGER DEFAULT 150 CHECK(mood >= 0 AND mood <= 255),
    stamina INTEGER DEFAULT 150 CHECK(stamina >= 0 AND stamina <= 255),
    speed INTEGER DEFAULT 150 CHECK(speed >= 0 AND speed <= 255),
    power INTEGER DEFAULT 150 CHECK(power >= 0 AND power <= 255),
    endurance INTEGER DEFAULT 150 CHECK(endurance >= 0 AND endurance <= 255),
    intelligence INTEGER DEFAULT 150 CHECK(intelligence >= 0 AND intelligence <= 255),
    body INTEGER DEFAULT 150 CHECK(body >= 0 AND body <= 255),
    adaptability INTEGER DEFAULT 1 CHECK(adaptability >= 0 AND adaptability <= 3),
    -- Skills (boolean flags)
    has_mid_race_accel BOOLEAN DEFAULT 0,
    has_start_wisdom BOOLEAN DEFAULT 0,
    has_late_race_power BOOLEAN DEFAULT 0,
    -- Current status
    stamina_current INTEGER DEFAULT 255 CHECK(stamina_current >= 0 AND stamina_current <= 255),
    experience_points INTEGER DEFAULT 0,
    total_races INTEGER DEFAULT 0,
    wins INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (team_id) REFERENCES teams(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id)
);

-- Tournaments table
CREATE TABLE IF NOT EXISTS tournaments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    description TEXT,
    status TEXT DEFAULT 'planned' CHECK(status IN ('planned', 'active', 'completed', 'cancelled')),
    max_participants INTEGER DEFAULT 8,
    entry_fee INTEGER DEFAULT 0,
    prize_pool INTEGER DEFAULT 0,
    -- Tournament settings
    distance REAL DEFAULT 100.0,
    track_type INTEGER DEFAULT 0, -- 0=PLASTIC, 1=GRASS, 2=CINDER, 3=DIRT
    weather INTEGER DEFAULT 0, -- 0=SUNNY, 1=VERY_SUNNY, 2=CLOUDY, etc.
    wind_direction INTEGER DEFAULT 0, -- 0=HEADWIND, 1=TAILWIND
    wind_speed REAL DEFAULT 0.0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    started_at DATETIME,
    completed_at DATETIME
);

-- Tournament stages (qualifiers, semifinals, finals, etc.)
CREATE TABLE IF NOT EXISTS tournament_stages (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    tournament_id INTEGER NOT NULL,
    name TEXT NOT NULL,
    stage_order INTEGER NOT NULL,
    status TEXT DEFAULT 'pending' CHECK(status IN ('pending', 'active', 'completed')),
    advancement_count INTEGER DEFAULT 4, -- How many advance to next stage
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (tournament_id) REFERENCES tournaments(id) ON DELETE CASCADE
);

-- Tournament participants
CREATE TABLE IF NOT EXISTS tournament_participants (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    tournament_id INTEGER NOT NULL,
    player_id INTEGER NOT NULL,
    registration_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    status TEXT DEFAULT 'registered' CHECK(status IN ('registered', 'active', 'eliminated', 'withdrawn')),
    FOREIGN KEY (tournament_id) REFERENCES tournaments(id) ON DELETE CASCADE,
    FOREIGN KEY (player_id) REFERENCES players(id) ON DELETE CASCADE,
    UNIQUE(tournament_id, player_id)
);

-- Matches table - individual races
CREATE TABLE IF NOT EXISTS matches (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    tournament_id INTEGER,
    stage_id INTEGER,
    match_number INTEGER DEFAULT 1,
    status TEXT DEFAULT 'scheduled' CHECK(status IN ('scheduled', 'in_progress', 'completed', 'cancelled')),
    -- Environment settings for this match
    track_type INTEGER DEFAULT 0,
    weather INTEGER DEFAULT 0,
    wind_direction INTEGER DEFAULT 0,
    wind_speed REAL DEFAULT 0.0,
    distance REAL DEFAULT 100.0,
    -- Timing
    scheduled_time DATETIME,
    started_at DATETIME,
    completed_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (tournament_id) REFERENCES tournaments(id) ON DELETE CASCADE,
    FOREIGN KEY (stage_id) REFERENCES tournament_stages(id) ON DELETE CASCADE
);

-- Match participants - which players are in each race
CREATE TABLE IF NOT EXISTS match_participants (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    match_id INTEGER NOT NULL,
    player_id INTEGER NOT NULL,
    lane_number INTEGER,
    -- Race results (filled after race completion)
    finish_position INTEGER,
    total_time REAL,
    reaction_time REAL,
    segment1_time REAL,
    segment2_time REAL,
    segment3_time REAL,
    segment4_time REAL,
    false_start BOOLEAN DEFAULT 0,
    skill1_activated BOOLEAN DEFAULT 0,
    skill2_activated BOOLEAN DEFAULT 0,
    skill3_activated BOOLEAN DEFAULT 0,
    stamina_cost INTEGER DEFAULT 10,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (match_id) REFERENCES matches(id) ON DELETE CASCADE,
    FOREIGN KEY (player_id) REFERENCES players(id) ON DELETE CASCADE,
    UNIQUE(match_id, player_id)
);

-- Player training/development log
CREATE TABLE IF NOT EXISTS training_sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    player_id INTEGER NOT NULL,
    training_type TEXT NOT NULL, -- 'speed', 'endurance', 'power', etc.
    attribute_improved TEXT,
    improvement_amount INTEGER DEFAULT 1,
    stamina_cost INTEGER DEFAULT 5,
    session_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (player_id) REFERENCES players(id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_players_team ON players(team_id);
CREATE INDEX IF NOT EXISTS idx_players_role ON players(role_id);
CREATE INDEX IF NOT EXISTS idx_tournament_participants_tournament ON tournament_participants(tournament_id);
CREATE INDEX IF NOT EXISTS idx_tournament_participants_player ON tournament_participants(player_id);
CREATE INDEX IF NOT EXISTS idx_match_participants_match ON match_participants(match_id);
CREATE INDEX IF NOT EXISTS idx_match_participants_player ON match_participants(player_id);
CREATE INDEX IF NOT EXISTS idx_matches_tournament ON matches(tournament_id);
CREATE INDEX IF NOT EXISTS idx_matches_stage ON matches(stage_id);
