#pragma once

#include <windows.h>
#include <string>
#include <vector>

struct Runner {
    std::string name;
    double speed;           // meters per second
    double endurance;
    double position;        // meters from start
    bool finished;
    double finishTime;      // seconds
    int place;
};

struct RaceConfig {
    double distance;        // meters
    int runnerCount;
    std::vector<Runner> runners;
    double updateInterval;  // seconds
};

struct RaceResult {
    bool raceFinished;
    double elapsedTime;     // seconds
    std::vector<Runner> runners;
    std::string winner;
};

class RaceEngine {
public:
    RaceEngine();
    ~RaceEngine();

    bool LoadDLL(const std::string& dllPath);
    void UnloadDLL();
    bool InitializeRace(const RaceConfig& config);
    RaceResult UpdateRace(double deltaTime);
    void ResetRace();
    
    const RaceResult& GetCurrentResult() const { return currentResult_; }
    bool IsDLLLoaded() const { return hDLL_ != nullptr; }
    bool IsRaceActive() const { return raceActive_; }

private:
    typedef bool (*InitializeRaceFunc)(double distance, int runnerCount, const char** runnerNames, 
                                       const double* speeds, const double* endurances);
    typedef bool (*UpdateRaceFunc)(double deltaTime, double* positions, double* times, bool* finished, int* places);
    typedef bool (*GetRaceInfoFunc)(double* elapsedTime, bool* raceFinished, char* winnerName, int nameBufferSize);
    typedef void (*ResetRaceFunc)();

    HMODULE hDLL_;
    InitializeRaceFunc initializeRace_;
    UpdateRaceFunc updateRace_;
    GetRaceInfoFunc getRaceInfo_;
    ResetRaceFunc resetRace_;
    
    RaceConfig currentConfig_;
    RaceResult currentResult_;
    bool raceActive_;
    
    std::vector<const char*> runnerNamePtrs_;
    std::vector<double> runnerSpeeds_;
    std::vector<double> runnerEndurances_;
    std::vector<double> positions_;
    std::vector<double> times_;
    std::vector<bool> finished_;
    std::vector<int> places_;
    
    void SetupRunnerData();
    void UpdateRunnerData();
    void CreateDefaultRace();
    double GetRandomSpeed();
    double GetRandomEndurance();
};