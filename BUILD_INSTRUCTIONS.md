# ASCII Running Simulation - Complete Build Guide

## Overview
This guide provides complete instructions for building the C++ ASCII running simulation system on Windows with full MSBuild/Visual Studio integration.

## Prerequisites

### Required Software
1. **Visual Studio 2019 or later** (Community/Professional/Enterprise)
   - Download: https://visualstudio.microsoft.com/downloads/
   - Required workloads:
     - Desktop development with C++
     - Windows 10 SDK (latest)

2. **CMake 3.15 or later**
   - Download: https://cmake.org/download/
   - Ensure CMake is added to system PATH

### Optional Software
- **PowerShell 5.1 or later** (for enhanced build scripts)
- **Git** (for version control)

## Quick Start

### Method 1: PowerShell (Recommended)
```powershell
# Verify environment
.\verify_build.ps1

# Build release version
.\build.ps1 -Configuration Release

# Build debug version
.\build.ps1 -Configuration Debug

# Build and run tests
.\build.ps1 -Test

# Run the simulation
.\build.ps1 -Run
```

### Method 2: Command Prompt
```cmd
# Verify environment
verify_build.bat

# Build release version
build.bat release

# Build debug version
build.bat debug

# Run the simulation
build.bat run
```

## Detailed Build Instructions

### 1. Environment Setup
```powershell
# Check all prerequisites
.\build.ps1 -Setup
```

### 2. Clean Build
```powershell
# Clean all build artifacts
.\build.ps1 -Clean

# Then build fresh
.\build.ps1 -Configuration Release
```

### 3. Build Configurations

#### Release Build (Optimized)
```powershell
.\build.ps1 -Configuration Release -Platform x64
```

#### Debug Build (Development)
```powershell
.\build.ps1 -Configuration Debug -Platform x64
```

#### 32-bit Build
```powershell
.\build.ps1 -Configuration Release -Platform x86
```

### 4. CMake Direct Usage

#### Configure
```cmd
mkdir build
pushd build
cmake .. -G "Visual Studio 17 2022" -A x64
cmake --build . --config Release --parallel
popd
```

#### Build with Testing
```cmd
mkdir build
cd build
cmake .. -G "Visual Studio 17 2022" -A x64 -DBUILD_TESTING=ON
cmake --build . --config Release --parallel
ctest --output-on-failure
```

## Output Structure

After successful build:
```
D:\simc\
├── bin\
│   └── AsciiRaceSimulator.exe      # Main executable
├── lib\
│   └── RaceSimulation.dll          # Race simulation DLL
├── build\
│   └── x64\
│       ├── Debug\
│       └── Release\
└── tests\
    ├── test_skill_system.exe       # Skill system tests
    ├── test_race_engine.exe        # Race engine tests
    └── integration_test.exe        # Integration tests
```

## Testing

### Run All Tests
```powershell
# Via build script
.\build.ps1 -Test

# Direct execution
.\verify_build.ps1
```

### Individual Test Executables
```cmd
# Skill system tests
.\build\x64\Release\bin\Release\test_skill_system.exe

# Race engine tests
.\build\x64\Release\bin\Release\test_race_engine.exe

# Integration tests
.\build\x64\Release\bin\Release\integration_test.exe
```

### CTest (CMake Test Framework)
```cmd
cd build\x64\Release
ctest --output-on-failure
```

## Troubleshooting

### Common Issues

#### 1. MSBuild/Visual Studio Not Found
**Error**: `MSBuild not found`
**Solution**: 
- Install Visual Studio 2019 or later
- Ensure Visual Studio is in PATH or run from "Developer Command Prompt"

#### 2. CMake Not Found
**Error**: `CMake not found`
**Solution**:
- Install CMake from https://cmake.org/download/
- Add CMake to system PATH
- Restart command prompt

#### 3. Build Failures
**Error**: `Build failed`
**Solutions**:
- Clean build: `build.bat clean`
- Check Windows SDK is installed
- Verify Visual Studio C++ workload

#### 4. DLL Loading Issues
**Error**: `Failed to load RaceSimulation.dll`
**Solutions**:
- Ensure DLL is in same directory as executable
- Check architecture mismatch (x64 vs x86)
- Verify DLL dependencies with `dumpbin /dependents`

### Debug Information

#### Get Build Information
```powershell
# Show environment details
.\build.ps1 -Setup

# Verbose build output
.\build.ps1 -Configuration Release -Verbose
```

#### Check DLL Dependencies
```cmd
# Visual Studio tools required
"C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\bin\Hostx64\x64\dumpbin.exe" /dependents lib\RaceSimulation.dll
```

## Development Workflow

### 1. Initial Setup
```powershell
# Clone repository
git clone [repository-url]
cd simc

# Verify environment
.\verify_build.ps1

# Build development version
.\build.ps1 -Configuration Debug
```

### 2. Development Cycle
```powershell
# Make changes to source code
# ... edit files ...

# Build and test
.\build.ps1 -Configuration Debug -Test

# Run simulation
.\build.ps1 -Run
```

### 3. Release Build
```powershell
# Clean build for release
.\build.ps1 -Clean

# Build release
.\build.ps1 -Configuration Release

# Verify release
.\verify_build.ps1
```

## Performance Notes

### Expected Performance
- **Build time**: 30-60 seconds (depends on system)
- **Executable size**: ~2-3 MB
- **DLL size**: ~1-2 MB
- **Startup time**: < 1 second

### Optimization Settings
- **Release build**: Full optimization (`/O2`)
- **Debug build**: Debug symbols (`/Zi`, `/Od`)
- **LTO**: Enabled for release builds

## CI/CD Integration

### GitHub Actions (Example)
```yaml
name: Build and Test
on: [push, pull_request]
jobs:
  build-windows:
    runs-on: windows-latest
    steps:
    - uses: actions/checkout@v2
    - name: Setup MSBuild
      uses: microsoft/setup-msbuild@v1
    - name: Build
      run: build.bat release
    - name: Test
      run: verify_build.bat
```

## Support

For build issues:
1. Check this guide first
2. Run verification scripts
3. Check system requirements
4. Create issue with build logs

## Version Information
- **CMake**: 3.15+
- **Visual Studio**: 2019+
- **Windows SDK**: 10.0+
- **C++ Standard**: C++17