"""
Main application controller for the running race simulation.
Coordinates between data loading, race calculation, and animation display.
"""

import os
import sys
import json
import argparse
from typing import Dict, Any, Optional
from data_loader import DataLoader
try:
    from animation_engine import AnimationEngine
    PYGAME_AVAILABLE = True
except ImportError:
    PYGAME_AVAILABLE = False
    AnimationEngine = None
from race_simulator_types import create_sample_runners


class UIController:
    """Main application controller managing the complete simulation workflow."""
    
    def __init__(self):
        """Initialize the application controller."""
        self.data_loader = None
        self.animation_engine = None
        self.race_data = None
        self.config = self._load_config()
        
    def _load_config(self) -> Dict[str, Any]:
        """
        Load application configuration from file or use defaults.
        
        Returns:
            Configuration dictionary
        """
        config_file = "config.json"
        default_config = {
            "window": {
                "width": 1200,
                "height": 800,
                "fps": 30
            },
            "race": {
                "default_distance": 100.0,
                "dll_path": None
            },
            "ui": {
                "show_fps": False,
                "auto_play": False
            }
        }
        
        try:
            if os.path.exists(config_file):
                with open(config_file, 'r') as f:
                    user_config = json.load(f)
                    # Merge with defaults
                    for key, value in default_config.items():
                        if key in user_config:
                            default_config[key].update(user_config[key])
            return default_config
            
        except Exception as e:
            print(f"Warning: Could not load config file: {e}")
            return default_config
    
    def parse_arguments(self, args: list = None) -> argparse.Namespace:
        """
        Parse command line arguments.
        
        Args:
            args: Command line arguments (None for sys.argv)
            
        Returns:
            Parsed arguments
        """
        parser = argparse.ArgumentParser(
            description="Running Race Simulation Visualization",
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
Examples:
  %(prog)s                                   # Run with sample data
  %(prog)s --runners runners.json           # Load runners from file
  %(prog)s --distance 200                   # Run 200m race
  %(prog)s --dll path/to/race_simulator.dll # Use specific DLL
  %(prog)s --export results.json            # Export results to file
            """
        )
        
        parser.add_argument(
            "--runners", "-r",
            type=str,
            help="JSON file containing runner data"
        )
        
        parser.add_argument(
            "--distance", "-d",
            type=float,
            default=self.config["race"]["default_distance"],
            help="Race distance in meters (default: 100)"
        )
        
        parser.add_argument(
            "--dll",
            type=str,
            default=self.config["race"]["dll_path"],
            help="Path to race_simulator.dll"
        )
        
        parser.add_argument(
            "--export", "-e",
            type=str,
            help="Export race results to JSON file"
        )
        
        parser.add_argument(
            "--sample",
            action="store_true",
            help="Use sample runner data"
        )
        
        parser.add_argument(
            "--no-gui",
            action="store_true",
            help="Calculate race without GUI (for testing)"
        )
        
        parser.add_argument(
            "--verbose", "-v",
            action="store_true",
            help="Enable verbose output"
        )
        
        return parser.parse_args(args)
    
    def load_runner_data(self, filename: Optional[str] = None) -> list:
        """
        Load runner data from file or use sample data.
        
        Args:
            filename: Path to JSON file with runner data
            
        Returns:
            List of runner dictionaries
        """
        if filename and os.path.exists(filename):
            print(f"Loading runners from: {filename}")
            return self.data_loader.load_runners_from_file(filename)
        else:
            print("Using sample runner data")
            return create_sample_runners()
    
    def calculate_race(self, runners: list, distance: float) -> bool:
        """
        Calculate race results using the DLL or fallback.
        
        Args:
            runners: List of runner dictionaries
            distance: Race distance in meters
            
        Returns:
            True if successful, False otherwise
        """
        try:
            print(f"Calculating {distance}m race with {len(runners)} runners...")
            self.race_data = self.data_loader.calculate_race(runners, distance)
            
            if self.race_data:
                print("\n" + self.data_loader.get_race_summary())
                return True
            else:
                print("Error: Failed to calculate race")
                return False
                
        except Exception as e:
            print(f"Error calculating race: {e}")
            return False
    
    def export_results(self, filename: str) -> bool:
        """
        Export race results to JSON file.
        
        Args:
            filename: Output file path
            
        Returns:
            True if successful, False otherwise
        """
        if not self.race_data:
            print("Error: No race data to export")
            return False
            
        success = self.data_loader.export_results(filename)
        if success:
            print(f"Results exported to: {filename}")
        else:
            print(f"Error: Failed to export results to {filename}")
        
        return success
    
    def run_animation(self) -> None:
        """Start the animation engine."""
        if not self.race_data:
            print("Error: No race data available for animation")
            return
        
        if not PYGAME_AVAILABLE:
            print("Pygame not available - skipping animation")
            return
        
        try:
            print("Starting animation...")
            self.animation_engine = AnimationEngine(
                width=self.config["window"]["width"],
                height=self.config["window"]["height"],
                fps=self.config["window"]["fps"]
            )
            
            self.animation_engine.load_race_data(self.race_data)
            
            if self.config["ui"]["auto_play"]:
                self.animation_engine.toggle_play_pause()
            
            self.animation_engine.run()
            
        except Exception as e:
            print(f"Error running animation: {e}")
    
    def run_headless(self, runners: list, distance: float) -> None:
        """
        Run the simulation without GUI (for testing/export).
        
        Args:
            runners: List of runner dictionaries
            distance: Race distance in meters
        """
        if self.calculate_race(runners, distance):
            print(f"Race simulation completed successfully")
            print(f"Total animation frames: {len(self.race_data['frames'])}")
            print(f"Total duration: {len(self.race_data['frames']) / 30.0:.2f} seconds")
    
    def run(self, args: list = None) -> int:
        """
        Main application entry point.
        
        Args:
            args: Command line arguments (None for sys.argv)
            
        Returns:
            Exit code (0 for success, 1 for error)
        """
        try:
            parsed_args = self.parse_arguments(args)
            
            # Initialize data loader
            dll_path = parsed_args.dll or self.config["race"]["dll_path"]
            self.data_loader = DataLoader(dll_path)
            
            # Load runner data
            if parsed_args.sample or not parsed_args.runners:
                runners = create_sample_runners()
            else:
                runners = self.load_runner_data(parsed_args.runners)
            
            if not runners:
                print("Error: No runner data available")
                return 1
            
            # Calculate race
            if not self.calculate_race(runners, parsed_args.distance):
                return 1
            
            # Export results if requested
            if parsed_args.export:
                self.export_results(parsed_args.export)
            
            # Run animation or headless mode
            if parsed_args.no_gui:
                self.run_headless(runners, parsed_args.distance)
            else:
                self.run_animation()
            
            return 0
            
        except KeyboardInterrupt:
            print("\nOperation cancelled by user")
            return 0
        except Exception as e:
            print(f"Fatal error: {e}")
            if parsed_args and getattr(parsed_args, 'verbose', False):
                import traceback
                traceback.print_exc()
            return 1
    
    def create_sample_runner_file(self, filename: str = "sample_runners.json") -> None:
        """
        Create a sample runner file for users to modify.
        
        Args:
            filename: Output filename
        """
        sample_data = {
            "description": "Sample runner data for race simulation",
            "race_distance": 100.0,
            "runners": create_sample_runners()
        }
        
        try:
            with open(filename, 'w') as f:
                json.dump(sample_data, f, indent=2)
            print(f"Sample runner file created: {filename}")
            print("Edit this file to customize runner parameters")
            
        except Exception as e:
            print(f"Error creating sample file: {e}")


def main():
    """Application entry point."""
    controller = UIController()
    
    # Check for simple commands
    if len(sys.argv) > 1 and sys.argv[1] == "--create-sample":
        filename = sys.argv[2] if len(sys.argv) > 2 else "sample_runners.json"
        controller.create_sample_runner_file(filename)
        return
    
    # Run main application
    exit_code = controller.run()
    sys.exit(exit_code)


if __name__ == "__main__":
    main()