#pragma once

#include <vector>
#include <string>
#include <chrono>
#include <math>

struct RunnerInfo;

class AnimationEngine {
public:
    AnimationEngine();
    ~AnimationEngine();

    bool initialize(int screenWidth, int screenHeight);
    void shutdown();

    void setRaceDistance(float distance);
    void setTrackLength(int length);
    
    void updateAnimation(const std::vector<RunnerInfo>& runners);
    void renderFrame();
    void clearScreen();
    
    void setTargetFPS(int fps);
    void waitForNextFrame();

private:
    int screenWidth;
    int screenHeight;
    int trackLength;
    float raceDistance;
    int targetFPS;
    
    std::chrono::steady_clock::time_point lastFrameTime;
    
    // ASCII art frames for runners
    std::vector<std::string> runnerFrames;
    
    void initializeRunnerFrames();
    void drawTrack();
    void drawRunners(const std::vector<RunnerInfo>& runners);
    void drawRunner(int lane, float position, const std::string& name);
    void drawFinishLine();
    void drawStartLine();
    void drawHUD(const std::vector<RunnerInfo>& runners);
    
    int getLaneY(int lane) const;
    int getPositionX(float distance) const;
    std::string getRunnerSprite(float progress) const;
    
    void drawText(int x, int y, const std::string& text);
    void drawHorizontalLine(int y, int x1, int x2, char c);
    void drawVerticalLine(int x, int y1, int y2, char c);
};