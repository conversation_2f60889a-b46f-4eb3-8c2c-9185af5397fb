# Running Simulation UI - Technical Specification

## Problem Statement
- **Business Issue**: The existing C++ race simulator provides race results through a DLL interface, but lacks visual representation of the race progression, making it difficult for users to understand how different runner attributes and environmental conditions affect race performance over time.
- **Current State**: A command-line basic_test.exe exists that can run single simulations, multiple simulations, and competitions, but provides only final race results without any visual progression data.
- **Expected Outcome**: A Python-based visualization system using Pygame that runs the complete race simulation first (via DLL integration), then provides animated playback of the race with play/pause controls, showing runner positions over time on a simple track visualization.

## Solution Overview
- **Approach**: Create a two-phase system: Phase 1 runs the race algorithm via DLL integration to get complete results, Phase 2 animates these results using Pygame with a simple dot-based track visualization and playback controls.
- **Core Changes**: 
  - DLL integration using Python ctypes for race calculation
  - Data structure for storing complete race progression data
  - Pygame-based animation system with play/pause functionality
  - Simple track visualization using character-based display
  - Windows standalone executable packaging
- **Success Criteria**: 
  - Successfully loads race_simulator.dll and calculates race results
  - Displays animated race progression with accurate timing
  - Provides responsive play/pause controls
  - Creates standalone Windows executable that runs without Python installation

## Technical Implementation

### Database Changes
- **No database changes required** - this is a visualization layer on top of existing DLL functionality

### Code Changes

#### DLL Integration Strategy (ctypes)
- **Files to Create**:
  - `src/visualizer/dll_interface.py` - DLL loading and function mapping
  - `src/visualizer/race_calculator.py` - Race result calculation wrapper

- **DLL Function Signatures** (from race_simulator.h):
  ```c
  // Initialize the race simulator
  void InitializeRaceSimulator()
  
  // Calculate race result for single runner
  RaceResult CalculateRaceResult(const RunnerAttributes* runner, const Environment* env)
  
  // Get error messages
  const char* GetLastErrorMessage()
  
  // Set random seed for reproducible results
  void SetRandomSeed(uint32_t seed)
  ```

- **Python ctypes mapping**:
  ```python
  # DLL loading and type definitions
  import ctypes
  from ctypes import wintypes
  
  class CRunnerAttributes(ctypes.Structure):
      _fields_ = [
          ("mood", ctypes.c_uint8),
          ("stamina", ctypes.c_uint8),
          ("speed", ctypes.c_uint8),
          ("power", ctypes.c_uint8),
          ("endurance", ctypes.c_uint8),
          ("intelligence", ctypes.c_uint8),
          ("body", ctypes.c_uint8),
          ("adaptability", ctypes.c_int),
          ("hasMidRaceAccel", ctypes.c_bool),
          ("hasStartWisdom", ctypes.c_bool),
          ("hasLateRacePower", ctypes.c_bool)
      ]
  
  class CEnvironment(ctypes.Structure):
      _fields_ = [
          ("trackType", ctypes.c_int),
          ("weather", ctypes.c_int),
          ("windDir", ctypes.c_int),
          ("windSpeed", ctypes.c_float)
      ]
  
  class CRaceResult(ctypes.Structure):
      _fields_ = [
          ("totalTime", ctypes.c_float),
          ("reactionTime", ctypes.c_float),
          ("segment1Time", ctypes.c_float),
          ("segment2Time", ctypes.c_float),
          ("segment3Time", ctypes.c_float),
          ("segment4Time", ctypes.c_float),
          ("falseStart", ctypes.c_bool),
          ("skillActivated", ctypes.c_bool * 3)
      ]
  ```

#### Data Structure for Complete Race Results
- **Files to Create**:
  - `src/visualizer/data_models.py` - Enhanced data structures for animation
  - `src/visualizer/race_data_generator.py` - Generate detailed race progression

- **Enhanced Data Structures**:
  ```python
  @dataclass
  class RaceSnapshot:
      runner_id: int
      position: float  # 0-100 meters
      time_elapsed: float  # seconds since start
      current_segment: int  # 1-4 (0-30m, 30-60m, 60-95m, 95-100m)
      speed: float  # m/s at this moment
      skill_active: bool = False  # if any skill is active at this moment
  
  @dataclass
  class AnimatedRaceResult:
      runner: RunnerAttributes
      final_result: RaceResult
      snapshots: List[RaceSnapshot]
      segment_speeds: List[float]  # [0-30m, 30-60m, 60-95m, 95-100m]
  
  @dataclass
  class RaceAnimationData:
      environment: Environment
      runners: List[AnimatedRaceResult]
      total_duration: float  # longest race time
  ```

- **Race Progression Generation**:
  - Calculate position at 0.1-second intervals
  - Derive segment speeds from segment times
  - Apply skill activation timing based on segment boundaries
  - Handle reaction time delays

#### Animation Playback System Design
- **Files to Create**:
  - `src/visualizer/animation_engine.py` - Core animation logic
  - `src/visualizer/track_renderer.py` - Visual track rendering
  - `src/visualizer/playback_controls.py` - Play/pause interface

- **Animation Engine**:
  ```python
  class AnimationEngine:
      def __init__(self, race_data: RaceAnimationData):
          self.race_data = race_data
          self.current_time = 0.0
          self.is_playing = False
          self.playback_speed = 1.0  # 1x, 2x, 4x, 0.5x
          self.frame_rate = 30  # FPS
      
      def update(self, delta_time: float):
          if self.is_playing:
              self.current_time += delta_time * self.playback_speed
              if self.current_time > self.race_data.total_duration:
                  self.current_time = self.race_data.total_duration
                  self.is_playing = False
      
      def get_current_positions(self) -> List[Tuple[int, float]]:
          # Return (runner_id, position) for current time
  ```

#### Pygame UI Specifications
- **Files to Create**:
  - `src/visualizer/main_window.py` - Main application window
  - `src/visualizer/track_view.py` - Track visualization component
  - `src/visualizer/control_panel.py` - Playback controls UI

- **Window Layout**:
  - Main window: 800x600 pixels
  - Track area: 700x200 pixels (top section)
  - Control panel: 700x100 pixels (bottom section)
  - Runner information panel: 200x300 pixels (right side)

- **Track Visualization**:
  - Horizontal track: 100 character positions (0-100m)
  - Each runner represented by colored dot '●'
  - Track background: simple ASCII representation
  - Current positions updated in real-time

- **Control Elements**:
  - Play/Pause button (spacebar and clickable)
  - Speed control (1x, 2x, 4x, 0.5x)
  - Reset button (return to start)
  - Progress bar showing race completion
  - Time display (current/total)

- **Visual Specifications**:
  ```python
  # Color scheme
  TRACK_COLOR = (200, 200, 200)  # Light gray
  RUNNER_COLORS = [
      (255, 0, 0),    # Red
      (0, 255, 0),    # Green
      (0, 0, 255),    # Blue
      (255, 255, 0),  # Yellow
      (255, 0, 255),  # Magenta
      (0, 255, 255),  # Cyan
      (128, 128, 0),  # Olive
      (128, 0, 128),  # Purple
  ]
  
  # Font specifications
  FONT_NAME = "Arial"
  FONT_SIZE = 12
  ```

### API Changes
- **No changes to existing DLL API** - uses current interface as-is
- **New Python API for visualization**:
  - `load_race_simulator(dll_path: str) -> bool` - Load DLL
  - `run_simulation(environment: Environment, runners: List[RunnerAttributes]) -> RaceAnimationData`
  - `create_visualization(race_data: RaceAnimationData) -> None`

### Configuration Changes
- **Files to Create**:
  - `config/visualizer_config.py` - Application settings
  - `requirements.txt` - Python dependencies

- **Settings**:
  ```python
  # Configuration parameters
  DLL_PATH = "build/bin/Debug/race_simulator.dll"
  WINDOW_WIDTH = 800
  WINDOW_HEIGHT = 600
  FPS = 30
  DEFAULT_PLAYBACK_SPEED = 1.0
  
  # Environment presets (matching basic_test.cpp)
  ENVIRONMENT_PRESETS = {
      "ideal": {
          "track_type": TrackType.PLASTIC,
          "weather": Weather.SUNNY,
          "wind_dir": WindDirection.TAILWIND,
          "wind_speed": 2.0
      },
      "poor": {
          "track_type": TrackType.DIRT,
          "weather": Weather.HEAVY_RAIN,
          "wind_dir": WindDirection.HEADWIND,
          "wind_speed": 5.0
      },
      "normal": {
          "track_type": TrackType.PLASTIC,
          "weather": Weather.CLOUDY,
          "wind_dir": WindDirection.HEADWIND,
          "wind_speed": 1.0
      }
  }
  ```

- **Dependencies**:
  ```
  pygame==2.5.2
  numpy==1.24.3
  dataclasses-json==0.6.1
  ```

## Implementation Sequence

### Phase 1: DLL Integration Foundation (1-2 days)
1. **File**: `src/visualizer/dll_interface.py`
   - Implement ctypes structure mappings
   - Create DLL loading and error handling
   - Test basic race calculation

2. **File**: `src/visualizer/race_calculator.py`
   - Create wrapper for CalculateRaceResult
   - Implement batch processing for multiple runners
   - Add validation and error reporting

3. **File**: `src/visualizer/data_models.py`
   - Define enhanced data structures
   - Implement serialization for race data
   - Create validation methods

### Phase 2: Race Data Generation (1-2 days)
1. **File**: `src/visualizer/race_data_generator.py`
   - Implement position calculation algorithm
   - Create timing interpolation system
   - Generate race snapshots at 0.1s intervals

2. **File**: `tests/test_race_data_generator.py`
   - Unit tests for position calculations
   - Verify timing accuracy
   - Test edge cases (false starts, skill activations)

### Phase 3: Animation System (2-3 days)
1. **File**: `src/visualizer/animation_engine.py`
   - Core animation loop implementation
   - Time-based position interpolation
   - Playback speed controls

2. **File**: `src/visualizer/track_renderer.py`
   - ASCII track visualization
   - Runner dot rendering
   - Position updates and smooth movement

3. **File**: `src/visualizer/playback_controls.py`
   - Button implementations
   - Keyboard shortcuts (spacebar for play/pause)
   - Progress bar and time display

### Phase 4: UI Integration (1-2 days)
1. **File**: `src/visualizer/main_window.py`
   - Pygame window initialization
   - Component layout and coordination
   - Event handling system

2. **File**: `src/visualizer/control_panel.py`
   - Control button positioning
   - State management for playback
   - User interaction handling

### Phase 5: Testing and Packaging (1-2 days)
1. **File**: `tests/test_integration.py`
   - End-to-end testing
   - Performance testing with 8 runners
   - Memory usage validation

2. **File**: `build.py`
   - PyInstaller configuration
   - Windows executable creation
   - Distribution packaging

## Validation Plan

### Unit Tests
- **test_dll_interface.py**: Verify DLL loading and function calls
- **test_race_data_generator.py**: Validate position calculations
- **test_animation_engine.py**: Test playback timing and controls
- **test_track_renderer.py**: Verify visual rendering accuracy

### Integration Tests
- **test_full_simulation.py**: Run 8-runner competition
- **test_performance.py**: Verify 30 FPS on standard hardware
- **test_memory.py**: Ensure no memory leaks during long races

### Business Logic Verification
- **Accuracy Test**: Compare final positions with DLL results
- **Timing Test**: Verify race duration matches DLL calculation
- **Visual Test**: Manual verification of smooth animation
- **Usability Test**: Verify play/pause responsiveness

### Build Validation
- **Executable Test**: Verify standalone .exe runs without Python
- **DLL Dependency Test**: Ensure race_simulator.dll is properly included
- **Windows Compatibility**: Test on Windows 10/11

## File Structure
```
D:\simc\
├── src/
│   └── visualizer/
│       ├── __init__.py
│       ├── dll_interface.py
│       ├── race_calculator.py
│       ├── data_models.py
│       ├── race_data_generator.py
│       ├── animation_engine.py
│       ├── track_renderer.py
│       ├── playback_controls.py
│       ├── main_window.py
│       └── control_panel.py
├── tests/
│   ├── test_dll_interface.py
│   ├── test_race_data_generator.py
│   ├── test_animation_engine.py
│   ├── test_track_renderer.py
│   └── test_integration.py
├── config/
│   └── visualizer_config.py
├── build/
│   ├── visualizer.exe
│   └── race_simulator.dll
├── requirements.txt
├── build.py
└── README.md
```

## Build and Packaging Instructions

### Development Setup
```bash
# Install dependencies
pip install -r requirements.txt

# Run from source
python -m src.visualizer.main_window
```

### Windows Executable Creation
```bash
# Install PyInstaller
pip install pyinstaller

# Build executable
python build.py

# Output: dist/visualizer/visualizer.exe
```

### Distribution Package
- **Required Files**:
  - `visualizer.exe` (main executable)
  - `race_simulator.dll` (race calculation engine)
  - `README.md` (usage instructions)
- **Target Platform**: Windows 10/11 (x64)
- **Package Size**: ~15MB (including Python runtime)

### Deployment Checklist
- [ ] DLL loading works from executable directory
- [ ] All visual elements render correctly
- [ ] Play/pause controls respond to keyboard/mouse
- [ ] Performance maintains 30 FPS with 8 runners
- [ ] No console window appears in release build
- [ ] Error handling for missing DLL