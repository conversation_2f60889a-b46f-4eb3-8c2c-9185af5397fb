#!/usr/bin/env python3
"""
Entry point for the running race simulation application.

This module provides the main entry point for the race simulation visualization
system. It handles both direct execution and module import scenarios.
"""

import sys
import os

# Add the src directory to the Python path for module imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ui_controller import UIController


def main():
    """
    Main entry point for the application.
    
    This function serves as the primary entry point when running the application
    directly. It delegates to the UIController for all application logic.
    
    Returns:
        int: Exit code (0 for success, non-zero for error)
    """
    try:
        # Create and run the application controller
        controller = UIController()
        exit_code = controller.run()
        return exit_code
        
    except KeyboardInterrupt:
        # Handle Ctrl+C gracefully
        print("\nApplication interrupted by user")
        return 0
    except Exception as e:
        # Handle any unexpected errors
        print(f"Unexpected error: {e}")
        return 1


if __name__ == "__main__":
    # When run as a script
    exit_code = main()
    sys.exit(exit_code)