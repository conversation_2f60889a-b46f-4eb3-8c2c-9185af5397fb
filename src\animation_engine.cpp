#include "animation_engine.h"
#include "console_utils.h"
#include "race_engine.h"
#include <iostream>
#include <algorithm>
#include <chrono>
#include <thread>
#include <iomanip>

AnimationEngine::AnimationEngine() : screenWidth(80), screenHeight(25), trackLength(60), 
    raceDistance(100.0f), targetFPS(30) {

AnimationEngine::~AnimationEngine() {
    shutdown();
}

bool AnimationEngine::initialize(int width, int height) {
    screenWidth = width;
    screenHeight = height;
    initializeRunnerFrames();
    lastFrameTime = std::chrono::steady_clock::now();
    return true;
}

void AnimationEngine::shutdown() {
    // Cleanup if needed
}

void AnimationEngine::setRaceDistance(float distance) {
    raceDistance = distance;
}

void AnimationEngine::setTrackLength(int length) {
    trackLength = length;
}

void AnimationEngine::setTargetFPS(int fps) {
    targetFPS = fps;
}

void AnimationEngine::waitForNextFrame() {
    auto now = std::chrono::steady_clock::now();
    auto targetFrameTime = std::chrono::milliseconds(1000 / targetFPS);
    auto elapsed = now - lastFrameTime;
    
    if (elapsed < targetFrameTime) {
        std::this_thread::sleep_for(targetFrameTime - elapsed);
    }
    
    lastFrameTime = std::chrono::steady_clock::now();
}

void AnimationEngine::updateAnimation(const std::vector<RunnerInfo>& runners) {
    // Update animation state based on runner information
    renderFrame();
}

void AnimationEngine::renderFrame() {
    clearScreen();
    drawTrack();
}

void AnimationEngine::clearScreen() {
    system("cls");
}

void AnimationEngine::initializeRunnerFrames() {
    runnerFrames = {
        " o /|\\ / \\",
        " O /|\\ / \\",
        " o -|- / \\",
        " O -|- / \\"
    };
}

void AnimationEngine::drawTrack() {
    const int trackStartY = 5;
    const int trackHeight = 4;
    const int trackStartX = 5;
    
    // Draw track boundaries
    for (int lane = 0; lane < 4; ++lane) {
        int laneY = trackStartY + lane * trackHeight;
        
        // Draw lane number
        std::cout << "\033[" << laneY << ";1H" << "Lane " << (lane + 1) << ":";
        
        // Draw track lines
        for (int x = trackStartX; x < trackStartX + trackLength; ++x) {
            std::cout << "\033[" << laneY << ";" << x << "H" << (x == trackStartX ? "|" : "-");
            std::cout << "\033[" << (laneY + trackHeight - 1) << ";" << x << "H" << (x == trackStartX ? "|" : "-");
        }
        
        // Draw finish line
        std::cout << "\033[" << laneY << ";" << (trackStartX + trackLength) << "H" << "|█";
        std::cout << "\033[" << (laneY + 1) << ";" << (trackStartX + trackLength) << "H" << "|█";
        std::cout << "\033[" << (laneY + 2) << ";" << (trackStartX + trackLength) << "H" << "|█";
    }
}

void AnimationEngine::drawRunners(const std::vector<RunnerInfo>& runners) {
    const int trackStartY = 6;
    const int trackStartX = 6;
    
    for (size_t i = 0; i < runners.size() && i < 4; ++i) {
        int posX = getPositionX(runners[i].distance);
        int posY = trackStartY + static_cast<int>(i) * 4;
        
        drawRunner(static_cast<int>(i), posX, runners[i].name);
    }
}

void AnimationEngine::drawRunner(int lane, int position, const std::string& name) {
    const int trackStartY = 6;
    const int nameOffsetX = 1;
    const int spriteOffsetY = 1;
    
    int posY = trackStartY + lane * 4;
    
    // Draw runner name
    std::cout << "\033[" << posY << ";" << (position - name.length() - nameOffsetX) << "H" << name.substr(0, 8);
    
    // Draw runner sprite
    std::string sprite = getRunnerSprite(static_cast<float>(position) / trackLength);
    std::cout << "\033[" << (posY + spriteOffsetY) << ";" << position << "H" << sprite;
}

void AnimationEngine::drawFinishLine() {
    const int trackStartY = 5;
    const int trackStartX = 5 + trackLength;
    
    for (int lane = 0; lane < 4; ++lane) {
        int laneY = trackStartY + lane * 4;
        std::cout << "\033[" << laneY << ";" << trackStartX << "H" << "|FINISH";
    }
}

void AnimationEngine::drawStartLine() {
    const int trackStartY = 5;
    const int trackStartX = 5;
    
    for (int lane = 0; lane < 4; ++lane) {
        int laneY = trackStartY + lane * 4;
        std::cout << "\033[" << laneY << ";" << trackStartX << "H" << "START|";
    }
}

void AnimationEngine::drawHUD(const std::vector<RunnerInfo>& runners) {
    // Draw title
    std::cout << "\033[1;25H" << "ASCII RUNNING RACE SIMULATOR";
    std::cout << "\033[2;25H" << "=============================";
    
    // Draw runner status
    for (size_t i = 0; i < runners.size() && i < 4; ++i) {
        int statusY = 20 + static_cast<int>(i);
        float progress = (runners[i].distance / raceDistance) * 100.0f;
        
        std::cout << "\033[" << statusY << ";5H" 
                  << runners[i].name.substr(0, 8) << ": " 
                  << std::fixed << std::setprecision(1) << progress << "%";
        
        if (runners[i].finished) {
            std::cout << " (Finished: " << runners[i].finishTime << "s)";
        }
    }
    
    // Draw controls
    std::cout << "\033[22;5H" << "Controls: SPACE = Start/Pause, R = Reset, Q = Quit";
}

int AnimationEngine::getLaneY(int lane) const {
    return 6 + lane * 4;
}

int AnimationEngine::getPositionX(float distance) const {
    const int trackStartX = 6;
    float progress = std::min(1.0f, distance / raceDistance);
    return trackStartX + static_cast<int>(progress * (trackLength - 10));
}

std::string AnimationEngine::getRunnerSprite(float progress) const {
    static int frameCounter = 0;
    frameCounter++;
    
    if (progress >= 1.0f) {
        return "\o/"; // Finished pose
    }
    
    // Running animation frames
    const std::string frames[4] = {
        " o-",
        " O-",
        " o=",
        " O="
    };
    
    return frames[frameCounter % 4];
}

void AnimationEngine::drawText(int x, int y, const std::string& text) {
    std::cout << "\033[" << y << ";" << x << "H" << text;
}

void AnimationEngine::drawHorizontalLine(int y, int x1, int x2, char c) {
    for (int x = x1; x <= x2; ++x) {
        std::cout << "\033[" << y << ";" << x << "H" << c;
    }
}

void AnimationEngine::drawVerticalLine(int x, int y1, int y2, char c) {
    for (int y = y1; y <= y2; ++y) {
        std::cout << "\033[" << y << ";" << x << "H" << c;
    }
}