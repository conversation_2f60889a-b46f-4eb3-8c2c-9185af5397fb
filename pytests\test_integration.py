"""
Integration Tests

Comprehensive end-to-end tests for complete user workflow including:
- Complete race simulation workflow
- DLL integration to visualization
- Configuration to execution
- User interaction to results
- Real-world usage scenarios
"""

import os
import sys
import unittest
import tempfile
import json
import shutil
from unittest.mock import patch, MagicMock

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from ui_controller import UIController
from data_loader import DataLoader
from race_simulator_types import create_sample_runners


class TestCompleteWorkflow(unittest.TestCase):
    """Test suite for complete end-to-end workflows."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.sample_runners = create_sample_runners()
        
    def tearDown(self):
        """Clean up test fixtures."""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_basic_workflow_dll_available(self):
        """Test complete workflow with DLL available."""
        # Create test configuration
        config = {
            "window": {"width": 800, "height": 600, "fps": 30},
            "race": {"default_distance": 100.0, "dll_path": None},
            "ui": {"show_fps": False, "auto_play": False}
        }
        
        config_path = os.path.join(self.temp_dir, "config.json")
        with open(config_path, 'w') as f:
            json.dump(config, f)
        
        # Create custom runner file
        runner_file = os.path.join(self.temp_dir, "custom_runners.json")
        custom_runners = [
            {"id": 1, "max_speed": 12.0, "acceleration": 3.0, "endurance": 0.90},
            {"id": 2, "max_speed": 11.5, "acceleration": 3.2, "endurance": 0.85},
            {"id": 3, "max_speed": 12.5, "acceleration": 2.8, "endurance": 0.88}
        ]
        with open(runner_file, 'w') as f:
            json.dump(custom_runners, f)
        
        # Test complete headless workflow
        controller = UIController()
        
        # Mock DLL to avoid actual dependency
        with patch('builtins.print'):
            with patch('ui_controller.DataLoader'):
                args = [
                    '--runners', runner_file,
                    '--distance', '150',
                    '--export', os.path.join(self.temp_dir, 'results.json'),
                    '--no-gui'
                ]
                exit_code = controller.run(args)
        
        self.assertEqual(exit_code, 0)
        
        # Verify results were exported
        results_file = os.path.join(self.temp_dir, 'results.json')
        self.assertTrue(os.path.exists(results_file))
        
        with open(results_file, 'r') as f:
            results = json.load(f)
            
        self.assertIn('race_info', results)
        self.assertIn('results', results)
        self.assertIn('frames', results)
        self.assertEqual(results['race_info']['distance'], 150.0)
        self.assertEqual(results['race_info']['total_runners'], 3)
    
    def test_workflow_no_dll_fallback(self):
        """Test complete workflow with DLL fallback."""
        # Test with no DLL available - should use fallback
        controller = UIController()
        
        with patch('builtins.print'):
            with patch('ui_controller.DataLoader'):
                args = ['--sample', '--distance', '200', '--no-gui']
                exit_code = controller.run(args)
        
        self.assertEqual(exit_code, 0)
    
    def test_workflow_sample_data_only(self):
        """Test workflow using only sample data."""
        controller = UIController()
        
        with patch('builtins.print'):
            with patch('ui_controller.DataLoader'):
                args = ['--sample', '--distance', '100', '--no-gui']
                exit_code = controller.run(args)
        
        self.assertEqual(exit_code, 0)
    
    def test_workflow_with_export(self):
        """Test workflow with results export."""
        export_path = os.path.join(self.temp_dir, "race_results.json")
        
        controller = UIController()
        
        with patch('builtins.print'):
            with patch('ui_controller.DataLoader'):
                args = ['--sample', '--distance', '100', '--export', export_path, '--no-gui']
                exit_code = controller.run(args)
        
        self.assertEqual(exit_code, 0)
        self.assertTrue(os.path.exists(export_path))
        
        # Verify export content
        with open(export_path, 'r') as f:
            export_data = json.load(f)
        
        self.assertIn('race_info', export_data)
        self.assertIn('results', export_data)
        self.assertIn('frames', export_data)
    
    def test_workflow_different_distances(self):
        """Test workflow with different race distances."""
        test_distances = [50.0, 100.0, 200.0, 400.0, 800.0]
        
        for distance in test_distances:
            with self.subTest(distance=distance):
                controller = UIController()
                
                with patch('builtins.print'):
                    with patch('ui_controller.DataLoader'):
                        args = ['--sample', '--distance', str(distance), '--no-gui']
                        exit_code = controller.run(args)
                
                self.assertEqual(exit_code, 0)
    
    def test_workflow_different_runner_counts(self):
        """Test workflow with different runner counts."""
        test_counts = [1, 3, 5, 8, 10]
        
        for count in test_counts:
            with self.subTest(runners=count):
                runners = [
                    {"id": i+1, "max_speed": 10.0 + i*0.5, 
                     "acceleration": 2.5 + i*0.1, "endurance": 0.80 + i*0.02}
                    for i in range(count)
                ]
                
                runner_file = os.path.join(self.temp_dir, f"runners_{count}.json")
                with open(runner_file, 'w') as f:
                    json.dump(runners, f)
                
                controller = UIController()
                
                with patch('builtins.print'):
                    with patch('ui_controller.DataLoader'):
                        args = ['--runners', runner_file, '--no-gui']
                        exit_code = controller.run(args)
                
                self.assertEqual(exit_code, 0)


class TestRealWorldScenarios(unittest.TestCase):
    """Test suite for real-world usage scenarios."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """Clean up test fixtures."""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_first_time_user_workflow(self):
        """Test workflow for first-time user."""
        # Simulate first-time user scenario
        controller = UIController()
        
        # User runs with --create-sample to get started
        sample_file = os.path.join(self.temp_dir, "my_runners.json")
        controller.create_sample_runner_file(sample_file)
        
        self.assertTrue(os.path.exists(sample_file))
        
        # User then runs with their custom file
        with open(sample_file, 'r') as f:
            sample_data = json.load(f)
        
        # Modify sample data
        sample_data['runners'][0]['max_speed'] = 13.5
        sample_data['race_distance'] = 150.0
        
        with open(sample_file, 'w') as f:
            json.dump(sample_data, f)
        
        # Test complete workflow
        with patch('builtins.print'):
            with patch('ui_controller.DataLoader'):
                args = ['--runners', sample_file, '--export', 
                       os.path.join(self.temp_dir, 'my_results.json'), '--no-gui']
                exit_code = controller.run(args)
        
        self.assertEqual(exit_code, 0)
    
    def test_research_scenario_workflow(self):
        """Test workflow for research/analysis scenario."""
        # Create research dataset
        research_runners = []
        for i in range(20):
            research_runners.append({
                "id": i+1,
                "max_speed": 10.0 + (i % 5) * 0.8,
                "acceleration": 2.0 + (i % 4) * 0.5,
                "endurance": 0.75 + (i % 3) * 0.1
            })
        
        research_file = os.path.join(self.temp_dir, "research_runners.json")
        with open(research_file, 'w') as f:
            json.dump({
                "description": "Research dataset for performance analysis",
                "race_distance": 400.0,
                "runners": research_runners
            }, f, indent=2)
        
        # Test research workflow
        controller = UIController()
        
        with patch('builtins.print'):
            with patch('ui_controller.DataLoader'):
                args = ['--runners', research_file, '--distance', '400', 
                       '--export', os.path.join(self.temp_dir, 'research_results.json'), '--no-gui']
                exit_code = controller.run(args)
        
        self.assertEqual(exit_code, 0)
        
        # Verify research results
        results_file = os.path.join(self.temp_dir, 'research_results.json')
        with open(results_file, 'r') as f:
            results = json.load(f)
        
        self.assertEqual(results['race_info']['total_runners'], 20)
        self.assertEqual(results['race_info']['distance'], 400.0)
    
    def test_coaching_scenario_workflow(self):
        """Test workflow for coaching/training scenario."""
        # Create training dataset with different skill levels
        training_runners = [
            {"id": 1, "max_speed": 8.5, "acceleration": 2.0, "endurance": 0.70},  # Beginner
            {"id": 2, "max_speed": 10.0, "acceleration": 2.5, "endurance": 0.80},  # Intermediate
            {"id": 3, "max_speed": 11.5, "acceleration": 3.0, "endurance": 0.90},  # Advanced
            {"id": 4, "max_speed": 9.0, "acceleration": 2.2, "endurance": 0.75},   # Beginner
            {"id": 5, "max_speed": 12.0, "acceleration": 3.2, "endurance": 0.95},  # Elite
        ]
        
        training_file = os.path.join(self.temp_dir, "training_runners.json")
        with open(training_file, 'w') as f:
            json.dump(training_runners, f, indent=2)
        
        # Test training workflow with multiple distances
        distances = [100.0, 200.0, 400.0]  # Sprint, mid-distance, distance
        
        for distance in distances:
            controller = UIController()
            
            with patch('builtins.print'):
                with patch('ui_controller.DataLoader'):
                    args = ['--runners', training_file, '--distance', str(distance),
                           '--export', os.path.join(self.temp_dir, f'training_{distance}m.json'), '--no-gui']
                    exit_code = controller.run(args)
            
            self.assertEqual(exit_code, 0)


class TestDLLIntegrationWorkflow(unittest.TestCase):
    """Test suite for DLL integration workflows."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """Clean up test fixtures."""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_dll_loading_workflow(self):
        """Test complete DLL loading and usage workflow."""
        # Test with mock DLL
        with patch('race_simulator_types.RaceSimulatorAPI') as mock_api_class:
            mock_api = MagicMock()
            mock_api_class.return_value = mock_api
            
            # Setup mock return value
            mock_api.calculate_race.return_value = {
                'total_runners': 5,
                'race_distance': 100.0,
                'results': [
                    {'runner_id': 1, 'finish_time': 10.5, 'final_position': 1, 'did_finish': True},
                    {'runner_id': 2, 'finish_time': 11.0, 'final_position': 2, 'did_finish': True},
                ],
                'snapshots': [
                    {'time': 0.0, 'runner_id': 1, 'position': 0.0, 'current_speed': 0.0, 'current_acceleration': 2.8},
                    {'time': 0.033, 'runner_id': 1, 'position': 1.5, 'current_speed': 3.0, 'current_acceleration': 2.5},
                ]
            }
            
            # Test workflow
            loader = DataLoader("fake_dll.dll")
            race_data = loader.calculate_race(create_sample_runners(), 100.0)
            
            self.assertIsNotNone(race_data)
            self.assertEqual(race_data['race_info']['total_runners'], 5)
            mock_api.calculate_race.assert_called_once()
    
    def test_dll_error_recovery_workflow(self):
        """Test recovery when DLL fails."""
        # Test DLL failure and fallback
        with patch('race_simulator_types.RaceSimulatorAPI') as mock_api_class:
            mock_api_class.side_effect = FileNotFoundError("DLL not found")
            
            # Should use fallback
            loader = DataLoader("missing_dll.dll")
            race_data = loader.calculate_race(create_sample_runners(), 100.0)
            
            # Should get fallback data
            self.assertIsNotNone(race_data)
            self.assertIn('race_info', race_data)
            self.assertIn('results', race_data)
            self.assertIn('frames', race_data)


class TestConfigurationWorkflow(unittest.TestCase):
    """Test suite for configuration-driven workflows."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """Clean up test fixtures."""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_config_override_workflow(self):
        """Test configuration override workflow."""
        # Create custom configuration
        custom_config = {
            "window": {"width": 1024, "height": 768, "fps": 60},
            "race": {"default_distance": 200.0, "dll_path": "/custom/path.dll"},
            "ui": {"show_fps": True, "auto_play": True}
        }
        
        config_file = os.path.join(self.temp_dir, "custom_config.json")
        with open(config_file, 'w') as f:
            json.dump(custom_config, f)
        
        # Test configuration loading
        controller = UIController()
        
        with patch('builtins.print'):
            with patch('ui_controller.DataLoader'):
                with patch('os.path.exists', return_value=True):
                    # Mock config loading
                    with patch.object(controller, 'config', custom_config):
                        args = ['--sample', '--no-gui']
                        exit_code = controller.run(args)
        
        self.assertEqual(exit_code, 0)
    
    def test_command_line_override_workflow(self):
        """Test command line argument override workflow."""
        controller = UIController()
        
        with patch('builtins.print'):
            with patch('ui_controller.DataLoader'):
                # Test overriding config values via command line
                args = [
                    '--sample',
                    '--distance', '250',
                    '--dll', '/override/path.dll',
                    '--export', os.path.join(self.temp_dir, 'override_results.json'),
                    '--no-gui'
                ]
                exit_code = controller.run(args)
        
        self.assertEqual(exit_code, 0)


class TestUserInteractionScenarios(unittest.TestCase):
    """Test suite for user interaction scenarios."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """Clean up test fixtures."""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_help_workflow(self):
        """Test help and documentation workflow."""
        controller = UIController()
        
        # Test help argument processing
        try:
            with self.assertRaises(SystemExit):
                controller.parse_arguments(['--help'])
        except SystemExit as e:
            # Help should exit with code 0
            self.assertIn(e.code, [0, None])
    
    def test_sample_creation_workflow(self):
        """Test sample file creation workflow."""
        controller = UIController()
        
        sample_file = os.path.join(self.temp_dir, "created_sample.json")
        controller.create_sample_runner_file(sample_file)
        
        self.assertTrue(os.path.exists(sample_file))
        
        # Verify created sample
        with open(sample_file, 'r') as f:
            sample_data = json.load(f)
        
        self.assertIn('runners', sample_data)
        self.assertIn('race_distance', sample_data)
        self.assertIn('description', sample_data)
        
        # Verify runners have correct structure
        for runner in sample_data['runners']:
            self.assertIn('id', runner)
            self.assertIn('max_speed', runner)
            self.assertIn('acceleration', runner)
            self.assertIn('endurance', runner)


if __name__ == '__main__':
    unittest.main()