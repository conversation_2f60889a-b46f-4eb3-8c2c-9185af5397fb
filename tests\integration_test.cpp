#include <iostream>
#include <vector>
#include <chrono>
#include <thread>
#include <memory>
#include "race_simulator.h"
#include "race_engine.h"
#include "ascii_simulator.h"
#include "console_utils.h"

class IntegrationTester {
public:
    static bool testDLLIntegration() {
        std::cout << "Testing DLL Integration..." << std::endl;
        
        try {
            // Test basic DLL functionality
            RunnerAttributes runner;
            runner.speed = 150;
            runner.power = 140;
            runner.intelligence = 130;
            
            // Test skill system functions
            bool triggers[] = {true, true, true};
            float baseTimes[] = {1.0f, 2.0f, 3.0f, 2.5f, 1.5f};
            float segmentDistances[] = {0.0f, 20.0f, 40.0f, 70.0f, 100.0f};
            
            float totalImpact = RaceSimulator::CalculateSkillImpact(&runner, triggers, baseTimes, segmentDistances);
            
            if (totalImpact != 0.0f) {
                std::cout << "  DLL Integration: PASS" << std::endl;
                return true;
            } else {
                std::cout << "  DLL Integration: FAIL - No impact calculated" << std::endl;
                return false;
            }
            
        } catch (const std::exception& e) {
            std::cout << "  DLL Integration: FAIL - " << e.what() << std::endl;
            return false;
        }
    }
    
    static bool testRaceSimulation() {
        std::cout << "Testing Race Simulation..." << std::endl;
        
        try {
            RaceEngine engine;
            
            // Create test runners
            std::vector<RunnerAttributes> runners;
            
            RunnerAttributes runner1;
            runner1.speed = 180;
            runner1.power = 160;
            runner1.intelligence = 150;
            
            RunnerAttributes runner2;
            runner2.speed = 170;
            runner2.power = 170;
            runner2.intelligence = 160;
            
            runners.push_back(runner1);
            runners.push_back(runner2);
            
            // Test race initialization
            if (!engine.initializeRace(runners, 100.0f)) {
                std::cout << "  Race Simulation: FAIL - Initialization failed" << std::endl;
                return false;
            }
            
            // Test race completion
            engine.startRace();
            
            // Wait a bit for simulation
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
            
            engine.stopRace();
            
            std::cout << "  Race Simulation: PASS" << std::endl;
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "  Race Simulation: FAIL - " << e.what() << std::endl;
            return false;
        }
    }
    
    static bool testASCIIRendering() {
        std::cout << "Testing ASCII Rendering..." << std::endl;
        
        try {
            // Test console capabilities
            if (!ConsoleUtils::initializeConsole()) {
                std::cout << "  ASCII Rendering: FAIL - Console initialization failed" << std::endl;
                return false;
            }
            
            // Test screen dimensions
            int width, height;
            ConsoleUtils::getConsoleSize(width, height);
            
            if (width < 80 || height < 24) {
                std::cout << "  ASCII Rendering: WARNING - Console too small (" << width << "x" << height << ")" << std::endl;
            } else {
                std::cout << "  ASCII Rendering: Console size " << width << "x" << height << std::endl;
            }
            
            // Test basic rendering
            ConsoleUtils::clearScreen();
            ConsoleUtils::setCursorPosition(0, 0);
            std::cout << "ASCII Racing Test - OK" << std::endl;
            
            std::this_thread::sleep_for(std::chrono::milliseconds(500));
            ConsoleUtils::clearScreen();
            
            std::cout << "  ASCII Rendering: PASS" << std::endl;
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "  ASCII Rendering: FAIL - " << e.what() << std::endl;
            return false;
        }
    }
    
    static bool testEndToEnd() {
        std::cout << "Testing End-to-End Race..." << std::endl;
        
        try {
            AsciiSimulator simulator;
            
            if (!simulator.initialize()) {
                std::cout << "  End-to-End: FAIL - Simulator initialization" << std::endl;
                return false;
            }
            
            // Run a quick test race
            simulator.setTestMode(true);
            simulator.setRaceDuration(2.0f); // 2 second test race
            
            simulator.run();
            simulator.shutdown();
            
            std::cout << "  End-to-End: PASS" << std::endl;
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "  End-to-End: FAIL - " << e.what() << std::endl;
            return false;
        }
    }
    
    static bool testPerformance() {
        std::cout << "Testing Performance..." << std::endl;
        
        try {
            auto start = std::chrono::high_resolution_clock::now();
            
            // Run 1000 skill calculations
            RunnerAttributes runner;
            runner.speed = 150;
            runner.power = 140;
            runner.intelligence = 130;
            
            bool triggers[] = {true, true, true};
            float baseTimes[] = {1.0f, 2.0f, 3.0f, 2.5f, 1.5f};
            float segmentDistances[] = {0.0f, 20.0f, 40.0f, 70.0f, 100.0f};
            
            for (int i = 0; i < 1000; i++) {
                RaceSimulator::CalculateSkillImpact(&runner, triggers, baseTimes, segmentDistances);
            }
            
            auto end = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();
            
            float avgTime = duration / 1000.0f;
            
            if (avgTime < 0.1f) { // Less than 0.1ms per calculation
                std::cout << "  Performance: PASS (" << avgTime << " ms/calc)" << std::endl;
                return true;
            } else {
                std::cout << "  Performance: WARNING (" << avgTime << " ms/calc)" << std::endl;
                return true; // Still pass, just slower
            }
            
        } catch (const std::exception& e) {
            std::cout << "  Performance: FAIL - " << e.what() << std::endl;
            return false;
        }
    }
};

int main(int argc, char* argv[]) {
    std::cout << "========================================" << std::endl;
    std::cout << "ASCII Running Simulation Integration Tests" << std::endl;
    std::cout << "========================================" << std::endl;
    std::cout << std::endl;
    
    bool allPassed = true;
    
    // Run all integration tests
    allPassed &= IntegrationTester::testDLLIntegration();
    allPassed &= IntegrationTester::testRaceSimulation();
    allPassed &= IntegrationTester::testASCIIRendering();
    allPassed &= IntegrationTester::testPerformance();
    
    // Skip end-to-end in test mode
    if (argc > 1 && std::string(argv[1]) == "--quick") {
        std::cout << "\nSkipping end-to-end test (quick mode)" << std::endl;
    } else {
        allPassed &= IntegrationTester::testEndToEnd();
    }
    
    std::cout << std::endl;
    std::cout << "========================================" << std::endl;
    
    if (allPassed) {
        std::cout << "ALL INTEGRATION TESTS PASSED! ✅" << std::endl;
        return 0;
    } else {
        std::cout << "SOME TESTS FAILED! ❌" << std::endl;
        return 1;
    }
}